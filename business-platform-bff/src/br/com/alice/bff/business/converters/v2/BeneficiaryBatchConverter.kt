package br.com.alice.bff.business.converters.v2

import br.com.alice.bff.business.models.v2.BeneficiaryBatchRequest
import br.com.alice.bff.business.models.v2.BeneficiaryBatchValidationErrorItemResponse
import br.com.alice.bff.business.models.v2.BeneficiaryBatchValidationErrorResponse
import br.com.alice.bff.business.models.v2.BeneficiaryBatchValidationResponse
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation

fun BeneficiaryBatchRequest.toHrBeneficiaryBatchTransport() = BeneficiaryBatchTransport(
    items = this.items.map {
        BeneficiaryBatchItemTransport(
            index = it.index,
            nationalId = it.nationalId,
            fullName = it.fullName,
            cnpj = it.cnpj,
            insuranceCnpj = it.insuranceCnpj,
            sex = it.sex,
            dateOfBirth = it.dateOfBirth,
            mothersName = it.mothersName,
            email = it.email,
            phoneNumber = it.phoneNumber,
            addressPostalCode = it.addressPostalCode,
            addressNumber = it.addressNumber,
            addressComplement = it.addressComplement,
            productTitle = it.productTitle,
            activatedAt = it.activatedAt,
            beneficiaryContractType = it.beneficiaryContractType,
            hiredAt = it.hiredAt,
            parentNationalId = it.parentNationalId,
            parentBeneficiaryRelationType = it.parentBeneficiaryRelationType,
            relationExceeds30Days = it.relationExceeds30Days,
            ownership = it.ownership,
        )
    }
)

fun BeneficiaryBatchValidation.toBeneficiaryBatchValidationResponse() = BeneficiaryBatchValidationResponse(
    success = this.success,
    errors = this.errors.map { error ->
        BeneficiaryBatchValidationErrorResponse(
            index = error.index,
            error = error.error.map { item ->
                BeneficiaryBatchValidationErrorItemResponse(
                    field = item.field,
                    message = item.message,
                )
            }
        )
    }
)
