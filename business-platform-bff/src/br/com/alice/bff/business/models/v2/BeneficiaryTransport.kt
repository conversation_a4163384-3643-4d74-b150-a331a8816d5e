package br.com.alice.bff.business.models.v2

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.BeneficiaryCancelationReason
import br.com.alice.data.layer.models.BeneficiaryViewInsuranceStatus
import br.com.alice.data.layer.models.BeneficiaryViewOnboardingStatus
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import java.time.LocalDateTime
import java.util.UUID

data class BeneficiaryListResponse(
    val id: UUID,
    val companyId: UUID,
    val nationalId: String,
    val fullName: String,
    val parentFullName: String?,
    val email: String,
    val type: FriendlyEnumResponse<BeneficiaryType>,
    val activatedAt: LocalDateTime? = null,
    val canceledAt: LocalDateTime? = null,
    val productId: UUID,
    val product: String? = null,
    val productPreviousName: String? = null,
    val insuranceStatus: FriendlyEnumResponse<BeneficiaryViewInsuranceStatus>? = null,
    val beneficiaryOnboardingCurrentStep: FriendlyEnumResponse<BeneficiaryTimelineStep>? = null,
    val scheduledProductChange: BeneficiaryScheduledProductChange? = null,
)

data class BeneficiaryStatusQuantitiesResponse(
    val total: Int,
    val insuranceStatus: FriendlyEnumResponse<BeneficiaryViewInsuranceStatus>? = null,
)

data class BeneficiaryDetailResponse(
    val id: UUID,
    val personId: UUID,
    val fullName: String,
    val nationalId: String,
    val email: String,
    val birthDate: LocalDateTime?,
    val phoneNumber: String?,
    val type: FriendlyEnumResponse<BeneficiaryType>,
    val insuranceStatus: FriendlyEnumResponse<InsuranceStatus>? = null,
    val plan: ProductSummary,
    val address: Address,
    val dependents: List<BeneficiarySummaryResponse>,
    val holder: BeneficiarySummaryResponse? = null,
    val activatedAt: LocalDateTime,
    val canceledAt: LocalDateTime? = null,
    val canceledReason: BeneficiaryCancelationReason? = null,
    val canceledDescription: String? = null,
    val hasContributed: Boolean? = null,
    val onboardingStatus: FriendlyEnumResponse<BeneficiaryViewOnboardingStatus>?,
    val scheduledProductChange: BeneficiaryScheduledProductChange? = null,
    val timeline: List<BeneficiaryTimeline>,
    val subContractId: UUID? = null
)

data class BeneficiaryScheduledProductChange(
    val id: UUID,
    val title: String,
    val applyAt: LocalDateTime?,
)

data class BeneficiarySummaryResponse(
    val id: UUID,
    val personId: UUID,
    val fullName: String,
    val nationalId: String,
    val insuranceStatus: FriendlyEnumResponse<InsuranceStatus>? = null,
)

data class BeneficiaryProductChangeRequest(
    val productId: UUID,
)

data class BeneficiaryProductChangeResponse(
    val scheduleId: UUID,
    val beneficiaryId: UUID,
    val productId: UUID,
    val applyAt: LocalDateTime,
)

enum class InsuranceStatus(val description: String) {
    ACTIVE("Ativo"),
    ACTIVE_WITH_ISSUES("Ativo com pendências"),
    PENDING_ACTIVATION("Ativação dentro do prazo"),
    ACTIVATING("Em ativação"),
    LATE_ACTIVATION("Ativação em atraso "),
    CANCELED("Cancelado"),
    CANCELING("Cancelamento agendado"),
}

enum class TimelineItemStatus {
    PENDING,
    CURRENT,
    DONE,
}

enum class BeneficiaryTimelineStep(val description: String, val order: Int) {
    APP_ACCESS("Acessar o app", 1),
    ANSWER_HEALTH_DECLARATION("Responder a declaração de saúde", 2),
    SCHEDULE_VIDEO_CALL("Agendar videochamada", 3),
    ATTEND_VIDEO_CALL("Realizar videochamada", 4),
    AWAIT_GRACE_ANALYSIS("Aguardar análise de carência", 5),
    SIGN_GRACE_TERM("Assinar termo de carência", 6),
    FINISH_APP_ACCESS("Finalizar cadastro no app", 7),
    AWAIT_ACTIVATION_DATE("Aguardar data de ativação", 8),
    ACTIVE_PLAN("Plano ativo", 9),
}

data class BeneficiaryTimeline(
    val step: BeneficiaryTimelineStep,
    val description: String,
    val status: TimelineItemStatus,
    val doneAt: LocalDateTime? = null,
)

data class BeneficiaryIsCancelableResponse(
    val canCancel: Boolean
)

data class ParentBeneficiaryRelationTypeOption(
    val value: String,
    val label: String
)

data class RelationTypeResponse(
    val options: List<ParentBeneficiaryRelationTypeOption>
)

data class BeneficiaryDependentRequest(
    val firstName: String,
    val lastName: String,
    val mothersName: String,
    val nationalId: String,
    val email: String,
    val sex: Sex,
    val birthDate: LocalDateTime,
    val phoneNumber: String,
    val activatedAt: LocalDateTime,
    val address: Address,
    val productId: UUID,
    val parentBeneficiary: UUID,
    val parentBeneficiaryRelationType: ParentBeneficiaryRelationType,
    val parentBeneficiaryRelatedAt: LocalDateTime,
    val subcontractId: UUID,
    val cnpj: String?,
)

data class BeneficiaryBatchRequest(
    val items: List<BeneficiaryBatchItemRequest>,
)

data class BeneficiaryBatchItemRequest(
    val index: Int,
    val nationalId: String? = null,
    val fullName: String? = null,
    val cnpj: String? = null,
    val insuranceCnpj: String? = null,
    val sex: String? = null,
    val dateOfBirth: String? = null,
    val mothersName: String? = null,
    val email: String? = null,
    val phoneNumber: String? = null,
    val addressPostalCode: String? = null,
    val addressNumber: String? = null,
    val addressComplement: String? = null,
    val productTitle: String? = null,
    val activatedAt: String? = null,
    val beneficiaryContractType: String? = null,
    val hiredAt: String? = null,
    val parentNationalId: String? = null,
    val parentBeneficiaryRelationType: String? = null,
    val relationExceeds30Days: String? = null,
    val ownership: String? = null,
)

data class BeneficiaryBatchValidationResponse(
    val success: List<Int> = emptyList(),
    val errors: List<BeneficiaryBatchValidationErrorResponse> = emptyList(),
)

data class BeneficiaryBatchValidationErrorResponse(
    val index: Int,
    val error: List<BeneficiaryBatchValidationErrorItemResponse>,
)

data class BeneficiaryBatchValidationErrorItemResponse(
    val field: String,
    val message: String,
)
