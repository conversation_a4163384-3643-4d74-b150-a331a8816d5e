package br.com.alice.appointment.controller

import br.com.alice.appointment.client.TimelineService
import br.com.alice.common.Response
import br.com.alice.common.asyncLayer
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.toResponse
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.APPOINTMENT_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.TimelineReferenceModel
import br.com.alice.data.layer.models.TimelineType
import br.com.alice.data.layer.services.TimelineDataService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger

class BackfillTimelineController(
    private val timelineDataService: TimelineDataService,
    private val timelineService: TimelineService,
    private val healthProfessionalService: HealthProfessionalService
) : Controller() {

    suspend fun changeType(request: TimelineChangeTypeRequest): Response = withEnvironment {
        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        timelineDataService.find { where { this.referencedModelId.inList(request.timelineIds) and this.type.eq(request.oldType) } }
            .pmapEach { timeline ->
                runCatching {
                    timelineDataService.update(timeline.copy(type = request.newType)).get()
                    successCount.incrementAndGet()
                }.onFailure {
                    errorsCount.incrementAndGet()
                    errors["timeline ${timeline.id} with error"] = it.message ?: "Unknown error"
                }
            }

        InternalFeatureResponse(
            successCount = successCount.get(),
            errorsCount = errorsCount.get(),
            additionalInfo = errors
        ).toResponse()
    }

    suspend fun addSpecialtyToTimeline(request: AddSpecialtyToTimelineRequest): Response = withEnvironment {
        val successCount = AtomicInteger(0)
        val errorCount = AtomicInteger(0)
        val errorList = mutableListOf<String>()
        val withoutStaffCount = AtomicInteger(0)
        val withoutSpecialtyCount = AtomicInteger(0)

        timelineDataService.find {
            where {
                this.specialtyId.isNull() and
                        this.referencedModelClass.eq(TimelineReferenceModel.APPOINTMENT) and
                        this.referencedModelDate.greaterEq(LocalDateTime.now().minusYears(2))
            }
                .limit { request.limit }
                .offset { request.offset }
                .orderBy { this.referencedModelDate }
                .sortOrder { desc }
        }.pmapEach { timeline ->
            if (timeline.staffId == null) {
                withoutStaffCount.incrementAndGet()
                return@pmapEach false.success()
            }
            val specialtyId = getStaffSpecialtyId(timeline.staffId!!).fold({ it }, {
                logger.error("error to get health professional specialty", it)
                errorCount.incrementAndGet()
                errorList.add(it.message ?: "error")
                return@pmapEach false.success()
            })
            if (specialtyId == null) {
                withoutSpecialtyCount.incrementAndGet()
                return@pmapEach false.success()
            }
            timelineService.updated(timeline.copy(specialtyId = specialtyId))
                .then { successCount.incrementAndGet() }
                .thenError {
                    logger.error("error to update timeline", it)
                    errorCount.incrementAndGet()
                    errorList.add(it.message ?: "error")
                }
        }
        AddSpecialtyToTimelineResponse(
            successCount.get(),
            errorCount.get(),
            withoutStaffCount.get(),
            withoutSpecialtyCount.get(),
            errorList
        ).toResponse()
    }


    private suspend fun getStaffSpecialtyId(staffId: UUID) =
        healthProfessionalService.findByStaffId(staffId).map { it.specialtyId }

    private suspend fun withEnvironment(function: suspend () -> Response) =
        withRootServicePolicy(APPOINTMENT_ROOT_SERVICE_NAME) {
            withUnauthenticatedTokenWithKey(APPOINTMENT_ROOT_SERVICE_NAME) {
                asyncLayer {
                    function()
                }
            }
        }
}

data class TimelineChangeTypeRequest(
    val newType: TimelineType,
    val oldType: TimelineType,
    val timelineIds: List<UUID>
)

data class AddSpecialtyToTimelineRequest(
    val limit: Int,
    val offset: Int,
)

data class AddSpecialtyToTimelineResponse(
    val successCount: Int,
    val errorCount: Int,
    val withoutStaffCount: Int,
    val withoutSpecialtyCount: Int,
    val errors: List<String>
)
