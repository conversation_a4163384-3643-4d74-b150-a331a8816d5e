package br.com.alice.product.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.data.layer.models.ProductBundle
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.Provider
import br.com.alice.data.layer.models.SpecialtyTiers
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ProductBundleService : Service,
    Getter<ProductBundle>,
    Adder<ProductBundle> {

    override val namespace get() = "product"
    override val serviceName get() = "product_bundle"

    override suspend fun add(model: ProductBundle): Result<ProductBundle, Throwable>

    override suspend fun get(id: UUID): Result<ProductBundle, Throwable>

    suspend fun update(model: ProductBundle, oldModel: ProductBundle): Result<ProductBundle, Throwable>

    suspend fun findActivesByIds(ids: List<UUID>): Result<List<ProductBundle>, Throwable>

    suspend fun findByProviderId(providerId: UUID): Result<List<ProductBundle>, Throwable>

    suspend fun findByExternalSpecialistId(externalSpecialist: UUID): Result<List<ProductBundle>, Throwable>

    suspend fun findByIdsWithProviders(ids: List<UUID>): Result<List<ProductBundleWithProviders>, Throwable>

    suspend fun findByIdsWithLimitedProviders(
        ids: List<UUID>,
        numberOfProviders: Int
    ): Result<List<ProductBundleWithProviders>, Throwable>

    suspend fun findBySpecialtyTiers(specialtyTiers: SpecialtyTiers): Result<List<ProductBundle>, Throwable>

    suspend fun findBySpecialtyTierId(specialtyTierId: UUID): Result<List<ProductBundle>, Throwable>

    suspend fun countAll(): Result<Int, Throwable>

    suspend fun findByRange(range: IntRange): Result<List<ProductBundle>, Throwable>

    suspend fun searchProduct(titlePrefix: String): Result<List<ProductBundle>, Throwable>

    suspend fun findActivesByIdsAndTypes(
        ids: List<UUID>,
        types: List<ProductBundleType>
    ): Result<List<ProductBundle>, Throwable>
}

data class ProductBundleWithProviders(
    val bundle: ProductBundle,
    val providers: List<Provider>
)


