package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.State
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.util.UUID

data class LegalGuardianInfoTempModel(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val firstName: String,
    val lastName: String,
    val socialFirstName: String? = null,
    val socialLastName: String? = null,
    val degreeOfKinship: DegreeOfKinship,
    val identityDocument: String,
    val identityDocumentIssuingBody: String,
    val nationalId: String,
    val email: String,
    val archived: Boolean = false,
    @Transient // because the type is different
    val address: LegalGuardianAddressModel,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
): Model, PersonReference {
    fun archive() = copy(archived = true)
}

// TODO: Extract to a common package, together with AddressModel from person
data class LegalGuardianAddressModel(
    val state: State,
    val city: String,
    val street: String,
    val number: String,
    val complement: String? = null,
    val neighbourhood: String? = null,
    val postalCode: String? = null,
    val lat: Double? = null,
    val lng: Double? = null
)
