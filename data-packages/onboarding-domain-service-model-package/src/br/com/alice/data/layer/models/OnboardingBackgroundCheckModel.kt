package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.serialization.JsonSerializable
import com.google.gson.annotations.Expose
import kotlinx.serialization.Transient
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class OnboardingBackgroundCheckModel(
    override val id: UUID = RangeUUID.generate(),
    @Expose
    override val personId: PersonId,
    val reportStatus: ReportStatus = ReportStatus.PENDING,
    val report: BackgroundCheckReportModel? = null,
    val notes: String? = null,
    val checklist: List<ChecklistItemModel>? = null,
    val rawData: String? = null,
    val finishedAt: LocalDateTime? = null,
    val externalReportId: String? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now()
) : Model, PersonReference {
    @Transient
    val isFinished get() = finishedAt != null
    @Transient
    val alreadyProcessed get() = reportStatus == ReportStatus.PROCESSED

    fun finish(notes: String?, checklist: List<ChecklistItemModel>) = copy(
        notes = notes,
        finishedAt = LocalDateTime.now(),
        checklist = checklist
    )
}

data class BackgroundCheckReportModel(
    val fullName: String,
    val mothersName: String,
    val dateOfBirth: LocalDate? = null,
    val income: String,
    val legalProceedings: List<LegalProceedingModel>,
    val financialRestrictions: List<FinancialRestrictionModel>? = null,
    val claims: List<ClaimModel>? = null,
    val pendingIssues: List<PendingIssueModel>? = null,
) : JsonSerializable

data class LegalProceedingModel(
    val type: String? = null,
    val link: String?
) : JsonSerializable

data class PendingIssueModel(
    val name: String,
    val cnpj: String,
    val amount: String,
    val createdAt: String,
    val eventType: String
) : JsonSerializable

data class ClaimModel(
    val name: String,
    val amount: String,
    val createdAt: String,
    val annotationDate: String,
) : JsonSerializable

data class FinancialRestrictionModel(
    val name: String,
    val amount: String,
    val createdAt: String,
) : JsonSerializable

data class ChecklistItemModel(
    val question: String,
    val answer: Boolean = false
) : JsonSerializable
