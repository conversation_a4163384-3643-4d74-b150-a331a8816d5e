package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class LegalGuardianAssociationModel(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val guardianId: PersonId,
    val degreeOfKinship: DegreeOfKinship,
    val statusHistory: List<LegalGuardianAssociationStatusModel>? = emptyList(),
    val status: LegalGuardianAssociationStatusType,
    val isSigned: Boolean = false,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0,
) : Model, PersonReference

data class LegalGuardianAssociationStatusModel(
    val legalGuardianAssociationStatusType: LegalGuardianAssociationStatusType,
    val updatedAt: LocalDateTime = LocalDateTime.now()
)

fun LegalGuardianAssociationModel.changeStatus(
    newStatus: LegalGuardianAssociationStatusType, updatedAt: LocalDateTime
) = copy(
    status = newStatus,
    statusHistory = statusHistory?.plus(listOf(LegalGuardianAssociationStatusModel(newStatus, updatedAt))),
    updatedAt = updatedAt
)
fun LegalGuardianAssociationModel.signResponsibilityTerm() = copy(
    isSigned = true,
    updatedAt = LocalDateTime.now()
)
