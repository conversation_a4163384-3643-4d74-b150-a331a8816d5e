package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus.DECLINED
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus.PENDING
import com.google.gson.annotations.Expose
import kotlinx.serialization.Transient
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class InsurancePortabilityRequestModel(
    override val id: UUID = RangeUUID.generate(),
    @Expose
    override val personId: PersonId,
    val healthInsuranceId: UUID? = null,
    val type: InsurancePortabilityRequestType? = InsurancePortabilityRequestType.NORMAL,
    val step: InsurancePortabilityRequestStep? = InsurancePortabilityRequestStep.HAS_HEALTH_INSURANCE,
    val declinedReasons: List<InsurancePortabilityRequestDeclineReason>? = emptyList(),
    val secondPortabilityRequirement: Boolean? = null,
    val currentTermPeriod: InsurancePortabilityRequestCurrentTermPeriod? = null,
    val minimumTermRequirement: Boolean? = null,
    val minimumTermRequirementEntryDate: LocalDate? = null,
    val priceCompatibilityRequirement: Boolean? = null,
    val priceCompatibilityRequirementPrice: BigDecimal? = null,
    val paymentFulfillmentRequirement: Boolean? = null,
    val hasCpt: Boolean? = null,
    val hasFulfilledCpt: Boolean? = null,
    val ansProtocolCode: String? = null,
    val activePlanRequirement: Boolean? = null,
    val hospitalCompatibility: Boolean? = null,
    val hospitalCompatibilityAnsCode: String? = null,
    val healthInsuranceCode: String? = null,
    val suggestedAction: InsurancePortabilitySuggestedAction? = null,
    val suggestedDeclineReasons: List<InsurancePortabilityRequestDeclineReason>? = emptyList(),
    val approvedPackage: InsurancePortabilityRequestApprovedPackage? = null,
    val answers: List<InsurancePortabilityRequestAnswerModel> = emptyList(),
    val answersV2: List<InsurancePortabilityRequestAnswerV2Model> = emptyList(),
    val status: InsurancePortabilityRequestStatus = InsurancePortabilityRequestStatus.CREATED,
    val missingDocuments: List<InsurancePortabilityMissingDocumentFileModel>? = emptyList(),
    val declineReason: InsurancePortabilityRequestDeclineReason? = null,
    val notes: String? = null,
    val productId: UUID? = null,
    val adhesionContract: Boolean = false,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val archived: Boolean = false,
    val pendingAt: LocalDateTime? = null,
    val finishedAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model, PersonReference {

    @Transient
    val pending get() = status == PENDING

    @Deprecated("Version 3 uses a list of reasons")
    fun decline(reason: InsurancePortabilityRequestDeclineReason, notes: String? = null) = copy(
        status = DECLINED,
        declineReason = reason,
        notes = notes
    )

    fun archive() = copy(archived = true)

    fun unarchive() = copy(archived = false)
}

data class InsurancePortabilityRequestAnswerModel(
    val answer: String,
    val questionType: InsurancePortabilityRequestQuestionType
)

data class InsurancePortabilityRequestAnswerV2Model(
    val answer: String,
    val questionType: InsurancePortabilityRequestQuestionTypeV2
)
