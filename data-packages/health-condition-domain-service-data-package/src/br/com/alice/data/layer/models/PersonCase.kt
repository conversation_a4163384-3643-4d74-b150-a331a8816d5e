package br.com.alice.data.layer.models

import br.com.alice.common.Disease
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.util.UUID

data class PersonCase (
    val recordId: UUID,
    val follow: Follow? = null,
    val responsibleStaffId: UUID? = null,
    val addedByStaffId: UUID? = null,
    val codeType: Disease.Type,
    val codeValue: String,
    val codeDescription: String? = null,
    val observation: String? = null,
    val severity: CaseSeverity,
    val status: CaseStatus,
    val addedAt: LocalDateTime,
    val startedAt: LocalDateTime,
    val healthConditionId: UUID,
    val channelId: String? = null,
    val seriousness: CaseSeriousness? = null,
    val version: Int = 0,
    override val id: UUID,
    override val personId: PersonId,
    val createdAt: LocalDateTime = LocalDateTime.now(),
): Model, PersonReference, HealthInformation {

    @Transient
    val staffIds = listOf(addedByStaffId, responsibleStaffId).distinct()
    @Transient
    val asDisease = Disease(
        type = codeType,
        value = codeValue,
        description = codeDescription,
        id = healthConditionId
    )
    fun description() = codeDescription ?: "$codeType - $codeValue"
}
