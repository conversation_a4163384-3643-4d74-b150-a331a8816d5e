package br.com.alice.data.layer.models

import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.data.layer.models.CaseSeverity.COMPENSATED
import br.com.alice.data.layer.models.CaseSeverity.ONGOING
import br.com.alice.data.layer.models.CaseStatus.ACTIVE
import br.com.alice.data.layer.models.CaseStatus.CONTEMPLATING
import br.com.alice.data.layer.models.CaseStatus.PENDING
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.util.UUID

data class CaseRecord(
    val caseId: UUID,
    val addedByStaffId: UUID? = null,
    val responsibleStaffId: UUID,
    val description: Disease,
    val cipes: List<Disease>? = emptyList(),
    val observation: String? = null,
    val severity: CaseSeverity,
    val status: CaseStatus,
    val addedAt: LocalDateTime,
    val startedAt: LocalDateTime,
    val referencedLinks: List<CaseRecordReference> = emptyList(),
    val caseCreatedBy: CaseRecordCreatedByType = CaseRecordCreatedByType.STAFF,
    val healthConditionId: UUID? = null,
    val seriousness: CaseSeriousness? = null,
    override val personId: PersonId,
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val follow: Follow? = null
) : Model, PersonReference, HealthInformation {

    @Transient
    val staffIds = listOf(addedByStaffId, responsibleStaffId).distinct()
    @Transient
    val validForCPTRule = listOf(PENDING, ACTIVE).contains(status)

    override fun sanitize() = when (this.description.type) {
        Disease.Type.GOAL -> {
            copy(
                status = if (CaseStatus.ofGoal().contains(this.status)) this.status else CONTEMPLATING,
                severity = if (CaseSeverity.ofGoal().contains(this.severity)) this.severity else ONGOING,
                seriousness = null
            )
        }
        else -> {
            copy(
                status = if (CaseStatus.ofCases().contains(this.status)) this.status else ACTIVE,
                severity = if (CaseSeverity.ofCases().contains(this.severity)) this.severity else COMPENSATED
            )
        }
    }

    fun isActive() = status in CaseStatus.actives() && severity in CaseSeverity.actives()
}

enum class CaseRecordCreatedByType {
    SYSTEM, STAFF, MEMBER
}

data class CaseRecordReference(
    val id: String,
    val model: CaseRecordReferenceModel
) : JsonSerializable

enum class CaseRecordReferenceModel {
    APPOINTMENT,
    HEALTH_DECLARATION,
    CHANNEL,
    PERSON_HEALTH_EVENT,
    COUNTER_REFERRAL,
    CLINICAL_OUTCOME_RECORD,
    HEALTH_LOGIC_RECOMMENDATION,
    SERVICE_SCRIPT_NAVIGATION,
    TERTIARY_INTENTION_TOUCH_POINT,
    HEALTH_PLAN_TASK_TEMPLATE,
}

enum class Follow(val value: Int, val title: String, val description: String) {
    CASE_DONE(
        1,
        "Demanda concluída",
        "Você e membro(a) consideram esta demanda concluída e ela não necessita de mais nenhum atendimento."
    ),
    FOLLOW_ATTENDENCE_NURSE(
        2,
        "Seguimento com Enf. Atendimento / Enf. Alice Agora",
        "Esta demanda deve ser acompanhada pelo pool de Enfermeiros(as) de Atendimento."
    ),
    REFERRAL_ON_SITE(
        3,
        "Encaminhamento OnSite",
        "Esta demanda deve ser avaliada presencialmente. Uma tarefa na wanda será criada para o pool de Referência ou para o(a) Enfermeiro(a) Gestor(a). Crie também as tarefas pertinentes no PdA."
    ),
    REFERRAL_ATTENDANCE_PHYSICIAN(
        4,
        "Encaminhamento para Méd. Atendimento / Méd. Alice Agora",
        "Esta demanda deve ser direcionada para o pool de Médicos(as) de Atendimento."
    ),
    REFERRAL_COMMUNITY_SPECIALIST(
        5,
        "Encaminhamento para Especialista",
        "Esta demanda deve ser avaliada por um especialista da Health Community. Uma tarefa na wanda será criada para o pool de Referência ou para o(a) Enfermeiro(a) Gestor(a). Crie também as tarefas pertinentes no PdA."
    ),
    FOLLOW_REFERENCE_NURSE(
        6,
        "Seguimento com Enf. Referência / Enf. Gestor",
        "Esta demanda deve ser acompanhada pelo pool de Enfermeiros(as) de Referência da Liga ou por Enfermeiro(a) Gestor(a) do Time de Saúde."
    ),
    REFERRAL_PS(
        7,
        "Encaminhamento ao Pronto Socorro",
        "A pessoa deve ser encaminhada ao pronto-socorro por conta desta demanda. Crie as tarefas pertinentes no PdA."
    ),
    REFERRAL_MULTI_TEAM(
        8,
        "Encaminhamento Multi",
        "Um profissional da equipe Multi deve dar seguimento a esta demanda. Uma tarefa na wanda será criada para o pool de Referência ou para o(a) Enfermeiro(a) Gestor(a). Crie também as tarefas pertinentes no PdA."
    ),
    REFERRAL_POP_NURSE(
        9,
        "Encaminhamento para Enf. Populacional / Enf. Gestor ",
        "Esta demanda será avaliada pelo(a) Enfermeiro(a) Populacional da Liga ou pelo(a) Enfermeiro(a) Gestor(a) do Time de Saúde. Uma tarefa na wanda será criada de acordo com a alocação do membro a um Time ou Liga."
    ),
    REFERRAL_POP_PHYSICIAN(
        10,
        "Encaminhamento para Méd. Populacional / Méd. Gestor",
        "Esta demanda será avaliada pelo(a) Médico(a) Populacional da Liga ou pelo(a) Médico(a) Gestor(a) do Time de Saúde. Uma tarefa na wanda será criada de acordo com a alocação do membro a um Time ou Liga."
    );

    companion object {
        fun getByValue(value: Int): Follow = Follow.values().first { it.value == value }
    }
}

