package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.DependentInformation
import br.com.alice.common.models.Sex
import br.com.alice.common.serialization.JsonSerializable
import com.google.gson.annotations.Expose
import kotlinx.serialization.Transient
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID

data class HealthDeclaration(
    @Expose
    override val personId: PersonId,
    val weight: BigDecimal? = null,
    val height: BigDecimal? = null,
    val sex: Sex? = null,
    val yearOfBirth: Int? = null,
    val answers: List<HealthDeclarationAnswer> = emptyList(),
    val cpts: List<Cpt> = emptyList(),
    val finishedAt: LocalDateTime? = null,
    val signedAt: LocalDateTime? = null,
    val signatureInfo: SignatureInfo? = null,
    val editedCpts: List<Cpt> = emptyList(),
    val editedAt: LocalDateTime? = null,
    val surgeries: List<Surgery> = emptyList(),
    val archived: Boolean = false,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override var updatedBy: UpdatedBy? = null,
    override val version: Int = 0,
    override val id: UUID = RangeUUID.generate()
) : Model, PersonReference, DependentInformation, UpdatedByReference {

    @Transient
    val finalCpts = if (isEdited) editedCpts else cpts
    @Transient
    val isFinished = finishedAt != null
    @Transient
    val conditions get() = answers.mapNotNull { it.healthConditions }.flatten().distinct()
    @Transient
    private val isEdited get() = editedAt != null

    fun empty() = copy(
        signatureInfo = null,
        signedAt = null,
        cpts = emptyList(),
        editedCpts = emptyList(),
        weight = null,
        height = null,
        sex = null,
        yearOfBirth = null,
        answers = emptyList(),
        finishedAt = null,
        surgeries = emptyList()
    )

    fun addCpts(cpts: List<Cpt>) = copy(
        cpts = cpts
    )

    fun editCpts(cpts: List<Cpt>) = copy(
        editedCpts = cpts.distinctBy { it.cids.first() },
        editedAt = LocalDateTime.now()
    )

    fun addNewAnswer(answer: HealthDeclarationAnswer): HealthDeclaration {
        val answers = answers.filterNot { it.questionType == answer.questionType }
        val newAnswers = answers.plus(answer)

        return copy(answers = newAnswers)
    }

    fun confirm() = copy(finishedAt = LocalDateTime.now())

    fun archive() = copy(archived = true)
}

data class HealthDeclarationAnswer(
    val question: String,
    val answer: String,
    val questionType: HealthDeclarationQuestionType? = null,
    val answeredAt: String? = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME),
    val healthConditions: List<HealthCondition>? = null,
    val denyAnswers: String? = null,
) : JsonSerializable

data class Surgery(
    val condition: String,
    val name: String? = null,
) : JsonSerializable

fun HealthCondition.toCpt() = Cpt(
    condition = this.name,
    cids = this.code?.let { listOf(it) } ?: emptyList(),
)

data class SignatureInfo(
    val deviceName: String
) : JsonSerializable

enum class HealthDeclarationQuestionType {
    IMC,
    PREGNANCY,
    PREGNANCY_RISK,
    PROSTHESIS,
    NOSE_EYE_AND_EARS,
    HEART_AND_BLOOD,
    HEART,
    LUNGS_OR_HEART,
    LUNGS,
    BRAIN,
    PROCTOLOGICAL,
    ENDOCRINE,
    VEINS,
    ENDOCRINE_AND_BLOOD,
    DIABETES_TYPE,
    GENITAL,
    KIDNEYS,
    ORTHOPEDICS,
    MENTAL_HEALTH,
    ORTHOPEDICS_AND_RHEUMATOLOGY,
    INFECTIOUS_CONDITIONS,
    TUMOR,
    TUMOR_RISK,
    ABDOMEN,
    GENETIC_CONDITION,
    AUTOIMMUNE_CONDITION,
    MALFORMATION,
    GLASSES,
    EYES,
    NOSE,
    MOUTH_THROAT,
    EARS,
    CIRCULATORY,
    HEMATOLOGICAL,
    ANEMIA,
    INTERCURRENCY,
    CHILD_DEVELOPMENT,
    DISEASE_IN_PREGNANCY,
    DEVELOPMENTAL_DELAY,
    SURGERY,
    CONFIRMATION
}

