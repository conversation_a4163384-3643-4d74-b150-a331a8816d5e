package br.com.alice.data.layer.models

import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.data.layer.models.HealthConditionCodeType.CIAP_2
import br.com.alice.data.layer.models.HealthConditionCodeType.CID_10
import br.com.alice.data.layer.models.HealthConditionCodeType.CIPE
import br.com.alice.data.layer.models.HealthConditionCodeType.GOAL
import br.com.alice.data.layer.models.HealthConditionCodeType.SYMPTOM
import kotlinx.serialization.Transient
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class HealthCondition(
    override val id: UUID = RangeUUID.generate(),
    val name: String,
    val displayName: String,
    val code: String? = null,
    val codeType: HealthConditionCodeType = CID_10,
    val specialities: List<String>? = null,
    val cptApplicationRule: CptApplicationRule = CptApplicationRule.NEVER,
    val surgeryNames: List<String>? = null,
    val riskRating: Int? = null,
    val isChronic: Boolean = false,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val searchTokens: String? = null,
    val memberFriendlyName: String? = null,
    val active: Boolean = true,
    val conditionType: HealthConditionType? = null,
    val suggestedMonthlyCost: BigDecimal? = null,
    val healthConditionAxisId: UUID? = null,
    @Transient
    val healthConditionTemplate: HealthConditionTemplate? = null
) : Model, JsonSerializable {

    @Transient
    val isEnriched = !specialities.isNullOrEmpty()

    fun isWithoutSurgeryOnly() = cptApplicationRule == CptApplicationRule.WITHOUT_SURGERY_ONLY
    fun toDisease() = Disease(
        id = this.id,
        type = when (this.codeType) {
            CID_10 -> Disease.Type.CID_10
            CIAP_2, HealthConditionCodeType.FREE_TEXT -> Disease.Type.CIAP_2
            GOAL -> Disease.Type.GOAL
            CIPE -> Disease.Type.CIPE
            SYMPTOM -> Disease.Type.SYMPTOM
        },
        value = this.code.orEmpty(),
        description = this.name
    )
}

enum class CptApplicationRule {
    ALWAYS,
    WITHOUT_SURGERY_ONLY,
    NEVER,
}

enum class HealthConditionType {
    ACUTE,
    LONGITUDINAL
}
