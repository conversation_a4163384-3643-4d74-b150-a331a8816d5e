package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.serialization.JsonSerializable
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.util.UUID

data class Risk(
    val calculatedValue: Int? = null,
    val finalValue: Int,
    val riskDescription: RiskDescription,
    val addedBy: AddedBy,
    val addedAt: LocalDateTime = LocalDateTime.now(),
    val referencedModels: List<ReferencedModel> = emptyList(),
    override val personId: PersonId,
    override val id: UUID = RangeUUID.generate()
) : Model, PersonReference, HealthInformation {

    @Transient
    val public get() = if (this.isTarget()) "TARGET" else "DEFAULT"

    fun isTarget() = this.riskDescription == RiskDescription.TARGET
    fun isHighRisk() = this.riskDescription == RiskDescription.HIGH_RISK

    fun getTargetCaseRecords() = this.referencedModels.filter { it.model == ReferenceModel.TARGET_CASE_RECORD }
    fun getTertiaryIntentionEmergency() = this.referencedModels.filter { it.model == ReferenceModel.TERTIARY_INTENTION_EMERGENCY }
    fun getTertiaryIntentionHospitalization() = this.referencedModels.filter { it.model == ReferenceModel.TERTIARY_INTENTION_HOSPITALIZATION }
    fun getPhysicianAppointment() = this.referencedModels.filter { it.model == ReferenceModel.PHYSICIAN_APPOINTMENT }
    fun getNewbornPerson() = this.referencedModels.filter { it.model == ReferenceModel.NEWBORN_PERSON }
    fun getChildWithHighRisk() = this.referencedModels.filter { it.model == ReferenceModel.CHILD_WITH_HIGH_RISK }

    fun getMinnesotaRisk() = if (isTarget()) toDescription(this.finalValue) else riskDescription

    companion object {
        fun toDescription(value: Int): RiskDescription =
            RiskDescription.values().find {
                it.startRange <= value && value <= it.endRange
            }!!
    }

    data class AddedBy(
        val id: UUID? = null,
        val type: AddedByType
    ) : JsonSerializable

    enum class AddedByType {
        STAFF, SYSTEM
    }

    data class ReferencedModel(
        val id: UUID,
        val model: ReferenceModel
    ) : JsonSerializable


    enum class ReferenceModel{
        CASE_RECORD,
        TARGET_CASE_RECORD,
        TERTIARY_INTENTION_EMERGENCY,
        TERTIARY_INTENTION_HOSPITALIZATION,
        PHYSICIAN_APPOINTMENT,
        NEWBORN_PERSON,
        CHILD_WITH_HIGH_RISK
    }
}
