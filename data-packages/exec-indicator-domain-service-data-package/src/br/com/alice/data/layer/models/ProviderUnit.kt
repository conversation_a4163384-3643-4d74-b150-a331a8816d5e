package br.com.alice.data.layer.models

import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Status
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

data class ProviderUnit(
    val id: UUID = RangeUUID.generate(),
    val type: Type,
    val name: String,
    val contractOrigin: Origin = Origin.ALICE,
    val site: String?,
    val cnpj: String?,
    val cnes: String? = null,
    val bankCode: String? = null,
    val agencyNumber: String? = null,
    val accountNumber: String? = null,
    val phones: List<PhoneNumber>,
    val workingPeriods: List<WorkingPeriod>,
    val qualifications: List<Qualification>,
    val imageUrl: String?,
    val providerId: UUID,
    val providerUnitGroupId: UUID? = null,
    val clinicalStaffIds: List<UUID>? = emptyList(),
    val administrativeStaff: List<UUID>? = emptyList(),
    val version: Int = 0,
    val brand: Brand = Brand.ALICE,
    val externalBrandId: String? = null,
    val medicalSpecialtyProfile: List<MedicalSpecialtyProfile>? = emptyList(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val urlSlug: String? = null,
    val status: Status = Status.ACTIVE,
    /** computed field */
    val address: StructuredAddress? = null,
    val searchTokens: String? = null,
    val deAccreditationDate: LocalDate? = null,
    val showOnScheduler: Boolean = false,
    val attendanceTypes: List<AttendanceType>? = emptyList(),
    val hasHospitalHealthTeam: Boolean = false,
    val showOnApp: Boolean = true,
    var updatedBy: UpdatedBy? = null,
) {
    val medicalSpecialtyIds = this.medicalSpecialtyProfile?.map { it.specialtyId }
    val medicalSubSpecialtyIds = this.medicalSpecialtyProfile?.map { it.subSpecialtyIds }?.flatten()

    enum class AttendanceType(val description: String) {
        EMERGENCY_CARE("Pronto Atendimento"),
        EXAMS_AND_PROCEDURES("Exames e procedimentos"),
        AMBULATORY("Consulta ambulatorial"),
        HOSPITALIZATION("Internação"),
        COMPLEX_THERAPIES("Terapias Complexas"),
        SERIAL_TREATMENT("Tratamento Seriado"),
        CHEMOTHERAPY_ATTACHMENTS("Anexos Quimioterapia"),
        RADIOTHERAPY_ATTACHMENTS("Anexos Radioterapia")
    }

    enum class Type(val description: String, val imageUrl: String) {
        HOSPITAL(
            "Internação",
            "https://s3.amazonaws.com/web.assets.alice.com.br/nuxt-gas/health-community/hospital.png"
        ),
        HOSPITAL_CHILDREN(
            "Hospital Infantil",
            "https://s3.amazonaws.com/web.assets.alice.com.br/nuxt-gas/health-community/children.png"
        ),
        LABORATORY(
            "Laboratórios Parceiros",
            "https://s3.amazonaws.com/web.assets.alice.com.br/nuxt-gas/health-community/laboratory.png"
        ),
        ALICE_HOUSE(
            "Casa Alice",
            "https://s3.amazonaws.com/web.assets.alice.com.br/nuxt-gas/health-community/alice_house.png"
        ),
        EMERGENCY_UNITY(
            "Pronto Socorro",
            "https://s3.amazonaws.com/web.assets.alice.com.br/nuxt-gas/health-community/emergency_unity.png"
        ),
        EMERGENCY_UNITY_CHILDREN(
            "Pronto Socorro Infantil",
            "https://s3.amazonaws.com/web.assets.alice.com.br/nuxt-gas/health-community/emergency_unity.png"
        ),
        MATERNITY(
            "Maternidade",
            "https://s3.amazonaws.com/web.assets.alice.com.br/nuxt-gas/health-community/maternity.png"
        ),
        CLINICAL(
            "Clínica",
            // TODO | change it before rollout
            "https://s3.amazonaws.com/web.assets.alice.com.br/nuxt-gas/health-community/maternity.png"
        ),
        CLINICAL_COMMUNITY(
            "Clínica da HC",
            // TODO | change it when have url
            "https://s3.amazonaws.com/web.assets.alice.com.br/nuxt-gas/health-community/maternity.png"
        ),
        MEDICAL_COMPANY(
            "Empresa Médica",
            // TODO | change it when have url
            "https://s3.amazonaws.com/web.assets.alice.com.br/nuxt-gas/health-community/maternity.png"
        ),
        VACCINE(
            "Laboratório/hospital com aplicação de vacinas",
            // TODO | change it when have url
            "https://s3.amazonaws.com/web.assets.alice.com.br/nuxt-gas/health-community/maternity.png"
        )
    }

    enum class Origin {
        ALICE,
        CASSI
    }

    data class WorkingPeriod(
        val open: DayOfWeekLocalTime,
        val close: DayOfWeekLocalTime? = null
    ) {
        constructor(dayOfWeek: DayOfWeek, open: LocalTime, close: LocalTime) :
                this(
                    DayOfWeekLocalTime(dayOfWeek, open),
                    if (close > open)
                        DayOfWeekLocalTime(dayOfWeek, close)
                    else
                        DayOfWeekLocalTime(dayOfWeek + 1, close)
                )

        companion object {
            val ALWAYS_OPEN = listOf(
                WorkingPeriod(
                    DayOfWeekLocalTime(DayOfWeek.MONDAY, LocalTime.MIDNIGHT)
                )
            )
        }
    }

    companion object {
        fun hospitalTypes(): List<Type> = listOf(
            Type.HOSPITAL,
            Type.MATERNITY,
            Type.EMERGENCY_UNITY,
            Type.HOSPITAL_CHILDREN,
            Type.EMERGENCY_UNITY_CHILDREN
        )

        fun laboratoryTypes(): List<Type> = listOf(
            Type.LABORATORY,
            Type.ALICE_HOUSE
        )
    }

    fun isHospital() = hospitalTypes().contains(type)
}

data class MedicalSpecialtyProfile(
    val specialtyId: UUID,
    val subSpecialtyIds: List<UUID> = emptyList(),
)

data class DayOfWeekLocalTime(
    val dayOfWeek: DayOfWeek,
    val localTime: LocalTime
)
