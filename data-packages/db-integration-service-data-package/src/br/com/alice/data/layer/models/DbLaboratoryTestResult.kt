package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.ExternalHealthInformation
import kotlinx.serialization.Transient
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class DbLaboratoryTestResult(
    val attendanceId: String,
    val claimId: Int? = null,
    @Transient
    val executionGroupId: UUID? = null,
    override val personId: PersonId,
    val items: List<DbResultadoProcedimento> = listOf(),
    val height: BigDecimal? = null,
    val weight: BigDecimal? = null,
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Model, PersonReference, ExternalHealthInformation {

    @Transient
    val resultDateTime
        get() = items
            .filter { it.dataHoraLiberacaoClinica != null }
            .minByOrNull { it.dataHoraLiberacaoClinica!! }
            ?.dataHoraLiberacaoClinica ?: createdAt

    @Transient
    val textResults get() : List<DbResultadoTexto> = items.flatMap { it.validResults() }
}

data class DbResultadoProcedimento(
    val codigoExameDB: String,
    val versaoLaudo: String?,
    val descricaoMetodologia: String?,
    val descricaoRegiaoColeta: String?,
    val dataHoraLiberacaoClinica: LocalDateTime?,
    val nomeLiberadorClinico: String?,
    val observacao1: String?,
    val observacao2: String?,
    val observacao3: String?,
    val observacao4: String?,
    val observacao5: String?,
    val material: String?,
    val identificacaoExameApoiado: String?,
    val materialApoiado: String?,
    val descricaoMaterialApoiado: String?,
    val descricaoExameApoiado: String?,
    val listaResultadoText: List<DbResultadoTexto> = listOf()
) {
    fun validResults() =
        listaResultadoText.filter { !it.valorResultado.isNullOrBlank() }
}

data class DbResultadoTexto(
    val codigoParametroDB: String,
    val descricaoParametroDB: String?,
    val valorResultado: String?,
    val unidadeMedida: String?,
    val valorReferencia: String?
) {
    companion object {
        val unfriendlyDescriptionsCodes = listOf("HIVQT", "HIV", "CHLAMY", "NEISSE", "TRICO", "MYCO")
    }

    fun friendlyDescription(): String =
        if (unfriendlyDescriptionsCodes.contains(codigoParametroDB))
            "$codigoParametroDB - $descricaoParametroDB"
        else
            descricaoParametroDB ?: ""
}
