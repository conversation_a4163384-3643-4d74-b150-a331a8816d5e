package br.com.alice.data.layer.models

import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.serialization.JsonSerializable
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.util.UUID

data class SpecialistOpinion(
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val appointmentId: UUID,
    val staffId: UUID,
    val status: SpecialistOpinionStatus = SpecialistOpinionStatus.REQUESTED,
    val medicalSpecialtyId: UUID,
    val assignedStaffId: UUID? = null,
    val assignedAt: LocalDateTime? = null,
    val caseSummary: String,
    val question: String,
    val demand: Disease? = null,
    val files: List<UUID>,
    val additionalParameters: AdditionalParameters? = null,
    override val personId: PersonId,
) : Model, PersonReference {

    @Transient
    val isRequested
        get() = status == SpecialistOpinionStatus.REQUESTED

    @Transient
    val isAssigned
        get() = status == SpecialistOpinionStatus.ASSIGNED

    @Transient
    val isResponded
        get() = status == SpecialistOpinionStatus.RESPONDED

    @Transient
    val isConcluded
        get() = status == SpecialistOpinionStatus.CONCLUDED

    fun assignToSpecialist(staffId: UUID): SpecialistOpinion =
        copy(
            status = SpecialistOpinionStatus.ASSIGNED,
            assignedStaffId = staffId,
            assignedAt = LocalDateTime.now()
        )

    fun removeAssignee(): SpecialistOpinion =
        copy(
            status = SpecialistOpinionStatus.REQUESTED,
            assignedStaffId = null,
            assignedAt = null
        )

    fun setEmailId(emailId: String): SpecialistOpinion =
        copy(
            additionalParameters = AdditionalParameters(emailId = emailId)
        )

    fun removeEmailId(): SpecialistOpinion =
        copy(
            additionalParameters = AdditionalParameters(emailId = null)
        )

    fun getDisease(): String? =
        when (demand?.type) {
            Disease.Type.CID_10 -> "CID ${demand.value}"
            Disease.Type.CIAP_2 -> "CIAP ${demand.value}"
            else -> demand?.value
        }
}

data class AdditionalParameters(
    val emailId: String? = null,
) : JsonSerializable

enum class SpecialistOpinionStatus {
    REQUESTED, ASSIGNED, RESPONDED, CONCLUDED
}

