package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.extensions.capitalizeEachWord
import br.com.alice.common.core.extensions.clearWhitespaces
import br.com.alice.common.core.extensions.onlyDigits
import com.google.gson.annotations.Expose
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.time.Period
import java.util.UUID

data class Lead(
    @Expose
    val nationalId: String? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    @Expose
    val email: String,
    val postalCode: String,
    val cnsNumber: String? = null,
    val dateOfBirth: LocalDateTime? = null,
    val declaredAge: Int? = null,
    val invitedAt: LocalDateTime? = null,
    val phoneNumber: String? = null,
    val trackingInfo: TrackingInfo? = null,
    val authorizeCommunication: Boolean? = null,
    val promoCodeId: UUID? = null,
    val productId: UUID? = null,
    val nickName: String? = null,
    val source: LeadSource? = null,
    val sourceId: UUID? = null,
    val zipcodeAddress: ZipcodeAddressLight? = null,
    val simulationHistory: List<UUID> = emptyList(),
    val ongoingCompanyDealId: UUID? = null,
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now()
) : Model {

    @Transient
    val contactName get() = firstName?.let { "$firstName $lastName" } ?: nickName

    @Transient
    val age
        get(): Int {
            if (dateOfBirth == null) return declaredAge!!
            val now = LocalDateTime.now().toLocalDate()
            return Period.between(dateOfBirth?.toLocalDate(), now).years
        }

    override fun sanitize() = copy(
        firstName = this.firstName?.capitalizeEachWord(),
        lastName = this.lastName?.capitalizeEachWord(),
        email = this.email.lowercase().trim(),
        nationalId = this.nationalId?.onlyDigits(),
        phoneNumber = this.phoneNumber?.onlyDigits()?.clearWhitespaces()
    )

    fun isInTest(test: LeadTest) =
        test in trackingInfo?.tests.orEmpty() || trackingInfo?.testsMap?.get(test) ?: false

    fun hasBeenSortedForTest(test: LeadTest) = trackingInfo?.testsMap?.get(test) != null
}

enum class LeadSource {
    SIMULATION, PROSPECT, APP, DUQUESA
}
