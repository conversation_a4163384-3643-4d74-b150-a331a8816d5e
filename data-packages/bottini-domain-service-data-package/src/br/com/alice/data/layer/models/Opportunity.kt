package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.util.UUID

data class Opportunity(
    override val id: UUID = RangeUUID.generate(),
    val simulationId: UUID? = null,
    val productId: UUID,
    @Deprecated("Please use priceListing instead")
    val prices: List<ProductPrice>,
    val priceListing: PriceListing? = null,
    val productPriceListingId: UUID? = null,
    val expiresAt: LocalDateTime,
    val parentOpportunityId: UUID? = null,
    val source: OpportunitySource = OpportunitySource.SIMULATION,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
): Model {
    fun getPrice(age: Int) = if (priceListing != null)
        priceListing.items.find { it.minAge <= age && age <= it.maxAge }?.amount
    else
        prices.find { it.minAge <= age && age <= it.maxAge }?.amount

    @Transient
    val expired get() = LocalDateTime.now() > expiresAt
}

fun Opportunity.withPriceListing(priceListing: PriceListing?): Opportunity =
    copy(priceListing = priceListing)

enum class OpportunitySource {
    SIMULATION,
    APP,
}
