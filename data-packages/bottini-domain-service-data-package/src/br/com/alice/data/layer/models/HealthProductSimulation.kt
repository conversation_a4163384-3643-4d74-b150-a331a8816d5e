package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType.AGE
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType.AGES
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType.EMAIL
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType.FIND_OUT_ALICE
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType.MICRO_COMPANY_AGES
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType.NAME
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType.PERSON_TARGET
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType.PHONE_NUMBER
import br.com.alice.data.layer.models.HealthProductSimulationQuestionType.POSTAL_CODE
import br.com.alice.data.layer.models.HealthProductSimulationSimulatorVersion.V8
import br.com.alice.data.layer.models.HealthProductSimulationSimulatorVersion.V9
import br.com.alice.data.layer.models.HealthProductSimulationType.COMPANY
import br.com.alice.data.layer.models.HealthProductSimulationType.CONSUMER
import br.com.alice.data.layer.models.HealthProductSimulationType.MEI
import br.com.alice.data.layer.models.HealthProductSimulationType.MICRO_COMPANY
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.util.UUID

data class HealthProductSimulation(
    override val id: UUID = RangeUUID.generate(),
    val answers: List<HealthProductSimulationAnswer> = emptyList(),
    val simulatorVersion: HealthProductSimulationSimulatorVersion? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    @Deprecated("Will be replaced by opportynity expiresAt")
    val expiresAt: LocalDateTime? = null,
    val trackingInfo: TrackingInfo? = null,
    val leadId: UUID? = null,
    val groupId: UUID? = null,
    val type: HealthProductSimulationType? = null,
    val abTest: AbTest? = null,
    val status: HealthProductSimulationStatus? = HealthProductSimulationStatus.STARTED,
    val productId: UUID? = null,
    val prices: PriceListing? = null,
    val salesAgentId: UUID? = null,
) : Model {
    enum class HealthProductSimulationStatus {
        STARTED,
        FINISHED,
    }

    @Transient
    val email get() = getAnswer(EMAIL)
    @Transient
    val name get() = getAnswer(NAME)
    @Transient
    val target get() = getAnswer(PERSON_TARGET)
    @Transient
    val postalCode get() = getAnswer(POSTAL_CODE)
    @Transient
    val phoneNumber get() = getAnswer(PHONE_NUMBER)
    @Transient
    val age get() = getAnswer(AGE)
    @Transient
    val findOutAlice get() = getAnswer(FIND_OUT_ALICE)
    @Transient
    val ages get() =
        (getAnswer(AGE)?.let { listOf(it) } ?: getAgesAnswer()?.map { it.age })?.map(String::toInt)

    fun isConsumerSimulation() =
        this.type?.let { simulationType ->
            simulationType == CONSUMER
        } ?: true

    fun isCompanySimulation() =
        this.type?.let { simulationType ->
            simulationType == COMPANY
        } ?: false

    fun isSmallCompanySimulation() =
        this.type?.let { simulationType ->
            simulationType in SMALL_COMPANY_SIMULATION_TYPES
        } ?: false

    fun isB2BSimulation() =
        this.type?.let { simulationType ->
            simulationType in B2B_SIMULATION_TYPES
        } ?: false

    fun isGroupSimulation() =
        this.groupId != null

    fun getRecommendedProductAnchor() =
        this.type?.let { simulationType ->
            when (simulationType) {
                MICRO_COMPANY,
                MEI -> ProductAnchor.ALICE_EINSTEIN_B2B
                COMPANY -> ProductAnchor.ALICE_EXCLUSIVE_P
                else -> ProductAnchor.ALICE_CUSTOM
            }
        } ?: ProductAnchor.ALICE_CUSTOM


    fun getAnswer(questionType: HealthProductSimulationQuestionType) = answers
        .filter { it.questionType == questionType }
        .map(HealthProductSimulationAnswer::answer)
        .firstOrNull()

    fun getAgesAnswer(): List<AgesAnswer>? =
        if (type in SMALL_COMPANY_SIMULATION_TYPES) getMicroCompanyAgesAnswer() else getConsumerAgesAnswer()

    fun verifyIsEmployeeCompanyLowerThan29() = when (getAnswer(HealthProductSimulationQuestionType.COMPANY_EMPLOYEES_NUMBER)) {
        "30-99",
        "100-499",
        "500+" -> false
        "1-5",
        "6-29" -> true
        else -> false
    }

    fun verifyIsEmployeeCompanyLowerThan6() = when (getAnswer(HealthProductSimulationQuestionType.COMPANY_EMPLOYEES_NUMBER)) {
        "1-5" -> true
        else -> false
    }

    fun isTestSimulation() = if (email != null) Regex(EMAIL_IS_ALICE_OR_TEST_REGEX).containsMatchIn(email!!) else false

    private fun getConsumerAgesAnswer(): List<AgesAnswer>? = getAnswer(AGES)?.let {
        getAgesArrayFromString(it)
    }

    private fun getMicroCompanyAgesAnswer(): List<AgesAnswer>? = getAnswer(MICRO_COMPANY_AGES)?.let {
        getAgesArrayFromString(it)
    }

    private fun getAgesArrayFromString(ages: String): List<AgesAnswer> {
        val gson = Gson()
        val agesArray = object : TypeToken<List<AgesAnswer>>() {}.type
        return gson.fromJson(ages, agesArray)
    }

    fun replaceAnswers(answers: List<HealthProductSimulationAnswer>) = copy(answers = answers)

    private fun checkIfQuestionIsRequired(questionType: HealthProductSimulationQuestionType): Boolean {
        val isRequiredInThisVersion = questionType.requiredVersionMap?.get(simulatorVersion)
        val isRequiredInThisType = questionType.requiredSimulationTypeMap?.get(type)

        return when {
            isRequiredInThisVersion != null && isRequiredInThisType != null -> isRequiredInThisVersion && isRequiredInThisType
            isRequiredInThisVersion != null -> isRequiredInThisVersion
            isRequiredInThisType != null -> isRequiredInThisType
            else -> questionType.required
        }
    }

    fun hasAllQuestionsAnswered() = HealthProductSimulationQuestionType
        .values()
        .filter { checkIfQuestionIsRequired(it) }
        .map(this::getAnswer)
        .all { !it.isNullOrBlank() }

    @Transient
    val progressPercent: Int
        get() {
            val requiredAnswersAnswered = answers.count { checkIfQuestionIsRequired(it.questionType) }
            val requiredAnswersTotal =
                HealthProductSimulationQuestionType.values().count { checkIfQuestionIsRequired(it) }

            return (100 * requiredAnswersAnswered) / requiredAnswersTotal
        }

    @Transient
    val expired get() = (expiresAt != null) && (LocalDateTime.now() > expiresAt)
}

private const val EMAIL_IS_ALICE_OR_TEST_REGEX = "\\b[A-Za-z0-9._%+-]+@(alice|test|teste)(\\.com(\\.br)?)?\\b"

data class HealthProductSimulationAnswer(
    val answer: String,
    val questionType: HealthProductSimulationQuestionType,
)

data class HealthProductSimulationAnswersV2(
    val companyEmployees: String?,
    val companyCity: String?,
    val companyCnpj: String?,
    val companyName: String?,
    val responsibleName: String?,
    val responsiblePhone: String?,
    val responsibleEmail: String?,
    val findOutAlice: String?,
)

fun HealthProductSimulationAnswersV2.verifyIsEmployeeCompanyLowerThan6(): Boolean {
    return when (this.companyEmployees) {
        "1-5" -> true
        else -> false
    }
}

enum class HealthProductSimulationQuestionType(
    val required: Boolean,
    val requiredVersionMap: Map<HealthProductSimulationSimulatorVersion, Boolean>? = null,
    val requiredSimulationTypeMap: Map<HealthProductSimulationType, Boolean>? = null
) {
    CONTACT_OPTIN(false, null, mapOf(HealthProductSimulationType.COMPANY to true)),
    EMAIL(true),
    EMPTY(false),
    HAS_CHILDREN(false),
    HAS_INSURANCE_PLAN(false),
    HEALTH_INSURANCE_OPINION(false),
    HEALTHCARE_TEAM(false),
    HOSPITAL_OPTION(false),
    LABORATORY_BUNDLE(false),
    NAME(true),
    PERSON_TARGET(false),
    PHONE_NUMBER(true),
    GENERAL_PREFERENCES(false),
    PLAN_PREFERENCES(false),
    POSTAL_CODE(true, null, mapOf(HealthProductSimulationType.MEI to false, MICRO_COMPANY to false, COMPANY to false)),
    METROPOLITAN_AREA(false),
    AGE(false),
    AGES(true, null, mapOf(HealthProductSimulationType.MEI to false, MICRO_COMPANY to false, COMPANY to false)),
    ACCOMMODATION(false),
    MICRO_COMPANY_AGES(false, null),
    MICRO_COMPANY_RESPONSIBLE_PERSON_DATA(false),
    MICRO_COMPANY_CITY(false, null, mapOf(HealthProductSimulationType.MEI to false, MICRO_COMPANY to false)),
    COMPANY_DATA(false),
    MICRO_COMPANY_DATA(false),
    COMPANY_EMPLOYEES_NUMBER(false, mapOf(HealthProductSimulationSimulatorVersion.V11 to true)),
    COMPANY_RESPONSIBLE_PERSON_DATA(false),
    COMPANY_CITY(false, null, mapOf(COMPANY to true)),
    PEOPLE_NUMBER(false, mapOf(V8 to true)),
    HAS_CNPJ(false, mapOf(V8 to true, V9 to true)),
    COMPANY_NAME(false, null, mapOf(HealthProductSimulationType.MEI to false, MICRO_COMPANY to false, COMPANY to true)),
    COMPANY_CNPJ(false, null, mapOf(HealthProductSimulationType.MEI to true, MICRO_COMPANY to true, COMPANY to true)),
    MEI(false, null, mapOf(MICRO_COMPANY to false, HealthProductSimulationType.MEI to false)),
    FIND_OUT_ALICE(
        required = false,
        requiredVersionMap = mapOf(HealthProductSimulationSimulatorVersion.V11 to true, V8 to false, HealthProductSimulationSimulatorVersion.V13 to false),
    )
}

enum class HealthProductSimulationSimulatorVersion {
    V0,
    V1,
    V2,
    V3,
    V4,
    V5,
    V6,
    V7,
    V8,
    V9,
    V10,
    V11,
    V12,
    V13,
    V14,
    V15;

    companion object {
        fun fromValue(value: String) = values().firstOrNull { it.name == value }
    }
}

data class AgesAnswer(
    val age: String,
)

enum class HealthProductSimulationType {
    CONSUMER,
    MICRO_COMPANY,
    COMPANY,
    MEI
}

val SMALL_COMPANY_SIMULATION_TYPES = listOf(
    MICRO_COMPANY,
    MEI
)

val B2B_SIMULATION_TYPES = listOf(
    COMPANY,
    MICRO_COMPANY,
    MEI
)

data class AbTest(
    val key: String,
    val variant: String
) : JsonSerializable
