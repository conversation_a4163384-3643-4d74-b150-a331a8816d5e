package br.com.alice.data.layer.models

import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.serialization.JsonSerializable
import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.util.UUID

data class ScreeningNavigation(
    val status: ScreeningNavigationStatus,
    override val personId: PersonId,
    val symptoms: List<Disease>? = emptyList(),
    val subjectiveCodes: List<Disease>? = emptyList(),
    val suggestedOutput: TriageSuggestedOutput? = null,
    val dalyaPrediction: DalyaPrediction? = null,
    val dalyaUncertainties: DalyaUncertainties? = null,
    val dalyaExplanation: String? = null,
    val iaTextSuggestion: String? = null,
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Model, PersonReference {

    @Transient
    val symptomIds = symptoms?.mapNotNull { it.id } ?: emptyList()

    fun isFinishedTriage() = this.status != ScreeningNavigationStatus.STARTED

}

enum class ScreeningNavigationStatus {
    FINISHED,
    STARTED,
    CLOSED,
    DROPPED
}

enum class TriageSuggestedOutput {
    PA_DIGITAL,
    PS,
    APS,
    SPECIALIST,
    UNKNOWN,
    CIAP_NOT_FOUND
}

data class DalyaPrediction(
    val type: TriageSuggestedOutput,
    val value: Float,
    val allValues: DalyaPredictionValues? = null
) : JsonSerializable

data class DalyaUncertainties(
    val type: TriageSuggestedOutput,
    val lowerValue: Float,
    val upperValue: Float,
    val allValues: DalyaUncertaintiesValues? = null
) : JsonSerializable

data class DalyaPredictionValues(
    @SerializedName("PA_DIGITAL")
    val paDigital: Float,

    @SerializedName("PS")
    val ps: Float? = null,

    @SerializedName("APS")
    val aps: Float,

    @SerializedName("SPECIALIST")
    val specialist: Float? = null

): JsonSerializable

data class DalyaUncertaintiesValues(
    @SerializedName("PA_DIGITAL")
    val paDigital: DalyaUncertaintiesRangeValues,

    @SerializedName("PS")
    val ps: DalyaUncertaintiesRangeValues? = null,

    @SerializedName("APS")
    val aps: DalyaUncertaintiesRangeValues,

    @SerializedName("SPECIALIST")
    val specialist: DalyaUncertaintiesRangeValues? = null,
): JsonSerializable

data class DalyaUncertaintiesRangeValues(
    val lower: Float,
    val upper: Float
): JsonSerializable
