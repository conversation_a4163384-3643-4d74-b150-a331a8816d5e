package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import io.ktor.util.date.WeekDay
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

data class AliceAgoraWorkingHours(
    val workingHours: List<WorkingHours>,
    val chatDescription: ChatDescription? = null,
    val overrides: List<Overrides> = emptyList(),
    val category: CategoryType = CategoryType.ASSISTANCE,
    val status: WorkingHoursStatus = WorkingHoursStatus.ACTIVE,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0,
    override val id: UUID = RangeUUID.generate()
) : Model, JsonSerializable

enum class CategoryType(val title: String) {
    ASSISTANCE("Assistencial"),
    ADMINISTRATIVE("Administrativo")
}

data class WorkingHours(
    val dayOfTheWeek: WeekDay,
    val opens: LocalTime,
    val closes: LocalTime
)

data class Overrides(
    val day: LocalDate,
    val opens: LocalTime,
    val closes: LocalTime,
    val chatDescription: ChatDescription? = null
): JsonSerializable

enum class WorkingHoursStatus {
    ACTIVE,
    INACTIVE;
}

data class ChatDescription(
    val online: AvailabilityTexts,
    val offline: AvailabilityTexts
): JsonSerializable

data class AvailabilityTexts(
    val hubAvailability: String,
    val hubDescription: String,
    val headerDescription: String
)
