package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.util.UUID

data class ServiceScriptAction(
    override val id: UUID = RangeUUID.generate(),
    val type: ActionType,
    val externalId: UUID,
    val status: ServiceScriptActionStatus = ServiceScriptActionStatus.ACTIVE
): Model, JsonSerializable {

    enum class ActionType {
        HEALTH_PLAN_TASK_TEMPLATE,
        HEALTH_PLAN_TASK_GROUP_TEMPLATE,
        HEALTH_CONDITION,
        SYMPTOM,
        WANDA_PHYSICAL_PRESCRIPTION,
        WANDA_NOT_PHYSICAL_PRESCRIPTION,
        WANDA_NOT_PRESCRIPTION
    }
}

enum class ServiceScriptActionStatus {
    ACTIVE,
    INACTIVE
}
