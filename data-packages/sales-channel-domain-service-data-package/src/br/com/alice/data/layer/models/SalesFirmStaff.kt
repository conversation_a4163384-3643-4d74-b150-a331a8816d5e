package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.Status
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.util.UUID

data class SalesFirmStaff(
    override val id: UUID = RangeUUID.generate(),
    val salesFirmId: UUID,
    val firstName: String,
    val lastName: String,
    val email: String,
    val role: SalesFirmStaffRole,
    val status: Status = Status.ACTIVE,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model {
    @Transient
    val fullName get() = "$firstName $lastName"

    @Transient
    val active get() = status == Status.ACTIVE
}

enum class SalesFirmStaffRole(val description: String) {
    MAIN_STAFF("Usuário principal da corretora"),
}

