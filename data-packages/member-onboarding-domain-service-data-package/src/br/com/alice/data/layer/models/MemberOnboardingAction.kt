package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class MemberOnboardingAction(
    val name: String = "",
    val icon: String? = null,
    val label: String,
    val actionUrl: String,
    val version: Int = 0,
    val isBackVisible: Boolean = true,
    val listenAppState: MemberOnboardingActionAppState? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val id: UUID = RangeUUID.generate(),
): Model {
    enum class MemberOnboardingActionAppState(description: String) {
        PERSON_ASSOCIATED_TO_HEALTH_LEAGUE("App state que gerencia membro vinculado a liga de saúde"),
    }
}
