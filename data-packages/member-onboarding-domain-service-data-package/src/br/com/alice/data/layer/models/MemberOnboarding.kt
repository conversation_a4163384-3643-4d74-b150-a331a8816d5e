package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType.COVER_MFC
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.util.UUID

data class MemberOnboarding (
    override val personId: PersonId,
    val steps: List<Step>,
    val completed: Boolean = false,
    val referencedLinks: List<MemberOnboardingReferencedLink> = emptyList(),
    val flowType: MemberOnboardingFlowType = MemberOnboardingFlowType.ADULT,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val id: UUID = RangeUUID.generate(),
): Model, PersonReference {

    enum class MemberOnboardingStepType {
        VIDEO,
        COVER,
        SCORE_MAGENTA,
        ALICE_INFO,
        CONCLUSION,
        COVER_MFC
    }

    enum class MemberOnboardingStepStatus {
        PENDING, ON_GOING, COMPLETED, BLOCKED
    }

    fun isStepCompleted(stepType: MemberOnboardingStepType) = steps.any { it.templateType == stepType && it.status == MemberOnboardingStepStatus.COMPLETED }

    fun isCoverMFCStepCompleted() = steps.any { it.templateType == COVER_MFC }
        .takeIf { it }
        ?.let { isStepCompleted(COVER_MFC) }
        ?: true

    @Transient
    private val hasBlockedStep get() = steps.any { it.status == MemberOnboardingStepStatus.BLOCKED }

    @Transient
    val isCompleted get() = finishedSteps == steps.size

    @Transient
    val canUnblockFinalStep get() = hasBlockedStep && finishedSteps == steps.size - 1

    @Transient
    val finishedSteps get() = steps.count { it.status == MemberOnboardingStepStatus.COMPLETED }
}

enum class MemberOnboardingFlowType {
    ADULT, CHILD
}

data class Step(
    val templateType: MemberOnboarding.MemberOnboardingStepType,
    val status: MemberOnboarding.MemberOnboardingStepStatus,
    val updatedAt: LocalDateTime? = LocalDateTime.now()
)

data class MemberOnboardingReferencedLink(
    val id: UUID,
    val model: MemberOnboardingReferencedLinkModel
) : JsonSerializable

enum class MemberOnboardingReferencedLinkModel {
    HEALTH_PLAN_TASK,
    HEALTH_PLAN_TASK_TEMPLATE
}
