package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class SuggestedProcedure(
    val specialtyId: UUID,
    val type: SuggestedProcedureType,
    val tussCode: String,
    val tussProcedureAliceCode: String,
    val healthSpecialistResourceBundleCode: String? = null,

    override val id: UUID = RangeUUID.generate(),
    override var updatedBy: UpdatedBy? = null,
    override val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Model, UpdatedByReference

enum class SuggestedProcedureType {
    PROCEDURE_EXECUTED_DEFAULT,
    PROCEDURE_EXECUTED
}
