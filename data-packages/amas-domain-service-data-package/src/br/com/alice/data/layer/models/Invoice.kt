package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.utils.RandomIdUtils
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class Invoice (
    val id: UUID = RangeUUID.generate(),

    val version: Int = 0,
    val code: String = createInvoiceCode(),
    val execIndicatorAuthorizerId: UUID? = null,
    val providerUnitId: UUID? = null,
    val userEmail: String,
    val status: TissInvoiceStatus = TissInvoiceStatus.DRAFT,
    val type: InvoiceType? = InvoiceType.HEALTH_INSTITUTION,
    val expenseType: InvoiceExpenseType = InvoiceExpenseType.UNDEFINED,
    val automaticGenerated: Boolean = false,
    val referenceDate: LocalDate? = LocalDate.now(),
    val staffId: UUID? = null,
    val searchTokens: String? = null,
    val bonusPercent: Int? = 0,

    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    companion object {
        private const val INVOICE_CODE_SIZE = 8
        private val invoiceCodeChars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ".toCharArray()
        fun createInvoiceCode() = RandomIdUtils.randomId(alphabet = invoiceCodeChars, size = INVOICE_CODE_SIZE)
    }

    fun canReceiveCritique() = listOf(
        TissInvoiceStatus.DRAFT,
        TissInvoiceStatus.RECEIVED,
        TissInvoiceStatus.REVISION_REQUESTED
    ).contains(status)
}

enum class InvoiceType {
    HEALTH_INSTITUTION,
    HEALTH_SPECIALIST;
    companion object {
        fun fromName(name: String) = InvoiceType.values().firstOrNull { it.name == name.uppercase() }
    }

}

enum class InvoiceExpenseType(val description: String) {
    EXPENSES("Outros pagamentos"),
    PROCEDURES("Honorários médicos"),
    UNDEFINED("Não especificado")
}
