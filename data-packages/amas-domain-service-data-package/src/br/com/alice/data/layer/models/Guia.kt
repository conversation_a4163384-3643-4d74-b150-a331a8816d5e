package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.PersonId
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.utils.RandomIdUtils
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class Guia(
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    val personId: PersonId? = null,
    val memberName: String? = null,
    val number: String,
    val memberNewBorn: String? = "N",
    val memberCode: String? = null,
    val memberTier: TierType? = null,
    val professionalRequesterName: String? = null,
    val professionalRequesterCouncilNumber: String? = null,
    val professionalRequesterCouncil: String? = null,
    val professionalRequesterCbos: String? = null,
    val professionalRequesterCouncilState: String? = null,
    val professionalExecutorName: String? = null,
    val professionalExecutorCouncil: String? = null,
    val professionalExecutorCouncilNumber: String? = null,
    val professionalExecutorCbos: String? = null,
    val professionalExecutorCouncilState: String? = null,
    val providerRequesterCode: String? = null,
    val providerRequesterName: String? = null,
    val requestedAt: LocalDate? = null,
    val attendanceType: String? = null,
    val providerExecutorCode: String? = null,
    val providerExecutorName: String? = null,
    val providerExecutorCnes: String? = null,
    val attendanceCharacter: String? = null,
    val accidentIndication: String? = null,
    val tissBatchNumber: String,
    val valueProcedures: BigDecimal? = null,
    val valueTaxRents: BigDecimal? = null,
    val valueMaterials: BigDecimal? = null,
    val valueMedicines: BigDecimal? = null,
    val valueOpme: BigDecimal? = null,
    val valueMedicinalGases: BigDecimal? = null,
    val valueDaily: BigDecimal? = null,
    val valueTotal: BigDecimal,
    val clinicIndication: String? = null,
    val appointmentType: String? = null,
    val providerRequesterCnpj: String? = null,
    val providerExecutorCnpj: String? = null,
    val tissBatchId: UUID,
    val serviceTypes: List<HealthSpecialistResourceBundleServiceType> = emptyList(),
    val executionEnvironment: HealthSpecialistProcedureExecutionEnvironment = HealthSpecialistProcedureExecutionEnvironment.DOES_NOT_APPLY,
    var updatedBy: UpdatedBy? = null
) : HealthInformation
{
    companion object {
        private const val NUMBER_SIZE = 8
        private const val TISS_BATCH_NUMBER_SIZE = 10
        private val codeChars = "**********".toCharArray()
        fun generateNumber(): String {
            return RandomIdUtils.randomId(alphabet = codeChars, size = NUMBER_SIZE)
        }

        fun generateTissBatchNumber(): String {
            return RandomIdUtils.randomId(alphabet = codeChars, size = TISS_BATCH_NUMBER_SIZE)
        }
    }
}
