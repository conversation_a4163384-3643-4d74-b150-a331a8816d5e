package br.com.alice.data.layer.models

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.isBeforeEq
import kotlinx.serialization.Transient
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class BeneficiaryModel(
    override val id: UUID = RangeUUID.generate(),
    override val parentBeneficiary: UUID? = null,
    override val personId: PersonId,
    val memberId: UUID,
    val companyId: UUID,
    override val type: BeneficiaryType,
    override val contractType: BeneficiaryContractType? = null,
    override val parentBeneficiaryRelationType: ParentBeneficiaryRelationType? = null,
    val activatedAt: LocalDateTime,
    val canceledAt: LocalDateTime? = null,
    override val hiredAt: LocalDateTime? = null,
    val parentBeneficiaryRelatedAt: LocalDateTime? = null,
    val canceledReason: BeneficiaryCancelationReason? = null,
    val canceledDescription: String? = null,
    override val cnpj: String? = null,
    val hasContributed: Boolean? = null,
    val archived: Boolean = false,
    override val version: Int = 0,
    @Transient
    val onboarding: BeneficiaryOnboardingModel? = null,
    @Transient
    val dependents: List<BeneficiaryModel>? = null,
    val brand: Brand? = Brand.ALICE,
    val companySubContractId: UUID? = null,
    val memberStatus: MemberStatus? = null,
    val parentPerson: PersonId? = null,
    val gracePeriodType: GracePeriodType? = null,
    val gracePeriodTypeReason: GracePeriodTypeReason? = null,
    val gracePeriodBaseDate: LocalDate? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override var updatedBy: UpdatedBy? = null,
) : Model, PersonReference, BeneficiaryBase, UpdatedByReference {

    @Transient
    val isCanceled get() = canceledAt != null && canceledAt.isBeforeEq(LocalDateTime.now().atEndOfTheDay())

    fun toNewMembershipBeneficiary(newMemberId: UUID, newMemberStatus: MemberStatus) = this.copy(
        id = RangeUUID.generate(),
        memberId = newMemberId,
        memberStatus = newMemberStatus,
    )

    fun withNewParent(newParent: BeneficiaryModel) = this.copy(
        parentPerson = newParent.personId,
        parentBeneficiary = newParent.id,
        companySubContractId = newParent.companySubContractId,
    )
}
