package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class MemberTelegramTrackingModel(
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    val externalId: String,
    val name: String? = null,
    val address: String? = null,
    val streetNumber: String? = null,
    val addressComplement: String? = null,
    val zip: String? = null,
    val city: String? = null,
    val state: String? = null,
    val country: String? = null,
    val nameSender: String? = null,
    val addressSender: String? = null,
    val zipSender: String? = null,
    val citySender: String? = null,
    val stateSender: String? = null,
    val streetNumberSender: String? = null,
    val addressComplementSender: String? = null,
    val countrySender: String? = null,
    val shipType: String? = null,
    val tracking: String? = null,
    val status: String? = null,
    val statusDelivery: String? = null,
    val trackingRecordsLastUpdate: LocalDateTime? = null,
    val dateSubmit: LocalDateTime? = null,
    val dateLastChange: LocalDateTime? = null,
    val tag: String? = null,

    val trackingEvents: List<MemberTelegramTrackingEventsModel> = emptyList(),
    val postalType: MemberTelegramPostalTypeModel? = null,
    val telegramInfo: MemberTelegramInfoModel? = null,
    val deliveryEstimate: LocalDateTime? = null,
    var searchTokens: String? = null
) : Model

data class MemberTelegramTrackingEventsModel(
    val code: String,
    val type: Int,
    val createdDateTime: LocalDateTime,
    val description: String
) : JsonSerializable

data class MemberTelegramPostalTypeModel(
    val acronym: String,
    val description: String,
    val category: String,
) : JsonSerializable

data class MemberTelegramInfoModel(
    val id: String,
    val jobVisualId: String,
    val msg: String,
    val senderCopy: Boolean,
    val deliveryConfirmation: Boolean,
) : JsonSerializable
