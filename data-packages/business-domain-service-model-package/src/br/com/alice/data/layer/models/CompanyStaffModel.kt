package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import com.google.gson.annotations.Expose
import kotlinx.serialization.Transient
import java.time.LocalDateTime
import java.util.UUID

data class CompanyStaffModel(
    override val id: UUID = RangeUUID.generate(),
    val companyId: UUID,
    val transactedAt: LocalDateTime = LocalDateTime.now(),
    val firstName: String,
    val lastName: String,
    val email: String,
    @Expose
    val role: CompanyStaffRole,
    val archivedAt: LocalDateTime? = null,
    val accessLevel: CompanyStaffAccessLevel = CompanyStaffAccessLevel.ADMIN,
    override val version: Int = 0,
    override var updatedBy: UpdatedBy? = null,
) : Model, UpdatedByReference {

    override fun sanitize() = copy(
        firstName = firstName.trim(),
        lastName = lastName.trim(),
        email = email.trim().lowercase(),
    )

    fun fullName() = "$firstName $lastName"

    fun isMainCompanyStaff() = listOf(
        CompanyStaffRole.MAIN_COMPANY_STAFF,
        CompanyStaffRole.LEGAL_REPRESENTATIVE
    ).contains(role)

    @Transient
    val active
        get() = archivedAt == null
}

