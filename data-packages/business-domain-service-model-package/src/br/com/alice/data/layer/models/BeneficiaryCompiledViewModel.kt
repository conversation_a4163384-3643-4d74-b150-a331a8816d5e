package br.com.alice.data.layer.models

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.Sex
import kotlinx.serialization.Transient
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class BeneficiaryCompiledViewModel(
    override val id: UUID = RangeUUID.generate(),
    val companyId: UUID,
    val companySubContractId: UUID? = null,
    override val personId: PersonId,
    val personNationalId: String,
    val personFullSocialName: String,
    val personEmail: String,
    val parentPersonId: PersonId? = null,
    val parentPersonFullName: String? = null,
    val personBirthDate: LocalDate? = null,
    val personSex: Sex? = null,
    val beneficiaryId: UUID,
    val beneficiaryType: BeneficiaryType,
    val beneficiaryContractType: BeneficiaryContractType? = null,
    val beneficiaryActivatedAt: LocalDateTime? = null,
    val beneficiaryCanceledAt: LocalDateTime? = null,
    val productId: UUID,
    val productAnsNumber: String? = null,
    val productTitle: String,
    val productDisplayName: String? = null,
    val productComplementName: String? = null,
    @Transient
    val productPreviousDisplayName: String? = null,
    val requestProductId: UUID? = null,
    val requestProductDisplayName: String? = null,
    val requestProductComplementName: String? = null,
    val requestProductApplyAt: LocalDateTime? = null,
    val memberId: UUID,
    val memberStatus: MemberStatus? = null,
    val memberCanceledAt: LocalDateTime? = null,
    val memberActivatedAt: LocalDateTime? = null,
    val immersionStatus: BeneficiaryViewImmersionStatus,
    val insuranceStatus: BeneficiaryViewInsuranceStatus,
    val lastOnboardingPhase: BeneficiaryOnboardingPhaseType? = null,
    val flowType: BeneficiaryOnboardingFlowType? = null,
    val viewUpdatedAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0
) : Model, PersonReference
