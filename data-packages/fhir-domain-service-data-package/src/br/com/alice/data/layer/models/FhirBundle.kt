package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.common.core.extensions.toZonedDateTime
import br.com.alice.common.logging.logger
import br.com.alice.common.models.ExternalHealthInformation
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.util.UUID

data class FhirBundle(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val externalId: String,
    val bundleType: String,
    val encounter: FhirEncounter?,
    val appointments: List<FhirAppointment> = emptyList(),
    val practitioners: List<FhirPractitioner> = emptyList(),
    val observations: List<FhirObservation> = emptyList(),
    val diagnosticReports: List<FhirSimpleDiagnosticReport> = emptyList(),
    val procedures: List<FhirProcedure> = emptyList(),
    val conditions: List<FhirCondition> = emptyList(),
    val allergyIntolerances: List<FhirAllergyIntolerance> = emptyList(),
    val medicationStatements: List<FhirMedicationStatement> = emptyList(),
    val riskAssessments: List<FhirRiskAssessment> = emptyList(),
    val clinicalImpressions: List<FhirClinicalImpression> = emptyList(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val provider: ProviderIntegration,
    val version: Int = 0,
) : Model, HealthInformation, PersonReference, ExternalHealthInformation {

    fun getEncounterStartDate() = encounter?.period?.start?.toSafeTimeZone()?.toLocalDateTime() ?: createdAt
    fun getEncounterEndDate() = encounter?.period?.end?.toSafeTimeZone()?.toLocalDateTime() ?: createdAt

    private fun getPractitionerNames() = encounter
        ?.participant?.mapNotNull { it.individual?.display }
        ?.joinToString(", ")

    fun getConditionNotes() = conditions
        ?.mapNotNull { it.note }
        ?.flatten()
        ?.filter { isValid(it.text) }
        ?.joinToString(" \n") { it.text } ?: ""

    fun description() = """
        <p><b>Nome do Médico:</b> ${getPractitionerNames()}</p>
        <p><b>Data e hora de chegada:<b> ${getEncounterStartDate().toBrazilianDateTimeFormat()}</p>
        <p><b>Data e hora de saída:</b> ${getEncounterEndDate().toBrazilianDateTimeFormat()}</p>
    """.trimIndent()

    private fun String.toSafeTimeZone(): ZonedDateTime? =
        try {
            this.toZonedDateTime()
        } catch (ex: Exception) {
            logger.error("error to parse for DateTime", ex)
            null
        }
}

private fun isValid(text: String?): Boolean =
    text?.uppercase() !in listOf("[NULL]", "[NULL,NULL]", "[NULL, NULL]", "NULL", null)

data class FhirBundleResource(
    val resource: FhirResource,
) : JsonSerializable
