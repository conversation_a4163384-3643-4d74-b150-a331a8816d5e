package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class HospitalSummaryHistory(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val providerIntegration: ProviderIntegration,
    val externalId: String? = null,
    val unit: String? = null,
    val eventId: UUID,
    val eventType: SummaryHistoryEventType = SummaryHistoryEventType.SAVE_FHIR_DOCUMENT,
    val eventValue: String? = null,
    val version: Int = 0,
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val createdAt: LocalDateTime = LocalDateTime.now()
) : Model, PersonReference

enum class SummaryHistoryEventType {
    NOTIFICATION_ADMITTED,
    NOTIFICATION_DISCHARGE,
    SAVE_FHIR_DOCUMENT,
    SAVE_DISCHARGE_SUMMARY,
    ENCOUNTER_DISCHARGED_AT,
    SUMMARY_MATCH_WITH_TERTIARY,
    SUMMARY_MATCH_WITHOUT_TERTIARY,
    SEND_DATA_TO_PROVIDER,
    ERROR_SEND_DATA_TO_PROVIDER,
    ERROR_CONVERT_TO_DISCHARGE_SUMMARY,
    ERROR_GET_DATA_AT_PROVIDER
}
