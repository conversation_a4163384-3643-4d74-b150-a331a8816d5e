package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.ExternalHealthInformation
import br.com.alice.common.models.HealthInformation
import java.time.LocalDateTime
import java.util.UUID

data class FhirDiagnosticReport(
    override val personId: PersonId,
    override val id: UUID = RangeUUID.generate(),
    val identifier: List<FhirIdentifier> = emptyList(),
    val externalId: String,
    val provider: String,
    val meta: FhirMeta?,
    val implicitRules: String?,
    val language: String?,
    val status: String,
    val code: FhirCodeableConcept,
    val categories: List<FhirCodeableConcept> = emptyList(),
    val subject: FhirReference,
    val issued: String?,
    val performers: List<FhirReference> = emptyList(),
    val resultsInterpreter: List<FhirReference> = emptyList(),
    val results: List<FhirReference> = emptyList(),
    val imagingStudies: List<FhirReference> = emptyList(),
    val conclusion: String?,
    val conclusionCodes: List<FhirCodeableConcept> = emptyList(),
    val presentedForms: List<FhirAttachment> = emptyList(),
    val patients: List<FhirPatient> = emptyList(),
    val observations: List<FhirObservation> = emptyList(),
    val servicesRequest: List<FhirServiceRequest> = emptyList(),
    val coverages: List<FhirCoverage> = emptyList(),
    val practitioners: List<FhirPractitioner> = emptyList(),
    val medias: List<FhirMedia> = emptyList(),
    val encounters: List<FhirEncounter> = emptyList(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0,
) : Model, HealthInformation, PersonReference, ExternalHealthInformation

data class FhirSimpleDiagnosticReport(
    override val id: String?,
    override val meta: FhirMeta?,
    override val implicitRules: String?,
    override val language: String?,
    override val resourceType: String,
    val status: String? = null,
    val code: FhirCodeableConcept?,
    val category: FhirCodeableConcept?,
    val subject: FhirReference?,
    val context: FhirReference?,
    val effectiveDateTime: String?,
    val effectivePeriod: String?,
    val issued: String?,
    val performer: List<FhirReference>? = emptyList(),
    val resultsInterpreter: List<FhirReference>? = emptyList(),
    val result: List<FhirReference>? = emptyList(),
    val imagingStudy: List<FhirReference>? = emptyList(),
    val conclusion: String?,
    val conclusionCode: List<FhirCodeableConcept>? = emptyList(),
    val codedDiagnosis: List<FhirCodeableConcept>? = emptyList(),
    val presentedForm: List<FhirAttachment>? = emptyList()
) : FhirResource

data class FhirSimpleDiagnosticReportNewVersion(
    override val id: String?,
    override val meta: FhirMeta?,
    override val implicitRules: String?,
    override val language: String?,
    override val resourceType: String,
    val status: String?,
    val code: FhirCodeableConcept,
    val category: List<FhirCodeableConcept>? = emptyList(),
    val subject: FhirReference?,
    val issued: String?,
    val performer: List<FhirReference>? = emptyList(),
    val resultsInterpreter: List<FhirReference>? = emptyList(),
    val result: List<FhirReference>? = emptyList(),
    val imagingStudy: List<FhirReference>? = emptyList(),
    val conclusion: String?,
    val conclusionCode: List<FhirCodeableConcept>? = emptyList(),
    val presentedForm: List<FhirAttachment>? = emptyList()
) : FhirResource
