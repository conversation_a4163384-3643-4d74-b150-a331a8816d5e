package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class ProviderReadTracking(
    override val id: UUID = RangeUUID.generate(),
    val provider: ProviderIntegration,
    override val personId: PersonId,
    val requestedData: RequestedData,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0
) : Model, PersonReference

data class RequestedData(
    val from: LocalDateTime,
    val to: LocalDateTime
) : JsonSerializable
