package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.builders.MembershipCardBuilder
import br.com.alice.api.ehr.controllers.model.AccreditedNetworkDetailsTransport
import br.com.alice.api.ehr.controllers.model.AccreditedNetworkResponse
import br.com.alice.api.ehr.controllers.model.Address
import br.com.alice.api.ehr.controllers.model.AgeGroup
import br.com.alice.api.ehr.controllers.model.DisclaimerCallOut
import br.com.alice.api.ehr.controllers.model.FilterType
import br.com.alice.common.Brand
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.toCustomFormat
import br.com.alice.coverage.br.com.alice.coverage.converter.HealthProfessionalConsolidatedLightConverter.toConsolidatedType
import br.com.alice.coverage.model.accredited_network.response.ConsolidatedProviderLightTransport
import br.com.alice.coverage.model.accredited_network.response.ConsolidatedSpecialistLightTransport
import br.com.alice.data.layer.models.CassiSpecialist
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PhoneNumber
import br.com.alice.data.layer.models.PhoneType
import br.com.alice.data.layer.models.Product
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.data.layer.models.StructuredAddress
import java.time.LocalDate
import java.util.UUID

fun ConsolidatedSpecialistLightTransport.toAccreditedNetworkResponse(
    subSpecialtiesMap: Map<UUID, MedicalSpecialty>
): AccreditedNetworkResponse =
    AccreditedNetworkResponse(
        id = id,
        name = name,
        address = address?.toAddress(),
        imageUrl = imageUrl,
        type = type,
        subSpecialties = subSpecialtyIds.mapNotNull { subSpecialtiesMap[it]?.name }
    )

fun ConsolidatedProviderLightTransport.toAccreditedNetworkResponse() = AccreditedNetworkResponse(
    id = id,
    name = name,
    address = address?.toAddress(),
    imageUrl = imageUrl,
    type = type,
)

fun FilterType.toProviderConsolidatedType(ageGroup: AgeGroup?) = when (this) {
    FilterType.HOSPITAL -> getHospitalType(ageGroup)
    FilterType.LABORATORY -> ConsolidatedAccreditedNetworkType.LABORATORY
    FilterType.EMERGENCY_UNITY -> getEmergencyType(ageGroup)
    FilterType.MATERNITY -> ConsolidatedAccreditedNetworkType.MATERNITY
    FilterType.SPECIALIST_AND_CLINICAL -> throw IllegalArgumentException("FilterType.SPECIALIST_AND_CLINICAL is not a ProviderUnit.Type")
}

fun CassiSpecialist.toAccreditedNetworkDetailsTransport(
    member: Member,
    specialties: List<AccreditedNetworkDetailsTransport.Specialty>
) = AccreditedNetworkDetailsTransport(
    id = id,
    type = ConsolidatedAccreditedNetworkType.CASSI_SPECIALIST,
    title = ConsolidatedAccreditedNetworkType.CASSI_SPECIALIST.title,
    name = name,
    providerImageUrl = imageUrl,
    council = council.toString(),
    specialties = specialties,
    membershipCard = MembershipCardBuilder.buildCassiMembershipCard(member),
    addresses = address.toProviderDetailsAddress(),
    phoneNumbers = phones.toPhoneNumber(),
    typeOfService = appointmentTypes.specialistAppointmentTypeToTypeOfService(),
    disclaimer = deAccreditationDate?.let { buildDisclaimerCallOut(member.brand ?: Brand.ALICE, it) }
)

fun HealthProfessional.toAccreditedNetworkDetailsTransport(
    person: Person,
    member: Member,
    product: Product,
    specialties: List<AccreditedNetworkDetailsTransport.Specialty>
) = AccreditedNetworkDetailsTransport(
    id = id,
    type = type.toConsolidatedType(),
    title = type.toConsolidatedType().title,
    name = name,
    providerImageUrl = imageUrl,
    council = council.toString(),
    specialties = specialties,
    membershipCard = MembershipCardBuilder.buildAliceMembershipCard(person, member, product),
    addresses = addressesStructured?.toProviderDetailsAddress(),
    phoneNumbers = phones.toPhoneNumber(),
    typeOfService = appointmentTypes.specialistAppointmentTypeToTypeOfService(),
    providerInformation = AccreditedNetworkDetailsTransport.ProviderInformation(
        imageUrl = imageUrl,
        curiosity = curiosity,
        education = education,
        qualifications = qualifications.map { it.description }
    ),
    disclaimer = deAccreditationDate?.let { buildDisclaimerCallOut(member.brand ?: Brand.ALICE, it) }
)

fun ProviderUnit.toAccreditedNetworkDetailsTransport(
    person: Person,
    member: Member,
    product: Product,
    specialties: List<AccreditedNetworkDetailsTransport.Specialty>
) = AccreditedNetworkDetailsTransport(
    id = id,
    type = type.toType(),
    title = type.toType().title,
    name = name,
    providerImageUrl = imageUrl,
    council = null,
    specialties = specialties,
    membershipCard = MembershipCardBuilder.buildMembershipCardByOrigin(
        contractOrigin,
        person,
        member,
        product
    ),
    addresses = listOfNotNull(address).toProviderDetailsAddress(),
    phoneNumbers = phones.toPhoneNumber(),
    disclaimer = deAccreditationDate?.let { buildDisclaimerCallOut(member.brand ?: Brand.ALICE, it) }
)

private fun getHospitalType(ageGroup: AgeGroup?) = when (ageGroup) {
    AgeGroup.CHILDREN -> ConsolidatedAccreditedNetworkType.HOSPITAL_CHILDREN
    else -> ConsolidatedAccreditedNetworkType.HOSPITAL
}

private fun getEmergencyType(ageGroup: AgeGroup?) = when (ageGroup) {
    AgeGroup.CHILDREN -> ConsolidatedAccreditedNetworkType.EMERGENCY_UNITY_CHILDREN
    else -> ConsolidatedAccreditedNetworkType.EMERGENCY_UNITY
}

private fun StructuredAddress.toAddress() = Address(
    fullAddress = formattedAddress(),
    lat = latitude.orEmpty(),
    lng = longitude.orEmpty()
)

private fun ProviderUnit.Type.toType() = when (this) {
    ProviderUnit.Type.HOSPITAL -> ConsolidatedAccreditedNetworkType.HOSPITAL
    ProviderUnit.Type.HOSPITAL_CHILDREN -> ConsolidatedAccreditedNetworkType.HOSPITAL_CHILDREN
    ProviderUnit.Type.LABORATORY -> ConsolidatedAccreditedNetworkType.LABORATORY
    ProviderUnit.Type.ALICE_HOUSE -> ConsolidatedAccreditedNetworkType.ALICE_HOUSE
    ProviderUnit.Type.EMERGENCY_UNITY -> ConsolidatedAccreditedNetworkType.EMERGENCY_UNITY
    ProviderUnit.Type.EMERGENCY_UNITY_CHILDREN -> ConsolidatedAccreditedNetworkType.EMERGENCY_UNITY_CHILDREN
    ProviderUnit.Type.MATERNITY -> ConsolidatedAccreditedNetworkType.MATERNITY
    ProviderUnit.Type.CLINICAL -> ConsolidatedAccreditedNetworkType.CLINICAL
    ProviderUnit.Type.CLINICAL_COMMUNITY -> ConsolidatedAccreditedNetworkType.CLINICAL_COMMUNITY
    ProviderUnit.Type.VACCINE -> ConsolidatedAccreditedNetworkType.VACCINE
    else -> throw InvalidArgumentException("Unsupported ProviderUnit.Type: $this")
}

private const val DEFAULT_ADDRESS_PREFIX = "Endereço"
private const val DEFAULT_PHONE_PREFIX = "Telefone"

private fun List<PhoneNumber>.toPhoneNumber() = this.mapIndexed { idx, phone ->
    val type = phone.type.toProviderPhoneType()
    val preformattedPhone = phone.phone.replace(Regex("\\s|\\(|\\)|-"), "")

    AccreditedNetworkDetailsTransport.PhoneNumber(
        label = phone.title ?: "$DEFAULT_PHONE_PREFIX ${idx + 1}",
        phoneNumber = phone.phone,
        type = type,
        phoneUrl = "tel:${preformattedPhone}",
    )
}

private fun PhoneType?.toProviderPhoneType(): AccreditedNetworkDetailsTransport.PhoneNumber.Type = when (this) {
    PhoneType.PHONE -> AccreditedNetworkDetailsTransport.PhoneNumber.Type.PHONE
    PhoneType.MOBILE -> AccreditedNetworkDetailsTransport.PhoneNumber.Type.MOBILE
    PhoneType.WHATSAPP -> AccreditedNetworkDetailsTransport.PhoneNumber.Type.WHATSAPP
    null -> AccreditedNetworkDetailsTransport.PhoneNumber.Type.PHONE
}

private fun List<StructuredAddress>.toProviderDetailsAddress() = this.mapIndexed { idx, address ->
    AccreditedNetworkDetailsTransport.Address(
        label = "$DEFAULT_ADDRESS_PREFIX ${idx + 1}",
        address = address.formattedAddress(),
    )
}

private fun List<SpecialistAppointmentType>.specialistAppointmentTypeToTypeOfService() = this.map {
    when (it) {
        SpecialistAppointmentType.PRESENTIAL -> AccreditedNetworkDetailsTransport.TypeOfService.IN_PERSON
        SpecialistAppointmentType.REMOTE -> AccreditedNetworkDetailsTransport.TypeOfService.REMOTE
    }
}

private fun buildDisclaimerCallOut(
    brand: Brand,
    deAccreditationDate: LocalDate
) = if (brand.isDuquesa()) {
    DisclaimerCallOut(
        title = "Disponível até ${deAccreditationDate.toCustomFormat("dd/MM")}",
        body = "A partir do dia ${deAccreditationDate.toCustomFormat("dd/MM")} parte da rede credenciada de especialistas QSaúde será substituída pelos Médicos Especialistas da Alice.",
        variant = DisclaimerCallOut.Variant.INFORMATION
    )
} else {
    DisclaimerCallOut(
        title = "Disponível até ${deAccreditationDate.toCustomFormat("dd/MM")}",
        body = "A partir do dia ${deAccreditationDate.toCustomFormat("dd/MM")} parte da rede credenciada de especialistas será substituída pelos profissionais da rede credenciada da Alice.",
        variant = DisclaimerCallOut.Variant.INFORMATION
    )
}
