package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.ControllerTestHelper
import br.com.alice.api.ehr.ServiceConfig
import br.com.alice.api.ehr.services.AuthService
import br.com.alice.authentication.Authenticator
import br.com.alice.authentication.sendSignInEmailLink
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.ehr.client.MailerService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class AuthControllerTest : ControllerTestHelper() {
    private val authService: AuthService = mockk()
    private val mailerService: MailerService = mockk()
    private val staffService: StaffService = mockk()

    private val controller = AuthController(
        authService,
        mailerService,
        staffService
    )

    private val request = SignInRequest(idToken = idToken)

    private val ehrSystem = SystemRequest(
        system = SystemTypeRequest.EHR,
        url = "https://www.saude.com.br"
    )
    private val eita = SystemRequest(
        system = SystemTypeRequest.EITA,
        url = "https://www.eita.com.br"
    )
    private val now = LocalDateTime.of(2021, 1, 1, 0, 0)

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { controller }

        mockkObject(Authenticator)

        every { Authenticator.generateRootServiceToken(any()) } returns "environmentToken"
        every { Authenticator.generateCustomToken(any(), any<String>()) } returns "customToken"
        every { Authenticator.generateCustomToken(any(), "Unauthenticated") } returns "SomeToken"

        mockkStatic(::sendSignInEmailLink)
        every { sendSignInEmailLink(any(), any()) } returns "https://some.link"
    }

    @Test
    fun `should return 200 when sign in is successful`() {
        coEvery { authService.signIn(idToken) } returns true

        authenticatedAs(idToken, staffTest) {
            post(to = "/ehr/signIn", body = request) { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `should return 401 when sign in is not successful`() {
        coEvery { authService.signIn(idToken) } returns false

        authenticatedAs(idToken, staffTest) {
            post(to = "/ehr/signIn", body = request) { response ->
                assertThat(response).isUnauthorized()
            }
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = StaffType::class,
        names = ["COMMUNITY_SPECIALIST", "PARTNER_HEALTH_PROFESSIONAL", "HEALTH_PROFESSIONAL","PITAYA"]
    )
    fun `sendEmailPin should send email pin when ehr system`(value: StaffType) {
        mockLocalDateTime(now) {
            val linkSendDate = now.toSaoPauloTimeZone().toBrazilianDateTimeFormat()
            val signInRequest = FirebaseSignInEmailPinRequest(
                email = "<EMAIL>",
                redirectToUrls = listOf(
                    ehrSystem,
                    eita
                )
            )
            val staff = staff.copy(type = value)
            val expectedVariables = mapOf(
                "pin" to "123456",
                "recipient" to staff.firstName,
                "app_name" to "EHR",
                "email" to staff.email,
                "send_date" to linkSendDate
            )
            coEvery {
                staffService.findByEmail(signInRequest.email)
            } returns staff.success()

            coEvery {
                authService.generatedPinAuthentication(staff.email, ehrSystem.url)
            } returns "123456".success()

            coEvery {
                mailerService.sendEmail(
                    recipientName = staff.fullName,
                    recipientEmail = staff.email,
                    replaceVariables = expectedVariables,
                    templateName = ServiceConfig.EmailTemplate.firebaseSignInMagicNumberTemplate
                )
            } returns "email_receipt_id".success()

            post(to = "ehr/auth/pin", body = signInRequest) { response ->
                assertThat(response).isOKWithData("email_receipt_id")

                coVerifyOnce { staffService.findByEmail(any()) }
                coVerifyOnce { authService.generatedPinAuthentication(any(), any()) }
                coVerifyOnce { mailerService.sendEmail(any(), any(), any(), any()) }
            }
        }
    }

    @ParameterizedTest
    @EnumSource(value = StaffType::class, names = ["HEALTH_ADMINISTRATIVE", "EXTERNAL_PAID_HEALTH_PROFESSIONAL"])
    fun `sendEmailPin should send email pin when eita system`(type: StaffType) {
        mockLocalDateTime(now) {
            val linkSendDate = now.toSaoPauloTimeZone().toBrazilianDateTimeFormat()
            val staff = staff.copy(type = type)
            val signInRequest = FirebaseSignInEmailPinRequest(
                email = "<EMAIL>",
                redirectToUrls = listOf(
                    ehrSystem,
                    eita
                )
            )
            val expectedVariables = mapOf(
                "pin" to "123456",
                "recipient" to staff.firstName,
                "app_name" to "EITA",
                "email" to staff.email,
                "send_date" to linkSendDate
            )
            coEvery {
                staffService.findByEmail(signInRequest.email)
            } returns staff.success()

            coEvery {
                authService.generatedPinAuthentication(staff.email, eita.url)
            } returns "123456".success()

            coEvery {
                mailerService.sendEmail(
                    recipientName = staff.fullName,
                    recipientEmail = staff.email,
                    replaceVariables = expectedVariables,
                    templateName = ServiceConfig.EmailTemplate.firebaseSignInMagicNumberTemplate
                )
            } returns "email_receipt_id".success()

            post(to = "ehr/auth/pin", body = signInRequest) { response ->
                assertThat(response).isOKWithData("email_receipt_id")

                coVerifyOnce { staffService.findByEmail(any()) }
                coVerifyOnce { authService.generatedPinAuthentication(any(), any()) }
                coVerifyOnce { mailerService.sendEmail(any(), any(), any(), any()) }
            }
        }
    }

    @ParameterizedTest
    @EnumSource(value = StaffType::class, names = ["TALK_CIRCLE", "GENERAL_SPECIALTY"])
    fun `sendEmailPin should return 400 when user type is not allow`(type: StaffType) {
        val staff = staff.copy(type = type)
        val signInRequest = FirebaseSignInEmailPinRequest(
            email = "<EMAIL>",
            redirectToUrls = listOf(
                ehrSystem,
                eita
            )
        )

        coEvery {
            staffService.findByEmail(signInRequest.email)
        } returns staff.success()

        post(to = "ehr/auth/pin", body = signInRequest) { response ->
            assertThat(response).isBadRequest()
            coVerifyOnce { staffService.findByEmail(any()) }
        }
    }

    @Test
    fun `sendEmailPin should not send email pin when staff not found`() {
        val signInRequest = FirebaseSignInEmailLinkRequest(
            email = "<EMAIL>",
            url = "https://www.alice.com.br"
        )
        coEvery { staffService.findByEmail(signInRequest.email) } returns NotFoundException("not_found").failure()

        post(to = "/auth/pin", body = signInRequest) { response ->
            assertThat(response).isNotFound()
        }
    }

    @Test
    fun `verifyAccessPin should verify access pin`() {
        val pin = "123456"
        val email = "<EMAIL>"
        val urlExpected = "https://www.alice.com.br"
        val expectedResponse = UrlResponse(urlExpected)
        coEvery {
            authService.getUrl(pin, email)
        } returns urlExpected.success()

        get(url = "ehr/auth/pin?email=$email&pin=$pin") { response ->
            assertThat(response).isOKWithData(expectedResponse)
        }
    }

    @Test
    fun `verifyAccessPin should return BadRequest when missing pin`() {
        val pin = "123456"
        get(url = "ehr/auth/pin?pin=$pin") { response ->
            assertThat(response).isBadRequest()
        }
    }

    @Test
    fun `verifyAccessPin should return BadRequest when missing email`() {
        val email = "<EMAIL>"
        get(url = "ehr/auth/pin?email=$email") { response ->
            assertThat(response).isBadRequest()
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = StaffType::class,
        names = ["COMMUNITY_SPECIALIST", "PARTNER_HEALTH_PROFESSIONAL", "HEALTH_PROFESSIONAL","PITAYA"]
    )
    fun `should redirect return no content when system is EHR`(value: StaffType) {
        val staff = staff.copy(type = value)
        val request = ShouldRedirectRequest(
            redirectToUrls = listOf(
                ehrSystem,
                eita
            )
        )
        coEvery { staffService.get(staff.id) } returns staff.success()

        authenticatedAs(idToken, staffTest) {
            post(to = "ehr/check_redirect", body = request) { response ->
                assertThat(response).isNoContent()
            }
        }
        coVerifyOnce { staffService.get(any()) }
        coVerifyNone { sendSignInEmailLink(any(), any()) }
    }

    @ParameterizedTest
    @EnumSource(value = StaffType::class, names = ["HEALTH_ADMINISTRATIVE", "EXTERNAL_PAID_HEALTH_PROFESSIONAL"])
    fun `should redirect return link when system is EITA`(value: StaffType) {
        val request = ShouldRedirectRequest(
            redirectToUrls = listOf(
                ehrSystem,
                eita
            )
        )
        val staff = staff.copy(type = value)

        coEvery { staffService.get(staff.id) } returns staff.success()
        mockkStatic(::sendSignInEmailLink)
        every { sendSignInEmailLink(staff.email, eita.url) } returns eita.url

        authenticatedAs(idToken, staffTest) {
            post(to = "ehr/check_redirect", body = request) { response ->
                assertThat(response).isOKWithData(UrlResponse(eita.url))
            }
        }
        coVerifyOnce { staffService.get(any()) }
        verifyOnce { sendSignInEmailLink(any(), any()) }
    }
}
