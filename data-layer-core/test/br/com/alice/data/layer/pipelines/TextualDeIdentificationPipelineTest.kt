package br.com.alice.data.layer.pipelines

import br.com.alice.authentication.TokenVerifier
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.data.layer.authorization.AuthorizationService
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelHistory
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelType
import br.com.alice.data.layer.models.HealthPlan
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.pipelines.context.ContextService
import br.com.alice.data.layer.repositories.JdbiRepository
import br.com.alice.data.layer.repositories.JdbiRepositoryFactory
import br.com.alice.data.layer.services.PersonTokenService
import br.com.alice.data.layer.services.PersonTokenServiceImpl
import br.com.alice.data.layer.services.ReplicationLagService
import br.com.alice.data.layer.tables.AppointmentTable
import br.com.alice.data.layer.tables.ChannelHistoryTable
import br.com.alice.data.layer.tables.HealthPlanTable
import br.com.alice.data.layer.tables.PersonTable
import br.com.alice.data.layer.tables.StaffTable
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class TextualDeIdentificationPipelineTest : DataServiceTestHelper() {

    private val authorizationService: AuthorizationService = mockk()
    private val tokenVerifier: TokenVerifier = mockk()
    private val personTokenService = PersonTokenServiceImpl(tokenJdbi) as PersonTokenService
    private val replicationLagService: ReplicationLagService = mockk()

    private val factory = DatabasePipelineFactory(mainJdbi, mainJdbiReplica, authorizationService,
        tokenVerifier, personTokenService, ContextService(tokenVerifier), replicationLagService, true)

    private val personDataPipeline = factory.get(PersonModel::class, PersonTable::class, false)
    private val appointmentDataPipeline = factory.get(Appointment::class, AppointmentTable::class, false)
    private val staffDataPipeline = factory.get(StaffModel::class, StaffTable::class, false)
    private val healthPlanPipeline = factory.get(HealthPlan::class, HealthPlanTable::class, false)
    private val channelHistoryPipeline = factory.get(ChannelHistory::class, ChannelHistoryTable::class, false)

    private val repository = JdbiRepository(mainJdbi, HealthPlanTable::class)
    private val channelHistoryRepository = JdbiRepository(mainJdbi, ChannelHistoryTable::class)
    private val personRepository = JdbiRepository(mainJdbi, PersonTable::class)

    @BeforeTest
    fun setup() {
        val factory = JdbiRepositoryFactory(mainJdbi)
        factory.get(PersonTable::class).truncate()
        factory.get(AppointmentTable::class).truncate()
        factory.get(StaffTable::class).truncate()
        factory.get(HealthPlanTable::class).truncate()
        factory.get(ChannelHistoryTable::class).truncate()
    }

    @Test
    fun `de-identify text on save and identify on get`() = runBlocking<Unit> {
        val person = DataLayerTestModelFactory.buildPerson()
        personTokenService.createForPersonId(person.id).get()
        personDataPipeline.add(person)

        val staff = DataLayerTestModelFactory.buildStaff()
        staffDataPipeline.add(staff)

        val appointment = TestModelFactory.buildAppointment(personId = person.id, staffId = staff.id).copy(updatedBy = UpdatedBy("staff", staff.id.toString(), "aaa"))

        appointmentDataPipeline.add(appointment)

        val healthPlan = TestModelFactory.buildHealthPlan(personId = person.id).copy(
            description = "Oi José, aqui você vai conseguir ver todas suas tarefas para ter mais saúde."
        )
        val result = healthPlanPipeline.add(healthPlan)

        val retrievedHealthPlan = result.get()
        assertThat(retrievedHealthPlan.description).isEqualTo("Oi José, aqui você vai conseguir ver todas suas tarefas para ter mais saúde.")

        val healthPlanTable = repository.get(retrievedHealthPlan.id)!!
        assertThat(healthPlanTable.description).isEqualTo("Oi @firstname, aqui você vai conseguir ver todas suas tarefas para ter mais saúde.")

        val list = healthPlanPipeline.findByQuery(Query()).get()
        assertThat(list.size).isEqualTo(1)
        val retrievedHealthPlan2 = list.first()
        assertThat(retrievedHealthPlan2.description).isEqualTo("Oi José, aqui você vai conseguir ver todas suas tarefas para ter mais saúde.")
    }

    @Test
    fun `#de-identify text on save and identify on get for lists of strings`() = runBlocking<Unit> {
        val person = DataLayerTestModelFactory.buildPerson(nationalId = "012.345.678.90", email = "<EMAIL>")
        personTokenService.createForPersonId(person.id).get()
        personDataPipeline.add(person)

        val channelHistory = ChannelHistory(
            personId = person.id,
            channelId = "channelId",
            type = ChannelType.CHAT,
            status = ChannelStatus.ACTIVE,
            tags = listOf("Tag 1"),
            action = ChannelChangeAction.CREATE_CHAT
        )

        val resultAdded = channelHistoryPipeline.add(channelHistory)
        assertThat(resultAdded.get().tags.first()).isEqualTo("Tag 1")

        val resultRepo = channelHistoryRepository.get(channelHistory.id)!!
        assertThat(resultRepo.tags.first()).isEqualTo("Tag 1")

        val resultGet = channelHistoryPipeline.findByQuery(Query()).get().first()
        assertThat(resultGet.tags.first()).isEqualTo("Tag 1")
    }

    @Test
    fun `#de-identify integrated with anonymized service`() = runBlocking<Unit> {
        val person = DataLayerTestModelFactory.buildPerson(nationalId = "012.345.678.90", email = "<EMAIL>")
        personTokenService.createForPersonId(person.id).get()
        val addedPerson = personDataPipeline.add(person).get()

        val staff = DataLayerTestModelFactory.buildStaff()
        staffDataPipeline.add(staff)

        val appointment = TestModelFactory.buildAppointment(personId = person.id, staffId = staff.id)
            .copy(updatedBy = UpdatedBy("staff", staff.id.toString(), "aaa"))
        appointmentDataPipeline.add(appointment)

        val healthPlan = TestModelFactory.buildHealthPlan(personId = person.id).copy(
            description = "Oi José da Silva (Zé), aqui você vai conseguir ver todas suas tarefas para ter mais saúde."
        )
        healthPlanPipeline.add(healthPlan)

        val personTable = ConverterExtension.converter<PersonModel, PersonTable>(personTokenService).convert(addedPerson)
            .copy(anonymized = true)
        personRepository.update(personTable)

        val retrievedHealthPlan = healthPlanPipeline.get(healthPlan.id).get()
        assertThat(retrievedHealthPlan.description).isEqualTo(
            "Oi ANONIMIZADO ANONIMIZADO (ANONIMIZADO), aqui você vai conseguir ver todas suas tarefas para ter mais saúde."
        )

        val healthPlanTable = repository.get(retrievedHealthPlan.id)!!
        assertThat(healthPlanTable.description).isEqualTo(
            "Oi @firstname @lastname (@nickname), aqui você vai conseguir ver todas suas tarefas para ter mais saúde."
        )

        val list = healthPlanPipeline.findByQuery(Query()).get()
        assertThat(list.size).isEqualTo(1)
        val retrievedHealthPlan2 = list.first()
        assertThat(retrievedHealthPlan2.description).isEqualTo(
            "Oi ANONIMIZADO ANONIMIZADO (ANONIMIZADO), aqui você vai conseguir ver todas suas tarefas para ter mais saúde."
        )
    }

}
