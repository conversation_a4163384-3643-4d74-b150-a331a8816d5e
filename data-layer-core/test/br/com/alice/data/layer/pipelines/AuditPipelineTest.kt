package br.com.alice.data.layer.pipelines

import br.com.alice.authentication.Claims
import br.com.alice.authentication.RootService
import br.com.alice.authentication.TokenVerifier
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.convertTo
import br.com.alice.common.core.exceptions.DatabaseException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.data.layer.MEMBER_WANNABE_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.AuthorizationService
import br.com.alice.data.layer.exceptions.NotFoundException
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.models.TestUpdatedByReferenceAndSoftDeletable
import br.com.alice.data.layer.pipelines.context.ContextService
import br.com.alice.data.layer.repositories.JdbiRepository
import br.com.alice.data.layer.services.PersonTokenService
import br.com.alice.data.layer.services.PersonTokenServiceImpl
import br.com.alice.data.layer.services.ReplicationLagService
import br.com.alice.data.layer.tables.TestUpdatedByReferenceAndSoftDeletableTable
import com.github.kittinunf.result.isSuccess
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertFailsWith

class AuditPipelineTest: DataServiceTestHelper() {

    private val authorizationService: AuthorizationService = mockk()
    private val tokenVerifier: TokenVerifier = mockk()
    private val contextService: ContextService = mockk()
    private val personTokenService = PersonTokenServiceImpl(tokenJdbi) as PersonTokenService
    private val replicationLagService: ReplicationLagService = mockk()

    private val factory = DatabasePipelineFactory(mainJdbi, mainJdbiReplica, authorizationService,
        tokenVerifier, personTokenService, contextService, replicationLagService, true)

    private val personDataPipeline =
        factory.get(TestUpdatedByReferenceAndSoftDeletable::class, TestUpdatedByReferenceAndSoftDeletableTable::class, false)

    private val repository = JdbiRepository(mainJdbi, TestUpdatedByReferenceAndSoftDeletableTable::class)

    @BeforeTest
    fun setup() {
        clearAllMocks()
        repository.truncate()
    }

    @Test
    fun `#retrieve should return updatedBy as is when it is not person`() = runBlocking {
        val staffId = RangeUUID.generate()
        val updatedBy = UpdatedBy(
            userId = staffId.toString(),
            userType = "Staff",
            environmentName = MEMBER_WANNABE_API_ROOT_SERVICE_NAME
        )
        val testResult = TestUpdatedByReferenceAndSoftDeletableTable(
            updatedBy = updatedBy
        )
        repository.add(testResult)

        val expected = testResult.copy(updatedBy = updatedBy).convertTo(TestUpdatedByReferenceAndSoftDeletable::class)

        val getResult = personDataPipeline.get(testResult.id)
        val findByQueryResult = personDataPipeline.findByQuery(Query(
            where = Predicate.eq(Field.UUIDField("id"), testResult.id)
        ))
        val findAuthByQueryResult = personDataPipeline.findAuthorizedByQuery(
            Query(
                where = Predicate.eq(Field.UUIDField("id"), testResult.id)
            )
        )

        assertThat(getResult).isSuccessWithDataIgnoringGivenFields(expected, "createdAt", "updatedAt")
        assertThat(findByQueryResult).isSuccessWithDataIgnoringGivenFields(listOf(expected), "createdAt", "updatedAt")
        assertThat(findAuthByQueryResult).isSuccessWithDataIgnoringGivenFields(listOf(expected), "createdAt", "updatedAt")
    }

    @Test
    fun `#retrieve should return updatedBy anonymized when it was saved anonymized`() = runBlocking {
        val person = DataLayerTestModelFactory.buildPerson()
        val token = personTokenService.createForPersonId(person.id).get()

        val testResult = TestUpdatedByReferenceAndSoftDeletableTable(
            updatedBy = UpdatedBy(
                userId = token.personId.id.toString(),
                userType = "Person",
                environmentName = MEMBER_WANNABE_API_ROOT_SERVICE_NAME
            )
        )
        repository.add(testResult)

        val updatedBy = UpdatedBy(
            userId = person.id.toString(),
            userType = "Person",
            environmentName = MEMBER_WANNABE_API_ROOT_SERVICE_NAME
        )

        val expected = testResult.copy(updatedBy = updatedBy).convertTo(TestUpdatedByReferenceAndSoftDeletable::class)

        val getResult = personDataPipeline.get(testResult.id)
        val findByQueryResult = personDataPipeline.findByQuery(Query(
            where = Predicate.eq(Field.UUIDField("id"), testResult.id)
        ))
        val findAuthByQueryResult = personDataPipeline.findAuthorizedByQuery(
            Query(
                where = Predicate.eq(Field.UUIDField("id"), testResult.id)
            )
        )

        assertThat(getResult).isSuccessWithDataIgnoringGivenFields(expected, "createdAt", "updatedAt")
        assertThat(findByQueryResult).isSuccessWithDataIgnoringGivenFields(listOf(expected), "createdAt", "updatedAt")
        assertThat(findAuthByQueryResult).isSuccessWithDataIgnoringGivenFields(listOf(expected), "createdAt", "updatedAt")
    }

    @Test
    fun `#retrieve should return updatedBy anonymized`() = runBlocking {
        val person = DataLayerTestModelFactory.buildPerson()
        val token = personTokenService.createForPersonId(person.id).get()

        val testResult = TestUpdatedByReferenceAndSoftDeletableTable(
            updatedBy = UpdatedBy(
                userId = token.personHiToken.id.toString(),
                userType = "Person",
                environmentName = MEMBER_WANNABE_API_ROOT_SERVICE_NAME
            )
        )
        repository.add(testResult)

        val updatedBy = UpdatedBy(
            userId = person.id.toString(),
            userType = "Person",
            environmentName = MEMBER_WANNABE_API_ROOT_SERVICE_NAME
        )

        val expected = testResult.copy(updatedBy = updatedBy).convertTo(TestUpdatedByReferenceAndSoftDeletable::class)

        val getResult = personDataPipeline.get(testResult.id)
        val findByQueryResult = personDataPipeline.findByQuery(Query(
            where = Predicate.eq(Field.UUIDField("id"), testResult.id)
        ))
        val findAuthByQueryResult = personDataPipeline.findAuthorizedByQuery(
            Query(
                where = Predicate.eq(Field.UUIDField("id"), testResult.id)
            )
        )

        assertThat(getResult).isSuccessWithDataIgnoringGivenFields(expected, "createdAt", "updatedAt")
        assertThat(findByQueryResult).isSuccessWithDataIgnoringGivenFields(listOf(expected), "createdAt", "updatedAt")
        assertThat(findAuthByQueryResult).isSuccessWithDataIgnoringGivenFields(listOf(expected), "createdAt", "updatedAt")
    }

    @Test
    fun `#add should anonymize updatedBy when it is person`() = runBlocking {
        val person = DataLayerTestModelFactory.buildPerson()
        val token = personTokenService.createForPersonId(person.id).get()
        val updatedBy = UpdatedBy(
            userId = token.personHiToken.id.toString(),
            userType = "Person",
            environmentName = MEMBER_WANNABE_API_ROOT_SERVICE_NAME
        )

        val testUpdatedByReferenceAndSoftDeletable = TestUpdatedByReferenceAndSoftDeletable()

        val expected = testUpdatedByReferenceAndSoftDeletable.copy(updatedBy = updatedBy)

        coEvery { contextService.getClaims(useInheritedAuthToken = true) } returns Claims("Person", person.id.toString())
        coEvery { contextService.getRootService() } returns RootService(MEMBER_WANNABE_API_ROOT_SERVICE_NAME)

        val addResult = personDataPipeline.add(testUpdatedByReferenceAndSoftDeletable)
        assertThat(addResult).isSuccessWithDataIgnoringGivenFields(expected, "createdAt", "updatedAt")

        coVerifyOnce { contextService.getClaims(useInheritedAuthToken = true) }
        coVerifyOnce { contextService.getRootService() }
    }

    @Test
    fun `#add should save updatedBy with userId as is when it is not person`() = runBlocking {
        val staffId = RangeUUID.generate()

        val updatedBy = UpdatedBy(
            userId = staffId.toString(),
            userType = "Staff",
            environmentName = MEMBER_WANNABE_API_ROOT_SERVICE_NAME
        )

        val testUpdatedByReferenceAndSoftDeletable = TestUpdatedByReferenceAndSoftDeletable()

        val expected = testUpdatedByReferenceAndSoftDeletable.copy(updatedBy = updatedBy)

        coEvery { contextService.getClaims(useInheritedAuthToken = true) } returns Claims("Staff", staffId.toString())
        coEvery { contextService.getRootService() } returns RootService(MEMBER_WANNABE_API_ROOT_SERVICE_NAME)

        val addResult = personDataPipeline.add(testUpdatedByReferenceAndSoftDeletable)
        assertThat(addResult).isSuccessWithDataIgnoringGivenFields(expected, "createdAt", "updatedAt")

        coVerifyOnce { contextService.getClaims(useInheritedAuthToken = true) }
        coVerifyOnce { contextService.getRootService() }
    }

    @Test
    fun `#update should anonymize updatedBy when it is person`() = runBlocking {
        val person = DataLayerTestModelFactory.buildPerson()
        val token = personTokenService.createForPersonId(person.id).get()

        val test = TestUpdatedByReferenceAndSoftDeletableTable()

        val updatedBy = UpdatedBy(
            userId = token.personHiToken.id.toString(),
            userType = "Person",
            environmentName = MEMBER_WANNABE_API_ROOT_SERVICE_NAME
        )

        val toUpdate = TestUpdatedByReferenceAndSoftDeletable(id = test.id)

        val expected = toUpdate.copy(updatedBy = updatedBy)

        repository.add(test)
        coEvery { contextService.getClaims(useInheritedAuthToken = true) } returns Claims("Person", person.id.toString())
        coEvery { contextService.getRootService() } returns RootService(MEMBER_WANNABE_API_ROOT_SERVICE_NAME)

        val updateResult = personDataPipeline.update(toUpdate)
        assertThat(updateResult).isSuccessWithDataIgnoringGivenFields(expected, "createdAt", "updatedAt", "version")

        coVerifyOnce { contextService.getClaims(useInheritedAuthToken = true) }
        coVerifyOnce { contextService.getRootService() }
    }

    @Test
    fun `#update should save updatedBy with userId as is when it is not person`() = runBlocking {
        val staffId = RangeUUID.generate()

        val test = TestUpdatedByReferenceAndSoftDeletableTable()

        val updatedBy = UpdatedBy(
            userId = staffId.toString(),
            userType = "Staff",
            environmentName = MEMBER_WANNABE_API_ROOT_SERVICE_NAME
        )

        val toUpdate = TestUpdatedByReferenceAndSoftDeletable(id = test.id)
        val expected = toUpdate.copy(updatedBy = updatedBy)

        repository.add(test)

        coEvery { contextService.getClaims(useInheritedAuthToken = true) } returns Claims("Staff", staffId.toString())
        coEvery { contextService.getRootService() } returns RootService(MEMBER_WANNABE_API_ROOT_SERVICE_NAME)

        val updateResult = personDataPipeline.update(toUpdate)
        assertThat(updateResult).isSuccessWithDataIgnoringGivenFields(expected, "createdAt", "updatedAt", "version")

        coVerifyOnce { contextService.getClaims(useInheritedAuthToken = true) }
        coVerifyOnce { contextService.getRootService() }
    }

    @Test
    fun `#softDelete should anonymize updatedBy when it is person`() = runBlocking {
        val person = DataLayerTestModelFactory.buildPerson()
        val token = personTokenService.createForPersonId(person.id).get()

        val test = TestUpdatedByReferenceAndSoftDeletableTable()
        repository.add(test)

        coEvery { contextService.getClaims(useInheritedAuthToken = true) } returns Claims("Person", person.id.toString())
        coEvery { contextService.getRootService() } returns RootService(MEMBER_WANNABE_API_ROOT_SERVICE_NAME)

        val softDeleteResult = personDataPipeline.softDelete(TestUpdatedByReferenceAndSoftDeletable(id = test.id))

        assertThat(softDeleteResult.isSuccess()).isTrue()

        val selectResult = mainJdbiReplica.withHandle<Map<String, Any>, DatabaseException> { handle ->
            handle.createQuery("SELECT * FROM test_updated_by_reference_and_soft_deletable WHERE id = \'${test.id}\'")
                .mapToMap().one()
        }
        assertThat(selectResult["deleted_at"]).isNotNull()
        assertThat(selectResult["updated_by"].toString()).
            isEqualTo(
                "{\"user_id\": \"${token.personHiToken.id}\"," +
                " \"user_type\": \"${"Person"}\"," +
                " \"environment_name\": \"${MEMBER_WANNABE_API_ROOT_SERVICE_NAME}\"}"
            )

        coVerifyOnce { contextService.getClaims(useInheritedAuthToken = true) }
        coVerifyOnce { contextService.getRootService() }
    }

    @Test
    fun `#softDelete should save updatedBy with userId as is when it is not person`() = runBlocking {
        val staffId = RangeUUID.generate()

        val test = TestUpdatedByReferenceAndSoftDeletableTable()
        repository.add(test)

        coEvery { contextService.getClaims(useInheritedAuthToken = true) } returns Claims("Staff", staffId.toString())
        coEvery { contextService.getRootService() } returns RootService(MEMBER_WANNABE_API_ROOT_SERVICE_NAME)

        val softDeleteResult = personDataPipeline.softDelete(TestUpdatedByReferenceAndSoftDeletable(id = test.id))

        assertThat(softDeleteResult.isSuccess()).isTrue()

        val selectResult = mainJdbiReplica.withHandle<Map<String, Any>, DatabaseException> { handle ->
            handle.createQuery("SELECT * FROM test_updated_by_reference_and_soft_deletable WHERE id = \'${test.id}\'")
                .mapToMap().one()
        }
        assertThat(selectResult["deleted_at"]).isNotNull()
        assertThat(selectResult["updated_by"].toString()).
        isEqualTo(
            "{\"user_id\": \"${staffId}\"," +
                    " \"user_type\": \"${"Staff"}\"," +
                    " \"environment_name\": \"${MEMBER_WANNABE_API_ROOT_SERVICE_NAME}\"}"
        )

        coVerifyOnce { contextService.getClaims(useInheritedAuthToken = true) }
        coVerifyOnce { contextService.getRootService() }
    }

    @Test
    fun `#update should throw exception when no token found for person`() = runBlocking {
        val person = DataLayerTestModelFactory.buildPerson()

        val test = TestUpdatedByReferenceAndSoftDeletableTable()
        repository.add(test)

        coEvery { contextService.getClaims(useInheritedAuthToken = true) } returns Claims("Person", person.id.toString())
        coEvery { contextService.getRootService() } returns RootService(MEMBER_WANNABE_API_ROOT_SERVICE_NAME)

        assertFailsWith<NotFoundException> {
            personDataPipeline.softDelete(TestUpdatedByReferenceAndSoftDeletable(id = test.id))
        }

        coVerifyOnce { contextService.getClaims(useInheritedAuthToken = true) }
        coVerifyOnce { contextService.getRootService() }
    }
}
