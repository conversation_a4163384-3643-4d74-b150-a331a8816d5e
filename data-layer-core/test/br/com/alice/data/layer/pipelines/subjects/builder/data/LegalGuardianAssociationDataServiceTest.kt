package br.com.alice.data.layer.pipelines.subjects.builder.data

import br.com.alice.authentication.TokenVerifier
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.authorization.AuthorizationService
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import br.com.alice.data.layer.helpers.DataServiceTestHelper
import br.com.alice.data.layer.helpers.TestTableFactory
import br.com.alice.data.layer.models.LegalGuardianAssociationModel
import br.com.alice.data.layer.models.LegalGuardianAssociationStatusModel
import br.com.alice.data.layer.models.LegalGuardianAssociationStatusType
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.pipelines.context.ContextService
import br.com.alice.data.layer.pipelines.subjects.builders.data.LegalGuardianAssociationDataService
import br.com.alice.data.layer.repositories.JdbiRepository
import br.com.alice.data.layer.services.PersonTokenService
import br.com.alice.data.layer.services.PersonTokenServiceImpl
import br.com.alice.data.layer.services.ReplicationLagService
import br.com.alice.data.layer.tables.LegalGuardianAssociationTable
import br.com.alice.data.layer.tables.PersonTable
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class LegalGuardianAssociationDataServiceTest: DataServiceTestHelper() {

    private val authorizationService: AuthorizationService = mockk()
    private val tokenVerifier: TokenVerifier = mockk()
    private val replicationLagService: ReplicationLagService = mockk()
    private val personTokenService = PersonTokenServiceImpl(tokenJdbi) as PersonTokenService

    private val factory = DatabasePipelineFactory(
        mainJdbi,
        mainJdbiReplica,
        authorizationService,
        tokenVerifier,
        personTokenService,
        ContextService(tokenVerifier),
        replicationLagService,
        true
    )

    private val dataService = LegalGuardianAssociationDataService(
        factory
    )

    private val testLegalGuardianAssociationPipeline =
        factory.get(LegalGuardianAssociationModel::class, LegalGuardianAssociationTable::class, false)

    private val personRepo = JdbiRepository(mainJdbi, PersonTable::class)

    private val personIdMock = PersonId()
    private val personToken = personTokenService.createForPersonId(personIdMock).get()
    private val personTable = TestTableFactory.buildPersonTable(
        id = personToken.personPiiToken,
        nationalId = "06576248962"
    )

    private val personGuardianIdMock = PersonId()
    private val personGuardianToken = personTokenService.createForPersonId(personGuardianIdMock).get()
    private val personGuardianTable = TestTableFactory.buildPersonTable(
        id = personGuardianToken.personPiiToken,
        nationalId = "06576248966"
    )

    @BeforeTest
    fun setup() {
        personRepo.add(personTable)
        personRepo.add(personGuardianTable)
    }

    @AfterTest
    fun cleanRepos() {
        personRepo.truncate()
    }

    @Test
    fun `#getLegalGuardians should query legal guardians`() = runBlocking<Unit> {
        val legalGuardianAssociation = DataLayerTestModelFactory.buildLegalGuardianAssociation(
            personId = personIdMock,
            guardianId = personGuardianIdMock,
            status = LegalGuardianAssociationStatusModel(
                LegalGuardianAssociationStatusType.VALID
            )
        )

        testLegalGuardianAssociationPipeline.add(legalGuardianAssociation)

        val actual = dataService.getLegalGuardians(personIdMock)

        Assertions.assertThat(actual)
            .usingRecursiveComparison()
            .ignoringFields("createdAt", "updatedAt")
            .isEqualTo(listOf(legalGuardianAssociation))
    }

}
