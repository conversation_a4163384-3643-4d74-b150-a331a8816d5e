package br.com.alice.data.layer.seeds

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.data.layer.models.AccommodationType
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.BeneficiaryViewImmersionStatus
import br.com.alice.data.layer.models.BeneficiaryViewInsuranceStatus
import br.com.alice.data.layer.models.CompanyAddressModel
import br.com.alice.data.layer.models.CompanyBankingInfoModel
import br.com.alice.data.layer.models.CompanyStaffRole
import br.com.alice.data.layer.models.ExternalIdKey
import br.com.alice.data.layer.models.ExternalIdModel
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.PriceAdjustmentType
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.ProductPriceModel
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.tables.BeneficiaryCompiledViewTable
import br.com.alice.data.layer.tables.BeneficiaryHubspotTable
import br.com.alice.data.layer.tables.BeneficiaryOnboardingPhaseTable
import br.com.alice.data.layer.tables.BeneficiaryOnboardingTable
import br.com.alice.data.layer.tables.BeneficiaryTable
import br.com.alice.data.layer.tables.CassiMemberTable
import br.com.alice.data.layer.tables.CompanyStaffTable
import br.com.alice.data.layer.tables.CompanyTable
import br.com.alice.data.layer.tables.ProductBundleTable
import br.com.alice.data.layer.tables.ProductTable
import br.com.alice.data.layer.tables.ProviderTable
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

class BusinessDatabaseSeed : Seed() {
    private val personId = PersonId(RangeUUID.generate(RangeUUID.PERSON_ID_RANGE))
    private val personToken = personTokenService.createForPersonId(personId).get()
    private val beneficiaryRepo = repoFactory.get(BeneficiaryTable::class)
    private val beneficiaryOnboardingRepo = repoFactory.get(BeneficiaryOnboardingTable::class)
    private val beneficiaryOnboardingPhaseRepo = repoFactory.get(BeneficiaryOnboardingPhaseTable::class)
    private val beneficiaryCompiledViewRepo = repoFactory.get(BeneficiaryCompiledViewTable::class)
    private val companyRepo = repoFactory.get(CompanyTable::class)
    private val cassiMemberRepo = repoFactory.get(CassiMemberTable::class)
    private val beneficiaryHubspotRepo = repoFactory.get(BeneficiaryHubspotTable::class)
    private val companyStaffRepo = repoFactory.get(CompanyStaffTable::class)

    private val address = CompanyAddressModel(
        postalCode = "12345-678",
        street = "Rua Lisboa",
        number = 999,
        city = "São Paulo",
        State = "SP"
    )
    private val bankingInfo = CompanyBankingInfoModel(
        bankCode = 123,
        agencyNumber = "1234",
        accountNumber = "123456"
    )

    private val einsteinLabProvider = ProviderTable(
        id = "683d4bbb-7db2-4264-a612-b7db94f6cf54".toUUID(),
        urlSlug = "Einstein Laboratório",
        name = "Lab. Albert Einstein",
        type = ProviderType.LABORATORY,
        imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/simulation/einstein_transparent.png",
    )

    private val labBundleEinstein = ProductBundleTable(
        id = "474e1df2-2380-4062-b05f-39eba3d66d10".toUUID(),
        name = "Lab. Albert Einstein",
        providerIds = listOf(einsteinLabProvider).map(ProviderTable::id),
        type = ProductBundleType.LABORATORY,
        priceScale = 3,
        imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/simulation/einstein_transparent.png",
    )

    private val product = ProductTable(
        id = UUID.fromString("6db23e1e-6994-482a-bcf9-74b2b8fc919f"),
        title = "ALICE AP.002.002.002.001.1",
        ansNumber = "*********",
        active = true,
        externalIds = listOf(
            ExternalIdModel(ExternalIdKey.MV_PLAN, "12"),
            ExternalIdModel(ExternalIdKey.MV_PRICE, "87"),
            ExternalIdModel(ExternalIdKey.MV_GRACE_PERIOD, "3")
        ),
        accommodation = AccommodationType.ROOM,
        bundleIds = listOf(labBundleEinstein.id),
        prices = listOf(
            ProductPriceModel(
                id = "12A",
                title = "59+ anos",
                minAge = 59,
                maxAge = Int.MAX_VALUE,
                amount = BigDecimal("3488.46"),
                priceAdjustment = BigDecimal("59.16")
            )
        )
    )

    private val company = CompanyTable(
        id = UUID.fromString("84a8ab73-6c06-457a-b3f0-3c798b22cf57"),
        name = "Batata Inc.",
        legalName = "Batata Inc.",
        cnpj = "1234567/0001-12",
        email = "<EMAIL>",
        phoneNumber = "*********",
        address = address,
        priceAdjustmentType = PriceAdjustmentType.POOL_SMALL_COMPANY,
        contractStartedAt = LocalDateTime.now(),
        beneficiariesCountAtDayZero = 3,
        bankingInfo = bankingInfo,
        billingAccountablePartyId = UUID.fromString("84a8ab73-6c06-457a-b3f0-3c798b22cf57"),
        availableProducts = listOf(product.id),
        defaultProductId = product.id,
        hasEmployeesAbroad = false,
        contractsUrls = emptyList()
    )

    private val beneficiary = BeneficiaryTable(
        id = UUID.fromString("1049cb21-193b-4658-b6b3-8583774546c9"),
        personId = personToken.personPiiToken,
        companyId = company.id,
        type = BeneficiaryType.EMPLOYEE,
        activatedAt = LocalDateTime.now(),
        memberId = UUID.fromString("bacca022-4fdb-11ed-bdc3-0242ac120002")
    )

    private val beneficiaryOnboarding = BeneficiaryOnboardingTable(
        id = UUID.fromString("de0761f0-73ed-11ec-90d6-0242ac120003"),
        beneficiaryId = beneficiary.id,
        flowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
        initialProductId = company.availableProducts!![0]
    )

    private val beneficiaryOnboardingPhase = BeneficiaryOnboardingPhaseTable(
        id = UUID.fromString("79fc81a8-73ee-11ec-90d6-0242ac120003"),
        beneficiaryOnboardingId = beneficiaryOnboarding.id,
        phase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
    )

    private val beneficiaryCompiledView = BeneficiaryCompiledViewTable(
        id = UUID.fromString("2c01a8aa-cb5a-455a-84aa-33f85b0f9568"),
        companyId = company.id,
        personId = personToken.personPiiToken,
        personNationalId = "00977127010",
        personFullSocialName = "Beneficiário de Teste",
        personEmail = "<EMAIL>",
        beneficiaryId = beneficiary.id,
        beneficiaryType = beneficiary.type,
        beneficiaryContractType = BeneficiaryContractType.CLT,
        productId = product.id,
        productTitle = product.title,
        memberId = UUID.fromString("4b82a9d2-9faf-11ec-b909-0242ac120002"),
        memberStatus = MemberStatus.ACTIVE,
        immersionStatus = BeneficiaryViewImmersionStatus.UNSCHEDULED,
        insuranceStatus = BeneficiaryViewInsuranceStatus.ACTIVE,
        lastOnboardingPhase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
        flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW,
    )

    private val cassiMember = CassiMemberTable(
        id = UUID.fromString("3982a64c-9faf-11ec-b909-0242ac120002"),
        memberId = UUID.fromString("4b82a9d2-9faf-11ec-b909-0242ac120002"),
        accountNumber = "****************",
        startDate = LocalDateTime.now(),
        expirationDate = LocalDateTime.now().plusYears(1),
    )

    private val beneficiaryHubspot = BeneficiaryHubspotTable(
        id = UUID.fromString("************************************"),
        beneficiaryId = UUID.fromString("7c5dec32-04fc-4789-b4f1-e19a24969235"),
        externalContactId = "5ffa35fa-57fb-4b12-984b-cc9c9a480881",
        externalDealId = "5cbc1a3d-3849-4b9b-9bfb-793bcb18e3f4",
        personId = personToken.personPiiToken,
        companyId = UUID.fromString("5b3284f0-1f06-431c-902e-4b9bf56fb046")
    )

    private val companyStaff = CompanyStaffTable(
        id = UUID.fromString("************************************"),
        companyId = company.id,
        role = CompanyStaffRole.MAIN_COMPANY_STAFF,
        firstName = "Company",
        lastName = "StaffModel",
        email = "<EMAIL>"
    )

    override fun run() {
        companyRepo.add(company)
        beneficiaryRepo.add(beneficiary)
        beneficiaryOnboardingRepo.add(beneficiaryOnboarding)
        beneficiaryOnboardingPhaseRepo.add(beneficiaryOnboardingPhase)
        beneficiaryCompiledViewRepo.add(beneficiaryCompiledView)
        cassiMemberRepo.add(cassiMember)
        beneficiaryHubspotRepo.add(beneficiaryHubspot)
        companyStaffRepo.add(companyStaff)
    }
}
