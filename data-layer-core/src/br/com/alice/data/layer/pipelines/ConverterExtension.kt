package br.com.alice.data.layer.pipelines

import br.com.alice.common.Conversion
import br.com.alice.common.Converter
import br.com.alice.common.ConverterException
import br.com.alice.common.buildConverter
import br.com.alice.common.core.PersonId
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.data.layer.services.PersonNonPiiToken
import br.com.alice.data.layer.services.PersonPiiToken
import br.com.alice.data.layer.services.PersonTokenService
import br.com.alice.data.layer.tables.Table
import kotlin.reflect.KClass

internal object ConverterExtension {

    fun <M : Any, T : Table<*>> converter(
        modelClass: KClass<M>,
        tableClass: KClass<T>,
        personTokenService: PersonTokenService
    ): Converter<M, T> =
        buildConverter(modelClass, tableClass, customTryConverters = {
            tryConvertPersonIdToPersonPiiToken(personTokenService)
                ?: tryConvertPersonIdToPersonNonPiiToken(personTokenService)
                ?: tryConvertPersonPiiTokenToPersonId(personTokenService)
                ?: tryConvertPersonNonPiiTokenToPersonId(personTokenService)
                ?: tryConvertTsVectorToString()
                ?: tryConvertStringToTsVector()
                ?: validatePersonId()
        })

    inline fun <reified M : Any, reified T : Table<*>> converter(personTokenService: PersonTokenService): Converter<M, T> {
        return buildConverter(customTryConverters = {
            tryConvertPersonIdToPersonPiiToken(personTokenService)
                ?: tryConvertPersonIdToPersonNonPiiToken(personTokenService)
                ?: tryConvertPersonPiiTokenToPersonId(personTokenService)
                ?: tryConvertPersonNonPiiTokenToPersonId(personTokenService)
                ?: tryConvertTsVectorToString()
                ?: tryConvertStringToTsVector()
                ?: validatePersonId()
        })
    }

    fun Conversion<*, *>.tryConvertPersonIdToPersonPiiToken(personTokenService: PersonTokenService): PersonPiiToken? =
        if (fromInstance is PersonId && toClassifier == PersonPiiToken::class) {
            personTokenService.getPersonPiiToken(fromInstance as PersonId)!!
        } else {
            null
        }

    fun Conversion<*, *>.tryConvertPersonIdToPersonNonPiiToken(personTokenService: PersonTokenService) =
        if (fromInstance is PersonId && toClassifier == PersonNonPiiToken::class) {
            personTokenService.getPersonNonPiiToken(fromInstance as PersonId)!!
        } else {
            null
        }

    fun Conversion<*, *>.tryConvertPersonPiiTokenToPersonId(personTokenService: PersonTokenService) =
        if (fromInstance is PersonPiiToken && toClassifier == PersonId::class) {
            personTokenService.getPersonId(fromInstance as PersonPiiToken)!!
        } else {
            null
        }

    fun Conversion<*, *>.tryConvertPersonNonPiiTokenToPersonId(personTokenService: PersonTokenService) =
        if (fromInstance is PersonNonPiiToken && toClassifier == PersonId::class) {
            personTokenService.getPersonId(fromInstance as PersonNonPiiToken)!!
        } else {
            null
        }

    fun Conversion<*, *>.tryConvertTsVectorToString() =
        if (fromInstance is TsVector && toClassifier == String::class) {
            if (nullable && fromInstance == null) {
                null
            } else {
                (fromInstance as TsVector).value
            }
        } else {
            null
        }

    fun Conversion<*, *>.tryConvertStringToTsVector() =
        if (fromInstance is String && toClassifier == TsVector::class) {
            if (nullable && fromInstance == null) {
                null
            } else {
                TsVector(fromInstance as String)
            }
        } else {
            null
        }

    fun Conversion<*, *>.validatePersonId(): Unit? {
        if (fromInstance is PersonId && toClassifier == String::class)
            throw ConverterException("It is not allowed to convert a type PersonId from a Model to a type String from a Table")

        return null
    }

    private fun <O : Any, D : Any> buildConverter(
        originClass: KClass<O>,
        destinyClass: KClass<D>,
        customTryConverters: (Conversion<*, *>).() -> Any?
    ) = Converter(originClass, destinyClass, customTryConverters)

}
