package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.common.core.Role
import com.google.gson.annotations.Expose
import java.util.UUID

open class HealthProfessionalSubject(
    id: UUID,
    role: Role,
    @Expose
    open val memberInPortfolio: Boolean = false,
) : StaffSubject(
    id = id,
    role = role
) {
    override fun toString() = "${this::class.simpleName}($id, $memberInPortfolio)"
}
