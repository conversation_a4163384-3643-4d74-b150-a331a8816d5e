package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoiceBreakdownModel
import br.com.alice.data.layer.models.InvoiceItemModel
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.services.PersonPiiToken
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID


internal data class MemberInvoiceTable(
    val memberId: UUID,
    override val personId: PersonPiiToken,
    val totalAmount: BigDecimal,
    val status: InvoiceStatus = InvoiceStatus.OPEN,
    val type: MemberInvoiceType? = null,
    val canceledReason: CancellationReason? = null,
    val referenceDate: LocalDate,
    val dueDate: LocalDateTime,
    val paidAt: LocalDateTime? = null,
    val batchLineId: UUID? = null,
    val memberInvoiceGroupId: UUID? = null,
    val preActivationPaymentId: UUID? = null,
    val invoiceItems: List<InvoiceItemModel>? = emptyList(),
    val invoiceBreakdown: InvoiceBreakdownModel? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<MemberInvoiceTable>, PersonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}

