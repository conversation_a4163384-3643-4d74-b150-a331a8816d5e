package br.com.alice.data.layer.tables

import br.com.alice.authentication.UserType
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.AppointmentScheduleCancelledByType
import br.com.alice.data.layer.models.AppointmentScheduleModel.ReferencedLink
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class AppointmentScheduleTable(
    override val personId: PersonNonPiiToken,
    val eventId: String?,
    val eventName: String,
    val location: String?,
    val status: AppointmentScheduleStatus,
    val healthcareTeamId: UUID? = null,
    val personTaskId: UUID? = null,
    val startTime: LocalDateTime,
    val endTime: LocalDateTime? = null,
    val type: AppointmentScheduleType,
    val staffId: UUID? = null,
    val healthPlanTaskId: UUID? = null,
    val currentUserType: UserType? = null,
    val eventUuid: UUID?,
    val referencedLinks: List<ReferencedLink> = emptyList(),
    val scheduledByInternalScheduler: Boolean = false,
    val appointmentScheduleEventTypeId: UUID? = null,
    val cancelledByType: AppointmentScheduleCancelledByType? = null,
    val providerUnitId: UUID? = null,
    val createdInternally: Boolean = false,
    val screeningNavigationId: UUID? = null,
    val hubspotMeetingId: String? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<AppointmentScheduleTable>, PersonNonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
