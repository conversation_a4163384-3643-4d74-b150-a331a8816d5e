package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import java.time.LocalDateTime
import java.util.UUID

internal data class BolepixPaymentDetailTable(
    val paymentId: UUID,
    val dueDate: LocalDateTime? = null,
    val paymentUrl: String? = null,
    val barcodeBoleto: String? = null,
    val paymentCodePix: String? = null,
    val boletoPaymentUrl: String? = null,
    val pixPaymentUrl: String? = null,
    val externalId: String? = null,
    val pixQrCode: String? = null,
    val pixCopyAndPaste: String? = null,
    val bankSlipBarcodeData: String? = null,
    val bankSlipDigitableLine: String? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
): Table<BolepixPaymentDetailTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
