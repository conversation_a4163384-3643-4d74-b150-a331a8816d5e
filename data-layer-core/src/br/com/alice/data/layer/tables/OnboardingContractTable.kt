package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.ContractSignatureModel
import br.com.alice.data.layer.models.MemberProduct
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class OnboardingContractTable(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonPiiToken,
    val documentUrl: String,
    val ansOrientationLetterSignature: ContractSignatureModel? = null,
    val healthDeclarationSignature: ContractSignatureModel? = null,
    val contractSignature: ContractSignatureModel? = null,
    val sentAt: LocalDateTime? = null,
    val signatureSentAt: LocalDateTime? = null,
    val selectedProduct: MemberProduct,
    val productPriceListingId: UUID? = null,
    val certificateUrl: String? = null,
    val archived: Boolean = false,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<OnboardingContractTable>, PersonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
