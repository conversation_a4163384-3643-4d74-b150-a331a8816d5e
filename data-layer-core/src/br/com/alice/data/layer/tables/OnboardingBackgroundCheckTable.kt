package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.BackgroundCheckReportModel
import br.com.alice.data.layer.models.ChecklistItemModel
import br.com.alice.data.layer.models.ReportStatus
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class OnboardingBackgroundCheckTable(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonPiiToken,
    val reportStatus: ReportStatus = ReportStatus.PENDING,
    val report: BackgroundCheckReportModel? = null,
    val notes: String? = null,
    val checklist: List<ChecklistItemModel>? = null,
    val rawData: String? = null,
    val finishedAt: LocalDateTime? = null,
    val externalReportId: String? = null,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<OnboardingBackgroundCheckTable>, PersonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
