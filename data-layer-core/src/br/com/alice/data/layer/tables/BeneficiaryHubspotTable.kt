package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class BeneficiaryHubspotTable(
    override val id: UUID = RangeUUID.generate(),
    val beneficiaryId: UUID,
    override val personId: PersonPiiToken,
    val companyId: UUID,
    val externalContactId: String,
    val externalDealId: String,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Table<BeneficiaryHubspotTable>, PersonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime): BeneficiaryHubspotTable =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
