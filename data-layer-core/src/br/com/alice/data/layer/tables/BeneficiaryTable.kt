package br.com.alice.data.layer.tables

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.BeneficiaryCancelationReason
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.GracePeriodType
import br.com.alice.data.layer.models.GracePeriodTypeReason
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class BeneficiaryTable(
    override val id: UUID = RangeUUID.generate(),
    val parentBeneficiary: UUID? = null,
    override val personId: PersonPiiToken,
    val memberId: UUID,
    val companyId: UUID,
    val type: BeneficiaryType,
    val contractType: BeneficiaryContractType? = null,
    val parentBeneficiaryRelationType: ParentBeneficiaryRelationType? = null,
    val activatedAt: LocalDateTime,
    val canceledAt: LocalDateTime? = null,
    val hiredAt: LocalDateTime? = null,
    val parentBeneficiaryRelatedAt: LocalDateTime? = null,
    val archived: Boolean = false,
    val canceledReason: BeneficiaryCancelationReason? = null,
    val canceledDescription: String? = null,
    val cnpj: String? = null,
    val hasContributed: Boolean? = null,
    val brand: Brand? = Brand.ALICE,
    val companySubContractId: UUID? = null,
    val memberStatus: MemberStatus? = null,
    val parentPerson: PersonPiiToken? = null,
    val gracePeriodType: GracePeriodType? = null,
    val gracePeriodTypeReason: GracePeriodTypeReason? = null,
    val gracePeriodBaseDate: LocalDate? = null,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy? = null
) : Table<BeneficiaryTable>, PersonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
