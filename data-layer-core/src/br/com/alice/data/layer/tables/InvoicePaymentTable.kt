package br.com.alice.data.layer.tables

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.InvoicePaymentSource
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.PaymentReason
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

internal data class InvoicePaymentTable(
    val amount: BigDecimal,
    val approvedAt: LocalDateTime? = null,
    val status: InvoicePaymentStatus = InvoicePaymentStatus.PENDING,
    val method: PaymentMethod = PaymentMethod.BOLETO,
    val canceledReason: CancellationReason? = null,
    val memberInvoiceIds: List<UUID>,
    val invoiceGroupId: UUID? = null,
    val externalId: String? = null,
    val source: InvoicePaymentSource? = null,
    val reason: PaymentReason? = null,
    val billingAccountablePartyId: UUID? = null,
    val origin: InvoicePaymentOrigin? = InvoicePaymentOrigin.UNDEFINED,
    val sendEmail: Boolean? = null,
    val amountPaid: BigDecimal? = null,
    val fine: BigDecimal? = null,
    val interest: BigDecimal? = null,
    val approvedByStaffId: UUID? = null,
    val invoiceLiquidationId: UUID? = null,
    val preActivationPaymentId: UUID? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy? = null,
) : Table<InvoicePaymentTable> {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
