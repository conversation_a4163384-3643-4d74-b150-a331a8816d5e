package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.models.HealthInformation
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.TierType
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class GuiaTable(
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),

    val personId: PersonNonPiiToken? = null,
    val memberName: String? = null,
    val number: String,
    val memberNewBorn: String? = null,
    val memberCode: String? = null,
    val memberTier: TierType? = null,
    val professionalRequesterName: String? = null,
    val professionalRequesterCouncilNumber: String? = null,
    val professionalRequesterCouncil: String? = null,
    val professionalRequesterCbos: String? = null,
    val professionalRequesterCouncilState: String? = null,
    val professionalExecutorName: String? = null,
    val professionalExecutorCouncil: String? = null,
    val professionalExecutorCouncilNumber: String? = null,
    val professionalExecutorCbos: String? = null,
    val professionalExecutorCouncilState: String? = null,
    val providerRequesterCode: String? = null,
    val providerRequesterName: String? = null,
    val requestedAt: LocalDate? = null,
    val attendanceType: String? = null,
    val providerExecutorCode: String? = null,
    val providerExecutorName: String? = null,
    val providerExecutorCnes: String? = null,
    val attendanceCharacter: String? = null,
    val accidentIndication: String? = null,
    val tissBatchNumber: String,
    val valueProcedures: BigDecimal? = null,
    val valueTaxRents: BigDecimal? = null,
    val valueMaterials: BigDecimal? = null,
    val valueMedicines: BigDecimal? = null,
    val valueOpme: BigDecimal? = null,
    val valueMedicinalGases: BigDecimal? = null,
    val valueDaily: BigDecimal? = null,
    val valueTotal: BigDecimal,
    val clinicIndication: String? = null,
    val appointmentType: String? = null,
    val providerRequesterCnpj: String? = null,
    val providerExecutorCnpj: String? = null,
    val tissBatchId: UUID,
    val serviceTypes: List<HealthSpecialistResourceBundleServiceType> = emptyList(),
    val executionEnvironment: HealthSpecialistProcedureExecutionEnvironment = HealthSpecialistProcedureExecutionEnvironment.DOES_NOT_APPLY,
    val updatedBy: UpdatedBy? = null
) : Table<GuiaTable>, HealthInformation {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
