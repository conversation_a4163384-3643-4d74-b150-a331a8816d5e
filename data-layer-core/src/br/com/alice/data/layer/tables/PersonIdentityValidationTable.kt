package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.PersonIdentityValidationStatus
import br.com.alice.data.layer.models.PersonIdentityValidationType
import br.com.alice.data.layer.services.PersonPiiToken
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

internal data class PersonIdentityValidationTable(
    override val personId: PersonPiiToken,
    val imageUsageTermConsentedAt: LocalDateTime? = null,
    val privacyPolicyTermConsentedAt: LocalDateTime? = null,
    val requestValidationCreatedAt: LocalDateTime,
    val scoreCreatedAt: LocalDateTime? = null,
    val score: BigDecimal? = null,
    val type: PersonIdentityValidationType,
    val status: PersonIdentityValidationStatus,
    val match: Boolean? = null,
    val fileVaultId: UUID? = null,
    val transactionId: String,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<PersonIdentityValidationTable>, PersonPiiReference {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
