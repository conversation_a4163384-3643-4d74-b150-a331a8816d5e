package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.extensions.isNotBlank
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.models.Gender
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.data.layer.models.CouncilModel
import br.com.alice.data.layer.models.HealthSpecialistScoreEnum
import br.com.alice.data.layer.models.PhoneNumber
import br.com.alice.data.layer.models.Qualification
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.data.layer.models.SpecialistBankAccountInfoModel
import br.com.alice.data.layer.models.SpecialistStatus
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class HealthProfessionalTable(
    val staffId: UUID,
    val profileBio: String? = null,
    val council: CouncilModel,
    val specialtyId: UUID? = null,
    val subSpecialtyIds: List<UUID> = emptyList(),
    val internalSpecialtyId: UUID? = null,
    val internalSubSpecialtyIds: List<UUID> = emptyList(),
    val quote: String? = null,
    val urlSlug: String? = null,
    val phones: List<PhoneNumber> = emptyList(),
    val qualifications: List<Qualification> = emptyList(),
    val imageUrl: String? = null,
    val education: List<String> = emptyList(),
    val tier: SpecialistTier? = null,
    val theoristTier: SpecialistTier? = null,
    val providerUnitIds: List<UUID> = emptyList(),
    val scheduleAvailabilityDays: Int? = null,
    val appointmentTypes: List<SpecialistAppointmentType> = emptyList(),
    val showOnApp: Boolean = true,
    val onCall: Boolean = false,
    val status: SpecialistStatus = SpecialistStatus.ACTIVE,
    val healthSpecialistScore: HealthSpecialistScoreEnum? = null,
    val paymentFrequency: Int? = 0,
    val email: String,
    val name: String,
    val gender: Gender? = null,
    val nationalId: String? = null,
    val type: StaffType,
    val role: Role? = null,
    var searchTokens: TsVector? = null,
    val addressIds: List<UUID>? = emptyList(),
    val contactIds: List<UUID> = emptyList(),
    val bankAccountInfo: List<SpecialistBankAccountInfoModel> = emptyList(),
    val curiosity: String? = null,
    val deAccreditationDate: LocalDate? = null,
    val onVacationUntil: LocalDateTime? = null,
    val onVacationStart: LocalDateTime? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<HealthProfessionalTable> {

    init {
        val value = listOfNotNull(name, nationalId, email, council.toString())
            .filter { it.isNotBlank() }
            .joinToString(" ")

        searchTokens = TsVector(value.unaccent())
    }

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
