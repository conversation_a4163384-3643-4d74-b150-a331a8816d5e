package br.com.alice.data.layer.tables

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class PersonPreferencesTable(
    override val personId: PersonPiiToken,
    val firstPaymentMethod: PaymentMethod,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
): Table<PersonPreferencesTable>, PersonPiiReference {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime): PersonPreferencesTable =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
