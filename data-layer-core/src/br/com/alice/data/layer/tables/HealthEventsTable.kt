package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.EventReferenceModel
import br.com.alice.data.layer.models.HealthEventOriginEnum
import br.com.alice.data.layer.models.HealthEventTypeEnum
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class HealthEventsTable(
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),

    override val personId: PersonNonPiiToken,
    val eventType: HealthEventTypeEnum,
    val caseIds: List<String>? = emptyList(),
    val caseRecordIds: List<String>? = emptyList(),
    val requestedAt: LocalDateTime,
    val executedAt: LocalDateTime? = null,
    val originReferences: List<EventReferenceModel>? = emptyList(),
    val executionReferences: List<EventReferenceModel>? = emptyList(),
    val origin: HealthEventOriginEnum,
    val healthProfessionalId: UUID? = null,
    val procedureIds: List<String>? = emptyList(),
) : Table<HealthEventsTable>, PersonNonPiiReference {

    override fun copyTable(
        version: Int,
        updatedAt: LocalDateTime,
        createdAt: LocalDateTime
    ) =
        copy(
            version = version,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
}
