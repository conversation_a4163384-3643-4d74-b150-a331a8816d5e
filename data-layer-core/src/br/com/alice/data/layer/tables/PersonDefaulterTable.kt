package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.PersonDefaulterType
import br.com.alice.data.layer.services.PersonPiiToken
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

internal data class PersonDefaulterTable(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonPiiToken,
    val value: BigDecimal,
    val type: PersonDefaulterType,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
): Table<PersonDefaulterTable>, PersonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
