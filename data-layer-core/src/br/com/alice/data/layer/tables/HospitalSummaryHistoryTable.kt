package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.data.layer.models.SummaryHistoryEventType
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class HospitalSummaryHistoryTable(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonNonPiiToken,
    val providerIntegration: ProviderIntegration,
    val externalId: String? = null,
    val unit: String? = null,
    val eventId: UUID,
    val eventType: SummaryHistoryEventType,
    val eventValue: String? = null,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<HospitalSummaryHistoryTable>, PersonNonPiiReference {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime): HospitalSummaryHistoryTable =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
