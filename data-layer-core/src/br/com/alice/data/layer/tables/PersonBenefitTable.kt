package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.CampaignPartners
import br.com.alice.data.layer.models.PersonBenefitOptInStatus
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class PersonBenefitTable(
    override val personId: PersonPiiToken,
    val campaignId: String,
    val partner: CampaignPartners,
    val optedInAt: LocalDateTime = LocalDateTime.now(),
    val optInStatus: PersonBenefitOptInStatus = PersonBenefitOptInStatus.ACCEPTED,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<PersonBenefitTable>, PersonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
