package br.com.alice.data.layer.tables

import br.com.alice.data.layer.models.DraftCommandAction
import br.com.alice.data.layer.models.DraftCommandReferencedModel
import br.com.alice.data.layer.models.DraftCommandStatus
import java.time.LocalDateTime
import java.util.UUID

internal data class DraftCommandTable(
    override val id: UUID,
    val appointmentId: UUID,
    val action: DraftCommandAction,
    val serializedModel: String,
    val referencedModel: DraftCommandReferencedModel,
    val status: DraftCommandStatus,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
): Table<DraftCommandTable> {
    override fun copyTable(
        version: Int,
        updatedAt: LocalDateTime,
        createdAt: LocalDateTime,
    ) = copy(
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}
