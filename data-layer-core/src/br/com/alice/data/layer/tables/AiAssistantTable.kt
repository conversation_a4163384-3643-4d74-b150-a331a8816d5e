package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import java.time.LocalDateTime
import java.util.UUID

data class AiAssistantTable(
    val name: String,
    val assistantId: String,
    val provider: String,
    val apiKeyEnvVar: String?,

    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val deletedAt: LocalDateTime?,
) : Table<AiAssistantTable>, SoftDeletable {

    override fun copyTable(
        version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime
    ) = copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
