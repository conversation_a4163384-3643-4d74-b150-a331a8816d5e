package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import java.time.LocalDateTime
import java.util.UUID

internal data class PixPaymentDetailTable(
    val paymentId: UUID,
    val paymentUrl: String? = null,
    val dueDate: LocalDateTime? = null,
    val paymentCode: String? = null,
    val externalId: String? = null,
    val qrCode: String? = null,
    val copyAndPaste: String? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
): Table<PixPaymentDetailTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
