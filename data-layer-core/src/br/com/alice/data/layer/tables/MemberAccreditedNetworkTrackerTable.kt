package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.AccreditedNetworkTrackerFilterType
import br.com.alice.data.layer.models.AccreditedNetworkTrackerResultModel
import br.com.alice.data.layer.models.SpecialityAccreditedNetworkTrackerModel
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class MemberAccreditedNetworkTrackerTable(
    val specialities: List<SpecialityAccreditedNetworkTrackerModel>,
    val subSpecialities: List<SpecialityAccreditedNetworkTrackerModel>,
    val types: List<AccreditedNetworkTrackerFilterType>,
    val latitude: String,
    val longitude: String,
    val range: String,
    val results: List<AccreditedNetworkTrackerResultModel>,
    val quantityResults: Int,
    val healthPlanTaskId: UUID?,
    override val personId: PersonPiiToken,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Table<MemberAccreditedNetworkTrackerTable>, PersonPiiReference {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}

