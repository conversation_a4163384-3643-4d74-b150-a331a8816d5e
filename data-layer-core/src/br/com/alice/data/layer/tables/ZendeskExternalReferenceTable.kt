package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.ZendeskExternalReference.DestinationModel
import br.com.alice.data.layer.models.ZendeskExternalReference.OriginModel
import java.time.LocalDateTime
import java.util.UUID

internal data class ZendeskExternalReferenceTable(
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),

    val originModel: OriginModel,
    val originModelId: String,
    val destinationId: Long? = null,
    val destinationNameSuffix: String? = null,
    val destinationModel: DestinationModel?,
    val syncedAt: LocalDateTime? = LocalDateTime.now()
): Table<ZendeskExternalReferenceTable> {
    override fun copyTable(
        version: Int,
        updatedAt: LocalDateTime,
        createdAt: LocalDateTime
    ): ZendeskExternalReferenceTable =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}

