package br.com.alice.data.layer.tables

import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.CompanyAddressModel
import br.com.alice.data.layer.models.CompanyBankingInfoModel
import br.com.alice.data.layer.models.CompanyBusinessUnit
import br.com.alice.data.layer.models.CompanyContractType
import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.CompanyStatus
import br.com.alice.data.layer.models.PriceAdjustmentType
import java.time.LocalDateTime
import java.util.UUID

internal data class CompanyTable(
    override val id: UUID = RangeUUID.generate(),
    val parentId: UUID? = null,
    val externalCode: String? = null,
    val name: String,
    val legalName: String,
    val cnpj: String,
    val email: String,
    val phoneNumber: String,
    val address: CompanyAddressModel,
    val priceAdjustmentType: PriceAdjustmentType? = null,
    val contractType: CompanyContractType? = null,
    val contractStartedAt: LocalDateTime? = null,
    val beneficiariesCountAtDayZero: Int? = null,
    val bankingInfo: CompanyBankingInfoModel? = null,
    val billingAccountablePartyId: UUID? = null,
    val availableProducts: List<UUID>? = null,
    val totalEmployees: Int? = null,
    val defaultProductId: UUID? = null,
    val flexBenefit: Boolean? = null,
    val hasEmployeesAbroad: Boolean? = null,
    val contractsUrls: List<String>,
    val defaultFlowType: BeneficiaryOnboardingFlowType? = null,
    val totvsContract: String? = null,
    val totvsContractVersion: String? = null,
    val totvsSubContract: String? = null,
    val totvsSubContractVersion: String? = null,
    val brand: Brand? = Brand.ALICE,
    val externalBrandId: String? = null,
    val contractIds: List<UUID> = emptyList(),
    val status: CompanyStatus? = null,
    val companySize: CompanySize? = null,
    val companyBusinessUnit: CompanyBusinessUnit? = null,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy? = null
) : Table<CompanyTable> {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
