package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.ActionPlanTaskUpdatedBy
import br.com.alice.data.layer.models.Attachment
import br.com.alice.data.layer.models.CaseRecordDetails
import br.com.alice.data.layer.models.Deadline
import br.com.alice.data.layer.models.Frequency
import br.com.alice.data.layer.models.Start
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class ActionPlanTaskTable(
    override val personId: PersonNonPiiToken,
    val healthPlanId: UUID?,
    val appointmentId: UUID?,
    val title: String?,
    val description: String?,
    val content: Map<String, Any>?,
    val dueDate: LocalDate?,
    val scheduledAt: LocalDateTime?,
    val appointmentScheduleId: UUID?,
    val status: ActionPlanTaskStatus,
    val lastRequesterStaffId: UUID,
    val requestersStaffIds: Set<UUID>,
    val type: ActionPlanTaskType,
    val releasedAt: LocalDateTime?,
    val finishedAt: LocalDateTime?,
    val acknowledgedAt: LocalDateTime?,
    val updatedBy: UpdatedBy?,
    val frequency: Frequency?,
    val deadline: Deadline?,
    val start: Start?,
    val attachments: List<Attachment> = emptyList(),
    val groupId: UUID?,
    val originTaskId: UUID?,
    val releasedByStaffId: UUID?,
    val finishedBy: ActionPlanTaskUpdatedBy? = null,
    val createdBy: ActionPlanTaskUpdatedBy? = null,
    val initiatedByMemberAt: LocalDateTime? = null,
    val favorite: Boolean,
    val caseRecordDetails: List<CaseRecordDetails> = emptyList(),
    val originalUpdatedAt: LocalDateTime,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<ActionPlanTaskTable>, PersonNonPiiReference, DeIdentifiedReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
