package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.DegreeOfKinship
import br.com.alice.data.layer.models.LegalGuardianAssociationStatusModel
import br.com.alice.data.layer.models.LegalGuardianAssociationStatusType
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class LegalGuardianAssociationTable(
    override val personId: PersonPiiToken,
    val guardianId: PersonPiiToken,
    val degreeOfKinship: DegreeOfKinship,
    val statusHistory: List<LegalGuardianAssociationStatusModel>? = emptyList(),
    val isSigned: Boolean = false,
    val status: LegalGuardianAssociationStatusType,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Table<LegalGuardianAssociationTable>, PersonPiiReference {

    override fun copyTable(
        version: Int,
        updatedAt: LocalDateTime,
        createdAt: LocalDateTime
    ) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
