package br.com.alice.data.layer.tables

import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.DalyaPrediction
import br.com.alice.data.layer.models.DalyaUncertainties
import br.com.alice.data.layer.models.ScreeningNavigationStatus
import br.com.alice.data.layer.models.TriageSuggestedOutput
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDateTime
import java.util.UUID

data class ScreeningNavigationTable(
    val status: ScreeningNavigationStatus,
    override val personId: PersonNonPiiToken,
    val symptoms: List<Disease>? = emptyList(),
    val subjectiveCodes: List<Disease>? = emptyList(),
    val suggestedOutput: TriageSuggestedOutput? = null,
    val dalyaPrediction: DalyaPrediction? = null,
    val dalyaUncertainties: DalyaUncertainties? = null,
    val dalyaExplanation: String? = null,
    val iaTextSuggestion: String? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<ScreeningNavigationTable>, PersonNonPiiReference {

    override fun copyTable(
        version: Int,
        updatedAt: LocalDateTime,
        createdAt: LocalDateTime,
    ) = copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
