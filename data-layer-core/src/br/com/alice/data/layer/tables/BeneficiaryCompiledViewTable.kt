package br.com.alice.data.layer.tables

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.RangeUUID
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.BeneficiaryViewImmersionStatus
import br.com.alice.data.layer.models.BeneficiaryViewInsuranceStatus
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class BeneficiaryCompiledViewTable(
    override val id: UUID = RangeUUID.generate(),
    val companyId: UUID,
    val companySubContractId: UUID? = null,
    override val personId: PersonPiiToken,
    val personNationalId: String,
    val personFullSocialName: String,
    val personEmail: String,
    val personBirthDate: LocalDate? = null,
    val personSex: Sex? = null,
    val parentPersonId: PersonPiiToken? = null,
    val parentPersonFullName: String? = null,
    val beneficiaryId: UUID,
    val beneficiaryType: BeneficiaryType,
    val beneficiaryContractType: BeneficiaryContractType? = null,
    val beneficiaryActivatedAt: LocalDateTime? = null,
    val beneficiaryCanceledAt: LocalDateTime? = null,
    val productId: UUID,
    val productAnsNumber: String? = null,
    val productTitle: String,
    val productDisplayName: String? = null,
    val productComplementName: String? = null,
    val requestProductId: UUID? = null,
    val requestProductDisplayName: String? = null,
    val requestProductComplementName: String? = null,
    val requestProductApplyAt: LocalDateTime? = null,
    val memberId: UUID,
    val memberStatus: MemberStatus? = null,
    val memberCanceledAt: LocalDateTime? = null,
    val memberActivatedAt: LocalDateTime? = null,
    val immersionStatus: BeneficiaryViewImmersionStatus,
    val insuranceStatus: BeneficiaryViewInsuranceStatus,
    val lastOnboardingPhase: BeneficiaryOnboardingPhaseType? = null,
    val flowType: BeneficiaryOnboardingFlowType? = null,
    val viewUpdatedAt: LocalDateTime = LocalDateTime.now(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val anonymized: Boolean = false
) : Table<BeneficiaryCompiledViewTable>, Anonymizable, PersonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
