package br.com.alice.data.layer.tables

import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.models.HealthSpecialistScoreEnum
import br.com.alice.data.layer.models.TierType
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class TussProcedureSpecialtyTable(
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),

    val tier: SpecialistTier,
    val price: BigDecimal,
    val medicalSpecialtyId: UUID? = null,
    val updatedStaffId: UUID,
    val beginAt: LocalDate,
    val endAt: LocalDate? = null,
    val tussCode: String,
    val aliceCode: String? = null,
    val healthSpecialistScore: HealthSpecialistScoreEnum? = null,
    val brand: Brand? = null,
    val productTier: TierType? = null,
    val healthSpecialistResourceBundleId: UUID? = null,
    val updatedBy: UpdatedBy? = null
) : Table<TussProcedureSpecialtyTable> {

    override fun copyTable(
        version: Int,
        updatedAt: LocalDateTime,
        createdAt: LocalDateTime
    ) =
        copy(
            version = version,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
}
