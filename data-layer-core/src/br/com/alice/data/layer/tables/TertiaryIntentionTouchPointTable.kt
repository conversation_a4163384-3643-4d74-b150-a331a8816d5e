package br.com.alice.data.layer.tables

import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.SummaryReferenceType
import br.com.alice.data.layer.models.TertiaryIntentionCoordinated
import br.com.alice.data.layer.models.TertiaryIntentionDischargeSummaryEvaluation
import br.com.alice.data.layer.models.TertiaryIntentionEvolution
import br.com.alice.data.layer.models.TertiaryIntentionHospitalizationOrigin
import br.com.alice.data.layer.models.TertiaryIntentionHospitalizationProcedure
import br.com.alice.data.layer.models.TertiaryIntentionHospitalizationReleaseType
import br.com.alice.data.layer.models.TertiaryIntentionHospitalizationResponsible
import br.com.alice.data.layer.models.TertiaryIntentionIntensiveCare
import br.com.alice.data.layer.models.TertiaryIntentionNotification
import br.com.alice.data.layer.models.TertiaryIntentionSemiIntensiveCare
import br.com.alice.data.layer.models.TertiaryIntentionSurgeryStatus
import br.com.alice.data.layer.models.TertiaryIntentionType
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

internal data class TertiaryIntentionTouchPointTable(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonNonPiiToken,
    val staffId: UUID? = null,
    val providerUnitId: UUID?,
    val type: TertiaryIntentionType?,
    val reason: String?,
    val objectiveCodes: List<Disease>? = emptyList(),
    val coordinated: TertiaryIntentionCoordinated?,
    val hasDischargeSummaryOnTime: Boolean = false,
    val dischargeSummaryEvaluation: TertiaryIntentionDischargeSummaryEvaluation? = null,
    val condition: String?,
    val startedAt: LocalDateTime?,
    val endedAt: LocalDateTime?,
    val hospitalizationOrigin: TertiaryIntentionHospitalizationOrigin?,
    val hospitalizationResponsible: TertiaryIntentionHospitalizationResponsible?,
    val internalPhysician: String?,
    val hospitalizationSpecialty: String?,
    val objectiveCodesReason: String?,
    val hasFollowTheFlow: Boolean?,
    val flowErrorReason: String?,
    val hadDiscussion: Boolean?,
    val hadInterappointment: Boolean?,
    val procedureDescription: String?,
    val hospitalizationPostProgramming: String?,
    val healthEventId: UUID? = null,
    val surgeryStatus: TertiaryIntentionSurgeryStatus?,
    val surgeryIndicationDate: LocalDateTime?,
    val surgeryAdmissionDate: LocalDateTime?,
    val surgeonSpecialistId: UUID?,
    val summaryReferenceId: UUID?,
    val summaryReferenceModel: SummaryReferenceType?,
    val hospitalizationProcedures: List<TertiaryIntentionHospitalizationProcedure> = emptyList(),
    val evolutions: List<TertiaryIntentionEvolution> = emptyList(),
    val intensiveCare: List<TertiaryIntentionIntensiveCare> = emptyList(),
    val semiIntensiveCare: List<TertiaryIntentionSemiIntensiveCare> = emptyList(),
    val dischargeForecastInDays: Int? = null,
    val avoidableByPrimaryCare: Boolean? = null,
    val appropriatenessEvaluationProtocol: Boolean? = null,
    val newBorn: Boolean? = null,
    val newBornWeight: BigDecimal? = null,
    val appropriateHospitalizationTime: Boolean? = null,
    val dehospitalizationResources: Boolean? = null,
    val rehospitalization: Boolean? = null,
    val hospitalizationReleaseType: TertiaryIntentionHospitalizationReleaseType? = null,
    val totvsGuiaId: UUID? = null,
    val notifications: List<TertiaryIntentionNotification> = emptyList(),
    val counterReferrals: List<UUID> = emptyList(),
    val totvsGuias: List<UUID> = emptyList(),
    val hasCausalLink: Boolean? = null,
    override val version: Int,
    override val createdAt: LocalDateTime,
    override val updatedAt: LocalDateTime
) : Table<TertiaryIntentionTouchPointTable>, PersonNonPiiReference {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
