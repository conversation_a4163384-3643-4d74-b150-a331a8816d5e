package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.data.layer.models.Attachment
import br.com.alice.data.layer.models.Deadline
import br.com.alice.data.layer.models.Frequency
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.Start
import java.time.LocalDateTime
import java.util.UUID

internal data class HealthPlanTaskTemplateTable(
    val type: HealthPlanTaskType,
    val title: String,
    val description: String? = null,
    val content: Map<String, Any>? = null,
    val frequency: Frequency? = null,
    val deadline: Deadline? = null,
    val start: Start? = null,
    val attachments: List<Attachment> = emptyList(),
    val active: Boolean = false,
    val updatedBy: UpdatedBy? = null,
    var searchTokens: TsVector? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<HealthPlanTaskTemplateTable> {
    init {
        searchTokens = TsVector(title.unaccent())
    }

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
