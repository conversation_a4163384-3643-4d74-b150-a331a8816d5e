package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.GuiaProcedureStatus
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

internal data class GuiaProcedureTable(
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),

    val executedAt: LocalDate? = null,
    val procedureCode: String,
    val procedureDescription: String,
    val procedureTable: String,
    val quantity: Int,
    val reduceValue: BigDecimal? = null,
    val unityValue: BigDecimal? = null,
    val totalValue: BigDecimal? = null,
    val executionStartAt: LocalTime? = null,
    val executionEndAt: LocalTime? = null,
    val accessVia: String? = null,
    val techniqueUsed: String? = null,
    val guiaId: UUID,
    val critique: String? = null,
    val hash: String? = null,
    val addedOnCriticize: Boolean = false,
    val status: GuiaProcedureStatus? = null,
    val serviceType: HealthSpecialistResourceBundleServiceType = HealthSpecialistResourceBundleServiceType.UNDEFINED,
    val isFollowUp: Boolean = false,
    val updatedBy: UpdatedBy? = null
) : Table<GuiaProcedureTable> {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
