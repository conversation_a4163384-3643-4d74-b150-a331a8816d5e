package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.HealthMeasurementInternalType
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

internal data class HealthMeasurementTable(
    override val personId: PersonNonPiiToken,
    val type: HealthMeasurementInternalType?,
    val typeId: UUID?,
    val value: BigDecimal,
    val active: Boolean,
    val addedAt: LocalDateTime,
    val updatedByStaffId: UUID?,
    val appointmentId: UUID?,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<HealthMeasurementTable>, PersonNonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
