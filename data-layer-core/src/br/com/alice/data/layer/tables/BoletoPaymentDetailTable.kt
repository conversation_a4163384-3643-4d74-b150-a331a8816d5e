package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import java.time.LocalDateTime
import java.util.UUID

internal data class BoletoPaymentDetailTable(
    val paymentId: UUID,
    val dueDate: LocalDateTime,
    val barcode: String? = null,
    val paymentUrl: String? = null,
    val externalId: String? = null,
    val barcodeData: String? = null,
    val digitableLine: String? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
): Table<BoletoPaymentDetailTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
