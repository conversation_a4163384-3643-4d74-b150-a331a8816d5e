package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.UserSignature
import java.time.LocalDateTime
import java.util.UUID

internal data class MemberContractTable(
    override val id: UUID = RangeUUID.generate(),
    val memberId: UUID,
    val signature: UserSignature? = null,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val deletedAt: LocalDateTime? = null
) : Table<MemberContractTable>, SoftDeletable {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime): MemberContractTable =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
