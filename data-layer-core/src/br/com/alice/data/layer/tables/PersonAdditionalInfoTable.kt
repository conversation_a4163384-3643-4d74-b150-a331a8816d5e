package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.storage.AliceFile
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class PersonAdditionalInfoTable(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonPiiToken,
    val emergencyContactName: String? = null,
    val emergencyContactRelationship: String? = null,
    val emergencyContactEmail: String? = null,
    val emergencyContactPhone: String? = null,
    val children: Int? = null,
    val education: String? = null,
    val occupation: String? = null,
    val relationship: String? = null,
    val livesWith: String? = null,
    val photo: AliceFile? = null,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val anonymized: Boolean = false
) : Table<PersonAdditionalInfoTable>, PersonPiiReference, Anonymizable {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
