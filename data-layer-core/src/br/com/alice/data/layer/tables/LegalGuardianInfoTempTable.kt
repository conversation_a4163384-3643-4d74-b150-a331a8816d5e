package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.AddressModel
import br.com.alice.data.layer.models.DegreeOfKinship
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class LegalGuardianInfoTempTable(
    override val personId: PersonPiiToken,
    val firstName: String,
    val lastName: String,
    val socialFirstName: String? = null,
    val socialLastName: String? = null,
    val identityDocument: String,
    val identityDocumentIssuingBody: String,
    val degreeOfKinship: DegreeOfKinship,
    val nationalId: String,
    val email: String,
    val archived: Boolean = false,
    val address: AddressModel,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val anonymized: Boolean = false,
): Table<LegalGuardianInfoTempTable>, PersonPiiReference, Anonymizable {
    override fun copyTable(
        version: Int,
        updatedAt: LocalDateTime,
        createdAt: LocalDateTime,
    ) = copy(
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}
