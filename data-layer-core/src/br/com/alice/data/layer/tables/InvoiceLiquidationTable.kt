package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.BusinessType
import br.com.alice.data.layer.models.InvoiceLiquidationStatus
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class InvoiceLiquidationTable(
    override val id: UUID = RangeUUID.generate(),
    val externalId: String,
    val amount: BigDecimal,
    val addition: BigDecimal = BigDecimal.ZERO,
    val discount: BigDecimal = BigDecimal.ZERO,
    val dueDate: LocalDate,
    val memberInvoiceGroupIds: List<UUID>,
    val status: InvoiceLiquidationStatus,
    val billingAccountablePartyId: UUID,
    val companyIds: List<UUID>,
    val subcontractIds: List<UUID>,
    @Deprecated("Use companyIds instead")
    val companyId: UUID?,
    val subcontractId: UUID?,
    val installment: Int,
    val totalInstallments: Int,
    val businessType: BusinessType,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy? = null
) : Table<InvoiceLiquidationTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
