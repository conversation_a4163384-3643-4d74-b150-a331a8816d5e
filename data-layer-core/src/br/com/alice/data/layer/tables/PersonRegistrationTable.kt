package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.PersonRegistrationAnswerModel
import br.com.alice.data.layer.models.PersonRegistrationStep
import br.com.alice.data.layer.services.PersonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class PersonRegistrationTable(
    override val personId: PersonPiiToken,
    val currentStep: PersonRegistrationStep,
    val answers: List<PersonRegistrationAnswerModel>,
    val finishedAt: LocalDateTime? = null,
    val archived: Boolean? = false,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<PersonRegistrationTable>, PersonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
