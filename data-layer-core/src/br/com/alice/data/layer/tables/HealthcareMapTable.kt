package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.HealthcareMap
import br.com.alice.data.layer.models.RiskDescription
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class HealthcareMapTable(
    val healthcareTeamId: UUID,
    val cidGroups: List<UUID> = emptyList(),
    val cases: List<HealthcareMap.HealthcareMapCase> = emptyList(),
    val riskValue: Int? = null,
    val riskDescription: RiskDescription? = null,
    val healthEvents: HealthcareMap.HealthEvents,
    val riskAddedAt: LocalDateTime? = null,
    val caseRecordAddedAt: LocalDateTime?,
    val totalCompensated: Int,
    val totalDecompensated: Int,
    val healthcareTeamAddedAt: LocalDateTime,
    val referenceNurseGroupId: UUID? = null,
    val multiStaffIds: List<UUID> = emptyList(),
    val public: HealthcareMap.Public = HealthcareMap.Public.DEFAULT,
    override val personId: PersonNonPiiToken,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<HealthcareMapTable>, PersonNonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
