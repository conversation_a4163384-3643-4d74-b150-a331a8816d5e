package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.models.Gender
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.data.layer.models.CouncilModel
import br.com.alice.data.layer.models.PhoneNumber
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.data.layer.models.SpecialistStatus
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CassiSpecialistTable(
    override val id: UUID = RangeUUID.generate(),
    val name: String,
    val specialtyId: UUID,
    val subSpecialtyIds: List<UUID> = emptyList(),
    val email: String? = null,
    val gender: Gender? = null,
    val council: CouncilModel,
    val phones: List<PhoneNumber> = emptyList(),
    val imageUrl: String? = null,
    val minAttendanceAge: Int? = null,
    val maxAttendanceAge: Int? = null,
    val appointmentTypes: List<SpecialistAppointmentType> = emptyList(),
    val status: SpecialistStatus = SpecialistStatus.ACTIVE,
    val urlSlug: String,
    var searchTokens: TsVector? = null,
    val deAccreditationDate: LocalDate? = null,
    val contactIds: List<UUID> = emptyList(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<CassiSpecialistTable> {
    init {
        searchTokens = TsVector("$name $email ${council.number}".unaccent())
    }

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
