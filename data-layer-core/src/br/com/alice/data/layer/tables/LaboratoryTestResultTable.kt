package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.LaboratoryTestResultModel.Key
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class LaboratoryTestResultTable(
    override val personId: PersonNonPiiToken,
    val performedAt: LocalDateTime,
    val key: Key,
    val otherName: String?,
    val value: String,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
    ) : Table<LaboratoryTestResultTable>, PersonNonPiiReference {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
