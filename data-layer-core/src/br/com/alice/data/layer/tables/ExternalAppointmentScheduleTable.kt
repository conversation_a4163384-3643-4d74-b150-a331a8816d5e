package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.ProfessionalModel
import br.com.alice.data.layer.models.ScheduleAppointmentType
import br.com.alice.data.layer.models.ScheduleLocationModel
import br.com.alice.data.layer.models.ScheduleProvider
import br.com.alice.data.layer.models.ScheduleStatus
import br.com.alice.data.layer.models.ScheduleType
import br.com.alice.data.layer.services.PersonNonPiiToken
import java.time.LocalDateTime
import java.util.UUID

internal data class ExternalAppointmentScheduleTable(
    override val personId: PersonNonPiiToken,
    val slotId: String,
    val verificationCode: String?,
    val appointmentType: ScheduleAppointmentType?,
    val startTime: LocalDateTime,
    val endTime: LocalDateTime?,
    val provider: ScheduleProvider,
    val status: ScheduleStatus,
    val type: ScheduleType,
    val location: ScheduleLocationModel?,
    val professional: ProfessionalModel,
    val attendanceLink: String?,
    val appointmentScheduleId: UUID? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<ExternalAppointmentScheduleTable>, PersonNonPiiReference {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
