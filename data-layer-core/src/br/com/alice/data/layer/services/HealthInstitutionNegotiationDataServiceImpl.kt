package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthInstitutionNegotiationModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthInstitutionNegotiationTable

class HealthInstitutionNegotiationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthInstitutionNegotiationModel>(factory.get(HealthInstitutionNegotiationModel::class, HealthInstitutionNegotiationTable::class)),
    HealthInstitutionNegotiationModelDataService
