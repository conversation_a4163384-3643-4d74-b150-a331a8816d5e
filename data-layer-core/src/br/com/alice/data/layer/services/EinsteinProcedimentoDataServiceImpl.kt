package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EinsteinProcedimento
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EinsteinProcedimentoTable

class EinsteinProcedimentoDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<EinsteinProcedimento>(factory.get(EinsteinProcedimento::class, EinsteinProcedimentoTable::class)),
    EinsteinProcedimentoDataService
