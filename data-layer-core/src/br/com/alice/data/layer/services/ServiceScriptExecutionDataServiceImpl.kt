package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ServiceScriptExecution
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ServiceScriptExecutionTable

class ServiceScriptExecutionDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ServiceScriptExecution>(factory.get(ServiceScriptExecution::class, ServiceScriptExecutionTable::class)),
    ServiceScriptExecutionDataService
