package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.SimpleCreditCardPaymentDetailModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.SimpleCreditCardPaymentDetailTable

class SimpleCreditCardPaymentDetailModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<SimpleCreditCardPaymentDetailModel>(factory.get(SimpleCreditCardPaymentDetailModel::class, SimpleCreditCardPaymentDetailTable::class)),
    SimpleCreditCardPaymentDetailModelDataService
