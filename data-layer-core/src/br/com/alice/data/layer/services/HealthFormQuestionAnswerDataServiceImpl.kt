package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthFormQuestionAnswer
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthFormQuestionAnswerTable

class HealthFormQuestionAnswerDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthFormQuestionAnswer>(factory.get(HealthFormQuestionAnswer::class, HealthFormQuestionAnswerTable::class)),
    HealthFormQuestionAnswerDataService
