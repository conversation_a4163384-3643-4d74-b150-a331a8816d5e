package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AssistanceSummaryModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AssistanceSummaryTable

class AssistanceSummaryDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
        BaseDataServiceImpl<AssistanceSummaryModel>(
            factory.get(AssistanceSummaryModel::class, AssistanceSummaryTable::class)
        ),
    AssistanceSummaryModelDataService
