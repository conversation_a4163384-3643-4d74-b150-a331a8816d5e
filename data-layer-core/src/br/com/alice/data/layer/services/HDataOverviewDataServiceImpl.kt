package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HDataOverview
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthDataOverviewTable

class HDataOverviewDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HDataOverview>(factory.get(HDataOverview::class, HealthDataOverviewTable::class)),
    HDataOverviewDataService
