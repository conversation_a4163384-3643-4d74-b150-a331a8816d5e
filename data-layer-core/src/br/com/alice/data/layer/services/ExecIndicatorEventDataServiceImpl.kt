package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ExecIndicatorEventModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ExecIndicatorEventTable

class ExecIndicatorEventDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ExecIndicatorEventModel>(factory.get(ExecIndicatorEventModel::class, ExecIndicatorEventTable::class)),
    ExecIndicatorEventModelDataService
