package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.SiteAccreditedNetwork
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.SiteAccreditedNetworkTable

class SiteAccreditedNetworkDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<SiteAccreditedNetwork>(factory.get(SiteAccreditedNetwork::class, SiteAccreditedNetworkTable::class)),
    SiteAccreditedNetworkDataService
