package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.RoutingHistory
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.RoutingHistoryTable

class RoutingHistoryDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<RoutingHistory>(factory.get(RoutingHistory::class, RoutingHistoryTable::class)),
    RoutingHistoryDataService
