package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ZipcodeAddress
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ZipcodeAddressTable

class ZipcodeAddressDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ZipcodeAddress>(factory.get(ZipcodeAddress::class, ZipcodeAddressTable::class)),
    ZipcodeAddressDataService
