package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.TissBatchModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.TissBatchTable

class TissBatchDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<TissBatchModel>(factory.get(TissBatchModel::class, TissBatchTable::class)),
    TissBatchModelDataService
