package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.TertiaryIntentionTouchPointTable

class TertiaryIntentionTouchPointDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
        BaseDataServiceImpl<TertiaryIntentionTouchPoint>(
            factory.get(TertiaryIntentionTouchPoint::class, TertiaryIntentionTouchPointTable::class)
        ),
        TertiaryIntentionTouchPointDataService
