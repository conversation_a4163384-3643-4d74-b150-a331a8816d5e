package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthFormOutcomeCalculatorConf
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthFormOutcomeCalculatorConfTable

class HealthFormOutcomeCalculatorConfDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthFormOutcomeCalculatorConf>(factory.get(HealthFormOutcomeCalculatorConf::class, HealthFormOutcomeCalculatorConfTable::class)),
    HealthFormOutcomeCalculatorConfDataService
