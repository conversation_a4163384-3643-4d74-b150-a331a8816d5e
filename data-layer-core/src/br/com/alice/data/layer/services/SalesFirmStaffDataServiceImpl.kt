package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.SalesFirmStaff
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.SalesFirmStaffTable

class SalesFirmStaffDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<SalesFirmStaff>(factory.get(SalesFirmStaff::class, SalesFirmStaffTable::class)),
    SalesFirmStaffDataService
