package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AppContentScreenDetail
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ScreenDetailTable

class ScreenDetailDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AppContentScreenDetail>(factory.get(AppContentScreenDetail::class, ScreenDetailTable::class)),
    ScreenDetailDataService
