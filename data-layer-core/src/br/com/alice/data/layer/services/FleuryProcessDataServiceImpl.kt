package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FleuryProcess
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FleuryProcessTable

class FleuryProcessDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FleuryProcess>(factory.get(FleuryProcess::class, FleuryProcessTable::class)),
    FleuryProcessDataService
