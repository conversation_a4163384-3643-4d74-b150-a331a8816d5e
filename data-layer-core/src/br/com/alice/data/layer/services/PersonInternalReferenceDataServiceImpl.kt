package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PersonInternalReference
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonInternalReferenceTable

class PersonInternalReferenceDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<PersonInternalReference>(factory.get(PersonInternalReference::class, PersonInternalReferenceTable::class)),
    PersonInternalReferenceDataService
