package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ProviderUnitGroupModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ProviderUnitGroupTable

class ProviderUnitGroupDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ProviderUnitGroupModel>(factory.get(ProviderUnitGroupModel::class, ProviderUnitGroupTable::class)),
    ProviderUnitGroupModelDataService
