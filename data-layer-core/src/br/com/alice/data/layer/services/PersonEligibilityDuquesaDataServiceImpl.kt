package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PersonEligibilityDuquesa
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonEligibilityDuquesaTable

class PersonEligibilityDuquesaDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<PersonEligibilityDuquesa>(factory.get(PersonEligibilityDuquesa::class, PersonEligibilityDuquesaTable::class)),
    PersonEligibilityDuquesaDataService
