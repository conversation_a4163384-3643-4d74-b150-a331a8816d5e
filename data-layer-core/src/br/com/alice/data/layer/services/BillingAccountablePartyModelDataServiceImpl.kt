package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.BillingAccountablePartyModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.BillingAccountablePartyTable

class BillingAccountablePartyModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<BillingAccountablePartyModel>(factory.get(BillingAccountablePartyModel::class, BillingAccountablePartyTable::class)),
    BillingAccountablePartyModelDataService
