package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.InvoiceItemModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.InvoiceItemTable

class InvoiceItemModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<InvoiceItemModel>(factory.get(InvoiceItemModel::class, InvoiceItemTable::class)), InvoiceItemModelDataService
