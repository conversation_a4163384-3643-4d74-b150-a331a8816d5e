package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.TestResultFeedback
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.TestResultFeedbackTable

class TestResultFeedbackDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
        BaseDataServiceImpl<TestResultFeedback>(factory.get(TestResultFeedback::class, TestResultFeedbackTable::class)),
        TestResultFeedbackDataService
