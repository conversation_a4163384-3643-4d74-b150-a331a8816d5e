package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CounterReferral
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CounterReferralTable

class CounterReferralDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CounterReferral>(factory.get(CounterReferral::class, CounterReferralTable::class)),
    CounterReferralDataService
