package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthConditionGroup
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthConditionGroupTable

class HealthConditionGroupDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthConditionGroup>(factory.get(HealthConditionGroup::class, HealthConditionGroupTable::class)),
    HealthConditionGroupDataService {
}
