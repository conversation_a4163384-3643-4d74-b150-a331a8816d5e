package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.MemberOnboardingCheckpoint
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.MemberOnboardingCheckpointTable

class MemberOnboardingCheckpointDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<MemberOnboardingCheckpoint>(factory.get(MemberOnboardingCheckpoint::class, MemberOnboardingCheckpointTable::class)),
    MemberOnboardingCheckpointDataService
