package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.BeneficiaryOnboardingModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.BeneficiaryOnboardingTable

class BeneficiaryOnboardingModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<BeneficiaryOnboardingModel>(factory.get(BeneficiaryOnboardingModel::class, BeneficiaryOnboardingTable::class)),
    BeneficiaryOnboardingModelDataService
