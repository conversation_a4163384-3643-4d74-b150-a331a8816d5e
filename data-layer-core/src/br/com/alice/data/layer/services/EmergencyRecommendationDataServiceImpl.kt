package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EmergencyRecommendation
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EmergencyRecommendationTable

class EmergencyRecommendationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
        BaseDataServiceImpl<EmergencyRecommendation>(
            factory.get(EmergencyRecommendation::class, EmergencyRecommendationTable::class)
        ),
    EmergencyRecommendationDataService
