package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.SiteAccreditedNetworkAddress
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.SiteAccreditedNetworkAddressTable

class SiteAccreditedNetworkAddressDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<SiteAccreditedNetworkAddress>(factory.get(SiteAccreditedNetworkAddress::class, SiteAccreditedNetworkAddressTable::class)),
    SiteAccreditedNetworkAddressDataService
