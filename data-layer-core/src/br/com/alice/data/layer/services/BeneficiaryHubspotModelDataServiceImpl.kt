package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.BeneficiaryHubspotModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.BeneficiaryHubspotTable

class BeneficiaryHubspotModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<BeneficiaryHubspotModel>(factory.get(BeneficiaryHubspotModel::class, BeneficiaryHubspotTable::class)),
    BeneficiaryHubspotModelDataService
