package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthCondition
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthConditionTable

class HealthConditionDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthCondition>(factory.get(HealthCondition::class, HealthConditionTable::class)),
    HealthConditionDataService
