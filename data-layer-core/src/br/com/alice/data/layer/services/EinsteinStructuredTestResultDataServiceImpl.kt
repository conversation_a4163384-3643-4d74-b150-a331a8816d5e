package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EinsteinStructuredTestResult
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EinsteinStructuredTestResultTable

class EinsteinStructuredTestResultDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<EinsteinStructuredTestResult>(factory.get(EinsteinStructuredTestResult::class, EinsteinStructuredTestResultTable::class)),
    EinsteinStructuredTestResultDataService
