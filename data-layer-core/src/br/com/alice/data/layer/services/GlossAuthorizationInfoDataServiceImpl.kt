package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.GlossAuthorizationInfoModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.GlossAuthorizationInfoTable

class GlossAuthorizationInfoDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<GlossAuthorizationInfoModel>(factory.get(GlossAuthorizationInfoModel::class, GlossAuthorizationInfoTable::class)),
        GlossAuthorizationInfoModelDataService {
}
