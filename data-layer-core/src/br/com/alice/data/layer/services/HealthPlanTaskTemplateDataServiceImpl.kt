package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthPlanTaskTemplate
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthPlanTaskTemplateTable

class HealthPlanTaskTemplateDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthPlanTaskTemplate>(factory.get(HealthPlanTaskTemplate::class, HealthPlanTaskTemplateTable::class)),
    HealthPlanTaskTemplateDataService
