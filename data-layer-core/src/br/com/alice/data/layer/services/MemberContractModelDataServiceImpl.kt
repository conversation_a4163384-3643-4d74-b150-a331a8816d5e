package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.MemberContractModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.MemberContractTable

class MemberContractModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<MemberContractModel>(factory.get(MemberContractModel::class, MemberContractTable::class)),
    MemberContractModelDataService
