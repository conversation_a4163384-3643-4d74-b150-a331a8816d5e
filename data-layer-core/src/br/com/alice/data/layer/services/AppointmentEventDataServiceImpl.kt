package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AppointmentEvent
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AppointmentEventTable

class AppointmentEventDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AppointmentEvent>(factory.get(AppointmentEvent::class, AppointmentEventTable::class)),
    AppointmentEventDataService
