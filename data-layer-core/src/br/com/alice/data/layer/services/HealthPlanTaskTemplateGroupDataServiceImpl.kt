package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthPlanTaskGroupTemplate
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthPlanTaskGroupTemplateTable

class HealthPlanTaskTemplateGroupDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthPlanTaskGroupTemplate>(factory.get(HealthPlanTaskGroupTemplate::class, HealthPlanTaskGroupTemplateTable::class)),
    HealthPlanTaskGroupTemplateDataService
