package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.MemberOnboardingStep
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.MemberOnboardingStepTable

class MemberOnboardingStepDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<MemberOnboardingStep>(factory.get(MemberOnboardingStep::class, MemberOnboardingStepTable::class)),
    MemberOnboardingStepDataService
