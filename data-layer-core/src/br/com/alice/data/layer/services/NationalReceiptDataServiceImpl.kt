package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.NationalReceiptModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.NationalReceiptTable

class NationalReceiptDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<NationalReceiptModel>(factory.get(NationalReceiptModel::class, NationalReceiptTable::class)),
    NationalReceiptModelDataService
