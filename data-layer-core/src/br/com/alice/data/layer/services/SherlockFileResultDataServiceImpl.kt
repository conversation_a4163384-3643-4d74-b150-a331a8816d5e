package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.SherlockFileResult
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.SherlockFileResultTable

class SherlockFileResultDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<SherlockFileResult>(factory.get(SherlockFileResult::class, SherlockFileResultTable::class)),
    SherlockFileResultDataService
