package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EligibilityCheckModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EligibilityCheckTable

class EligibilityCheckDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<EligibilityCheckModel>(factory.get(EligibilityCheckModel::class, EligibilityCheckTable::class)),
    EligibilityCheckModelDataService
