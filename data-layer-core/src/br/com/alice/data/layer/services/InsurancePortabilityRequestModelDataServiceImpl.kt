package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.InsurancePortabilityRequestModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.InsurancePortabilityRequestTable

class InsurancePortabilityRequestModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<InsurancePortabilityRequestModel>(
        factory.get(InsurancePortabilityRequestModel::class, InsurancePortabilityRequestTable::class)
    ), InsurancePortabilityRequestModelDataService
