package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AliceTestResultFile
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AliceTestResultFileTable

class AliceTestResultFileDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AliceTestResultFile>(factory.get(AliceTestResultFile::class, AliceTestResultFileTable::class)),
    AliceTestResultFileDataService
