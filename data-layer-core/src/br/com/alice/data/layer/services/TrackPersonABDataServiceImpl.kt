package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.TrackPersonABModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.TrackPersonABTable

class TrackPersonABDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<TrackPersonABModel>(factory.get(TrackPersonABModel::class, TrackPersonABTable::class)),
    TrackPersonABModelDataService
