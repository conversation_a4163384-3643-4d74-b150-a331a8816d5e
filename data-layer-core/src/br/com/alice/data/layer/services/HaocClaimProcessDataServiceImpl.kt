package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HaocClaimProcess
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HaocClaimProcessTable

class HaocClaimProcessDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HaocClaimProcess>(factory.get(HaocClaimProcess::class, HaocClaimProcessTable::class)),
    HaocClaimProcessDataService
