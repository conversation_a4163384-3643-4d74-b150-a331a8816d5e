package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.TimelineAiSummaryReview
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.TimelineAiSummaryReviewTable

class TimelineAiSummaryReviewDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<TimelineAiSummaryReview>(factory.get(TimelineAiSummaryReview::class, TimelineAiSummaryReviewTable::class)),
    TimelineAiSummaryReviewDataService
