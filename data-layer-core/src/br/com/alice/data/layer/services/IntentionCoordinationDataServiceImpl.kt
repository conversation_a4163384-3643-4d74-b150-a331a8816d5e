package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.IntentionCoordination
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.IntentionCoordinationTable

class IntentionCoordinationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<IntentionCoordination>(factory.get(IntentionCoordination::class, IntentionCoordinationTable::class)),
    IntentionCoordinationDataService {
}
