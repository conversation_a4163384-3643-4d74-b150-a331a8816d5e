package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthcareAdditionalTeam
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthcareAdditionalTeamTable

class HealthcareAdditionalTeamDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthcareAdditionalTeam>(factory.get(HealthcareAdditionalTeam::class, HealthcareAdditionalTeamTable::class)),
    HealthcareAdditionalTeamDataService
