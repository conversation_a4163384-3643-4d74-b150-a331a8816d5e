package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AIAssistant
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AiAssistantTable

class AIAssistantDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AIAssistant>(factory.get(AIAssistant::class, AiAssistantTable::class)),
    AIAssistantDataService
