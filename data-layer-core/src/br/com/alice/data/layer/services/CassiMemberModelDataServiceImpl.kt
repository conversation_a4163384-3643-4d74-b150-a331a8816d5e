package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CassiMemberModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CassiMemberTable

class CassiMemberModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CassiMemberModel>(factory.get(CassiMemberModel::class, CassiMemberTable::class)),
    CassiMemberModelDataService
