package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ChannelTag
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ChannelTagTable

class ChannelTagDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ChannelTag>(factory.get(ChannelTag::class, ChannelTagTable::class)),
    ChannelTagDataService
