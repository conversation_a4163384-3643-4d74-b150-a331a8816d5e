package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ServiceScriptNavigation
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ServiceScriptNavigationTable

class ServiceScriptNavigationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ServiceScriptNavigation>(factory.get(ServiceScriptNavigation::class, ServiceScriptNavigationTable::class)),
    ServiceScriptNavigationDataService
