package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HospitalizationInfoModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HospitalizationInfoTable

class HospitalizationInfoDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HospitalizationInfoModel>(factory.get(HospitalizationInfoModel::class, HospitalizationInfoTable::class)),
    HospitalizationInfoModelDataService
