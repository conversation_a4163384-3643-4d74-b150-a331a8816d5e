package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.SiteAccreditedNetworkProvider
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.SiteAccreditedNetworkProviderTable

class SiteAccreditedNetworkProviderDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<SiteAccreditedNetworkProvider>(factory.get(SiteAccreditedNetworkProvider::class, SiteAccreditedNetworkProviderTable::class)),
    SiteAccreditedNetworkProviderDataService
