package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ProviderModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ProviderTable

class ProviderDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ProviderModel>(factory.get(ProviderModel::class, ProviderTable::class)),
    ProviderModelDataService
