package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AppointmentMacro
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AppointmentMacroTable

class AppointmentMacroDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AppointmentMacro>(factory.get(AppointmentMacro::class, AppointmentMacroTable::class)),
    AppointmentMacroDataService
