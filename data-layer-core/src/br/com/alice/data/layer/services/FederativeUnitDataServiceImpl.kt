package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FederativeUnit
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FederativeUnitTable

class FederativeUnitDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FederativeUnit>(factory.get(FederativeUnit::class, FederativeUnitTable::class)),
    FederativeUnitDataService
