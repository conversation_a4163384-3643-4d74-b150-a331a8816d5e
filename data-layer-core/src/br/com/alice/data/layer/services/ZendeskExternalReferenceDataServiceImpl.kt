package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ZendeskExternalReference
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ZendeskExternalReferenceTable

class ZendeskExternalReferenceDataServiceImpl internal constructor(factory: DatabasePipelineFactory):
    BaseDataServiceImpl<ZendeskExternalReference>(factory.get(ZendeskExternalReference::class, ZendeskExternalReferenceTable::class)),
    ZendeskExternalReferenceDataService
