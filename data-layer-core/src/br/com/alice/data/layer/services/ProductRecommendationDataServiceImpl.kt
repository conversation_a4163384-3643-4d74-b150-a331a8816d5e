package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ProductRecommendation
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ProductRecommendationTable

class ProductRecommendationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ProductRecommendation>(factory.get(ProductRecommendation::class, ProductRecommendationTable::class)),
    ProductRecommendationDataService
