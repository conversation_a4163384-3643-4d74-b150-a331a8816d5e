package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.RiskGroup
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.RiskGroupTable

class RiskGroupDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<RiskGroup>(factory.get(RiskGroup::class, RiskGroupTable::class)),
    RiskGroupDataService
