package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthDemandMonitoring
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthDemandMonitoringTable

class HealthDemandMonitoringDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
        BaseDataServiceImpl<HealthDemandMonitoring>(factory.get(HealthDemandMonitoring::class, HealthDemandMonitoringTable::class)),
        HealthDemandMonitoringDataService
