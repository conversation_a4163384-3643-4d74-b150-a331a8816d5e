package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.Opportunity
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.OpportunityTable

class OpportunityDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<Opportunity>(factory.get(Opportunity::class, OpportunityTable::class)), OpportunityDataService
