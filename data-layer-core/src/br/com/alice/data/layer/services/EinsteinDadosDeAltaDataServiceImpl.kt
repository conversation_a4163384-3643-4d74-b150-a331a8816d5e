package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EinsteinDadosDeAlta
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EinsteinDadosDeAltaTable

class EinsteinDadosDeAltaDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<EinsteinDadosDeAlta>(factory.get(EinsteinDadosDeAlta::class, EinsteinDadosDeAltaTable::class)),
    EinsteinDadosDeAltaDataService
