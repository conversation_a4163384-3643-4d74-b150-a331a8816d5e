package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.B2bBatchInvoiceReportModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.B2bBatchInvoiceReportTable

class B2bBatchInvoiceReportModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<B2bBatchInvoiceReportModel>(factory.get(B2bBatchInvoiceReportModel::class, B2bBatchInvoiceReportTable::class)),
    B2bBatchInvoiceReportModelDataService
