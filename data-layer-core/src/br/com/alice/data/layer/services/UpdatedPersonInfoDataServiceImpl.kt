package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.UpdatedPersonContactInfoTempModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.UpdatedPersonContactInfoTempTable


class UpdatedPersonContactInfoTempModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<UpdatedPersonContactInfoTempModel>(factory.get(UpdatedPersonContactInfoTempModel::class, UpdatedPersonContactInfoTempTable::class)), UpdatedPersonContactInfoTempModelDataService

