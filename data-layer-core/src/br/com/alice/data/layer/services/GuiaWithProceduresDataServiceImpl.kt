package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.GuiaWithProceduresModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.GuiaWithProceduresTable

class GuiaWithProceduresDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<GuiaWithProceduresModel>(factory.get(GuiaWithProceduresModel::class, GuiaWithProceduresTable::class)),
    GuiaWithProceduresModelDataService
