package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.BoletoPaymentDetailModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.BoletoPaymentDetailTable

class BoletoPaymentDetailModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
        BaseDataServiceImpl<BoletoPaymentDetailModel>(factory.get(BoletoPaymentDetailModel::class, BoletoPaymentDetailTable::class)),
        BoletoPaymentDetailModelDataService
