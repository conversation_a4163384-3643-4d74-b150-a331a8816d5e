package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EinsteinEncaminhamento
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EinsteinEncaminhamentoTable


class EinsteinEncaminhamentoDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<EinsteinEncaminhamento>(factory.get(EinsteinEncaminhamento::class, EinsteinEncaminhamentoTable::class)),
    EinsteinEncaminhamentoDataService
