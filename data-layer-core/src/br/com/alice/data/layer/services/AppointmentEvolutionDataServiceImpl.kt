package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AppointmentEvolution
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AppointmentEvolutionTable

class AppointmentEvolutionDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AppointmentEvolution>(factory.get(AppointmentEvolution::class, AppointmentEvolutionTable::class)),
    AppointmentEvolutionDataService
