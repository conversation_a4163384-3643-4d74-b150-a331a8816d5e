package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ChannelMacro
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ChannelMacroTable

class ChannelMacroDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ChannelMacro>(factory.get(ChannelMacro::class, ChannelMacroTable::class)),
    ChannelMacroDataService
