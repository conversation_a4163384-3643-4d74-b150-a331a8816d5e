package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.DeadletterQueue
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.DeadletterQueueTable

class DeadletterQueueDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<DeadletterQueue>(factory.get(DeadletterQueue::class, DeadletterQueueTable::class)),
    DeadletterQueueDataService
