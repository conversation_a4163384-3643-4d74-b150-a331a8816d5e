package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.Protocol
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ProtocolTable

class ProtocolDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<Protocol>(factory.get(Protocol::class, ProtocolTable::class)),
    ProtocolDataService
