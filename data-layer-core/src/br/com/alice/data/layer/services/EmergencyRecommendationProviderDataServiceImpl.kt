package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EmergencyRecommendationProvider
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EmergencyRecommendationProviderTable

class EmergencyRecommendationProviderDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
        BaseDataServiceImpl<EmergencyRecommendationProvider>(
            factory.get(EmergencyRecommendationProvider::class, EmergencyRecommendationProviderTable::class)
        ),
    EmergencyRecommendationProviderDataService
