package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.StaffChannelHistory
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.StaffChannelHistoryTable

class StaffChannelHistoryDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<StaffChannelHistory>(factory.get(StaffChannelHistory::class, StaffChannelHistoryTable::class)),
    StaffChannelHistoryDataService
