package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.SiteAccreditedNetworkCategory
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.SiteAccreditedNetworkCategoryTable

class SiteAccreditedNetworkCategoryDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<SiteAccreditedNetworkCategory>(factory.get(SiteAccreditedNetworkCategory::class, SiteAccreditedNetworkCategoryTable::class)),
    SiteAccreditedNetworkCategoryDataService
