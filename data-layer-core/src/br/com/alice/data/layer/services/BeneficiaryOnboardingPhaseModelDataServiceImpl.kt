package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.BeneficiaryOnboardingPhaseTable

class BeneficiaryOnboardingPhaseModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<BeneficiaryOnboardingPhaseModel>(factory.get(BeneficiaryOnboardingPhaseModel::class, BeneficiaryOnboardingPhaseTable::class)),
    BeneficiaryOnboardingPhaseModelDataService
