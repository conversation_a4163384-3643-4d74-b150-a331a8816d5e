package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthConditionRelated
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthConditionRelatedTable

class HealthConditionRelatedDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthConditionRelated>(factory.get(HealthConditionRelated::class, HealthConditionRelatedTable::class)),
    HealthConditionRelatedDataService
