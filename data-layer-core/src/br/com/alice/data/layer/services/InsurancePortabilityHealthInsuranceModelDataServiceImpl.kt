package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.InsurancePortabilityHealthInsuranceModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.InsurancePortabilityHealthInsuranceTable

class InsurancePortabilityHealthInsuranceModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<InsurancePortabilityHealthInsuranceModel>(
        factory.get(InsurancePortabilityHealthInsuranceModel::class, InsurancePortabilityHealthInsuranceTable::class)
    ), InsurancePortabilityHealthInsuranceModelDataService
