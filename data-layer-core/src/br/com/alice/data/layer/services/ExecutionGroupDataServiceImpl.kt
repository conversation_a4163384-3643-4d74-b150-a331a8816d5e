package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ExecutionGroupModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ExecutionGroupTable

class ExecutionGroupDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ExecutionGroupModel>(factory.get(ExecutionGroupModel::class, ExecutionGroupTable::class)),
    ExecutionGroupModelDataService
