package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.OutcomeConf
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.OutcomeConfTable

class OutcomeConfDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<OutcomeConf>(factory.get(OutcomeConf::class, OutcomeConfTable::class)),
    OutcomeConfDataService
