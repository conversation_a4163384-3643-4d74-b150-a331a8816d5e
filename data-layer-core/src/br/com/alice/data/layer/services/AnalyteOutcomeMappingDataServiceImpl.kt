package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AnalyteOutcomeMapping
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AnalyteOutcomeMappingTable

class AnalyteOutcomeMappingDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
        BaseDataServiceImpl<AnalyteOutcomeMapping>(factory.get(AnalyteOutcomeMapping::class, AnalyteOutcomeMappingTable::class)),
        AnalyteOutcomeMappingDataService
