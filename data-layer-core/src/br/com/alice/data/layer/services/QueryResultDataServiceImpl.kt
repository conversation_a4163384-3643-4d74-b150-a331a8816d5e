package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.QueryResult
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.QueryResultTable

class QueryResultDataServiceImpl internal constructor(factory: DatabasePipelineFactory):
    BaseDataServiceImpl<QueryResult>(factory.get(QueryResult::class, QueryResultTable::class)),
    QueryResultDataService
