package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthDeclaration
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthDeclarationTable

class HealthDeclarationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthDeclaration>(factory.get(HealthDeclaration::class, HealthDeclarationTable::class)),
    HealthDeclarationDataService

