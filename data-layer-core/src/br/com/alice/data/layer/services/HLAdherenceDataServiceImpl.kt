package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HLAdherence
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthLogicAdherenceTable

class HLAdherenceDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
        BaseDataServiceImpl<HLAdherence>(factory.get(HLAdherence::class, HealthLogicAdherenceTable::class)),
        HLAdherenceDataService
