package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.BeneficiaryModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.BeneficiaryTable

class BeneficiaryModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<BeneficiaryModel>(factory.get(BeneficiaryModel::class, BeneficiaryTable::class)),
    BeneficiaryModelDataService
