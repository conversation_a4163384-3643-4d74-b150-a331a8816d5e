package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AppointmentProcedureExecuted
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AppointmentProcedureExecutedTable

class AppointmentProcedureExecutedDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AppointmentProcedureExecuted>(factory.get(AppointmentProcedureExecuted::class, AppointmentProcedureExecutedTable::class)),
    AppointmentProcedureExecutedDataService
