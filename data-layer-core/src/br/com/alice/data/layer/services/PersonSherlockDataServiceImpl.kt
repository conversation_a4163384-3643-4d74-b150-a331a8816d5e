package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonTable
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import java.security.MessageDigest
import java.util.UUID

class PersonSherlockDataServiceImpl internal constructor(
    factory: DatabasePipelineFactory,
    private val personTokenService: PersonTokenService
) : BaseDataServiceImpl<PersonModel>(factory.get(PersonModel::class, PersonTable::class)), PersonSherlockDataService {

    override suspend fun findByIds(ids: List<UUID>): Result<List<PersonModel>, Throwable> {
        logAccess(ids)
        return ids.pmap { personTokenService.getPersonIdByUUID(it) }.let { personIds ->
            databasePipeline.findByQuery(
                Query(
                    where = PersonSherlockDataService.PersonIdField().inList(personIds)
                )
            )
        }
    }

    override suspend fun getPersonIds(ids: List<UUID>): Result<List<PersonId>, Throwable> {
        logAccess(ids)
        return ids.pmap { personTokenService.getPersonIdByUUID(it) }.success()
    }

    override suspend fun get(id: UUID): Result<PersonModel, Throwable> = databasePipeline.get(personTokenService.getPersonIdByUUID(id))

    override suspend fun generateHashByIds(
        ids: List<String>,
        seed: Map<String, String>
    ): Result<List<Map<String, String>>, Throwable> = ids.pmap { fetchAllTokenAndCalculate(it, seed) }.success()

    private fun fetchAllTokenAndCalculate(id: String, seed: Map<String, String>): Map<String, String> {
        val mapResult = mutableMapOf<String, String>()

        val personId = personTokenService.getPersonIdByUUID(id.toUUID())
        val personPiiToken = personTokenService.getPersonPiiToken(personId)
        val personNonPiiToken = personTokenService.getPersonNonPiiToken(personId)

        mapResult["person_id01"] = calculateSha256("${personId.id}_person_id01_${seed["person_id01"]}".toByteArray())

        if (personPiiToken != null) {
            mapResult["person_id02"] =
                calculateSha256("${personPiiToken.id}_person_id02_${seed["person_id02"]}".toByteArray())
        }

        if (personNonPiiToken != null) {
            mapResult["person_id03"] =
                calculateSha256("${personNonPiiToken.id}_person_id03_${seed["person_id03"]}".toByteArray())
        }

        logHashCalculation(personId)
        return mapResult.toMap()
    }

    private fun calculateSha256(digest: ByteArray): String =
        MessageDigest
            .getInstance("SHA-256")
            .digest(digest)
            .fold(StringBuilder()) { sb, it -> sb.append("%02x".format(it)) }
            .toString()

    private fun logAccess(ids: List<UUID>) = logger.info(
        "sherlock_person_token_conversion",
        "ids" to ids
    )

    private fun logHashCalculation(id: PersonId) =
        logger.info("sherlock_fetch_token_and_calculation_hashs", "person_id" to id)
}
