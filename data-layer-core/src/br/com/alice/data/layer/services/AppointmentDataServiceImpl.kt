package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AppointmentTable

class AppointmentDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<Appointment>(factory.get(Appointment::class, AppointmentTable::class)),
    AppointmentDataService
