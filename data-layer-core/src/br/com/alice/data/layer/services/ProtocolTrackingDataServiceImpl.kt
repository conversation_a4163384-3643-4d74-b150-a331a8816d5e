package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ProtocolTracking
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ProtocolTrackingTable

class ProtocolTrackingDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ProtocolTracking>(factory.get(ProtocolTracking::class, ProtocolTrackingTable::class)),
    ProtocolTrackingDataService
