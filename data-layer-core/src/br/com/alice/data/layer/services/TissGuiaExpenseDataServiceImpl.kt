package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.TissGuiaExpenseModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.TissGuiaExpenseTable

class TissGuiaExpenseDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<TissGuiaExpenseModel>(factory.get(TissGuiaExpenseModel::class, TissGuiaExpenseTable::class)),
    TissGuiaExpenseModelDataService
