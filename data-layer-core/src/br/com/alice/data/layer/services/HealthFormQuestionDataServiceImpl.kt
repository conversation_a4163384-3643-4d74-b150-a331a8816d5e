package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthFormQuestion
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthFormQuestionTable

class HealthFormQuestionDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthFormQuestion>(factory.get(HealthFormQuestion::class, HealthFormQuestionTable::class)),
    HealthFormQuestionDataService
