package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.SuggestedProcedure
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.SuggestedProcedureTable

class SuggestedProcedureDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<SuggestedProcedure>(factory.get(SuggestedProcedure::class, SuggestedProcedureTable::class)),
    SuggestedProcedureDataService
