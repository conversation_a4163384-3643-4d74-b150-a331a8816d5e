package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PersonClinicalAccount
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonClinicalAccountTable

class PersonClinicalAccountDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<PersonClinicalAccount>(factory.get(PersonClinicalAccount::class, PersonClinicalAccountTable::class)),
    PersonClinicalAccountDataService
