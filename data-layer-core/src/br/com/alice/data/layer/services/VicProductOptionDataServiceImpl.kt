package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.VicProductOption
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.VicProductOptionTable

class VicProductOptionDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<VicProductOption>(factory.get(VicProductOption::class, VicProductOptionTable::class)),
    VicProductOptionDataService
