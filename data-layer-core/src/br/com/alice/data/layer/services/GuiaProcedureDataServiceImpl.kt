package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.GuiaProcedureModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.GuiaProcedureTable

class GuiaProcedureDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<GuiaProcedureModel>(factory.get(GuiaProcedureModel::class, GuiaProcedureTable::class)),
    GuiaProcedureModelDataService
