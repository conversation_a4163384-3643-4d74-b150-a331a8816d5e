package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ChannelFup
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ChannelFupTable

class ChannelFupDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ChannelFup>(factory.get(ChannelFup::class, ChannelFupTable::class)),
    ChannelFupDataService
