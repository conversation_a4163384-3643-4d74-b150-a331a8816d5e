package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthPlanTaskTable

class HealthPlanTaskDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthPlanTask>(factory.get(HealthPlanTask::class, HealthPlanTaskTable::class)),
    HealthPlanTaskDataService
