package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ClinicalOutcomeRecord
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ClinicalOutcomeRecordTable

class ClinicalOutcomeRecordDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ClinicalOutcomeRecord>(factory.get(ClinicalOutcomeRecord::class, ClinicalOutcomeRecordTable::class)),
    ClinicalOutcomeRecordDataService
