package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CompanyProductConfigurationModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CompanyProductConfigurationTable

class CompanyProductConfigurationModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CompanyProductConfigurationModel>(factory.get(CompanyProductConfigurationModel::class, CompanyProductConfigurationTable::class)),
    CompanyProductConfigurationModelDataService
