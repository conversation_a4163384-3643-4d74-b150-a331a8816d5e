package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthcareTeamChannel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthcareTeamChannelTable

class HealthcareTeamChannelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthcareTeamChannel>(factory.get(HealthcareTeamChannel::class, HealthcareTeamChannelTable::class)),
    HealthcareTeamChannelDataService
