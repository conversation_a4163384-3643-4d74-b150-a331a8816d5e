package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HLActionRecommendation
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthLogicActionRecommendationTable

class HealthLogicActionRecommendationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HLActionRecommendation>(factory.get(HLActionRecommendation::class, HealthLogicActionRecommendationTable::class)),
    HealthLogicActionRecommendationDataService
