package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EinsteinDiagnostico
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EinsteinDiagnosticoTable

class EinsteinDiagnosticoDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<EinsteinDiagnostico>(factory.get(EinsteinDiagnostico::class, EinsteinDiagnosticoTable::class)),
    EinsteinDiagnosticoDataService
