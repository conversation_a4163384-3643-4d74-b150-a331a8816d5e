package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AppointmentAutofillHistory
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AppointmentAutofillHistoryTable

class AppointmentAutofillHistoryHistoryDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AppointmentAutofillHistory>(factory.get(AppointmentAutofillHistory::class, AppointmentAutofillHistoryTable::class)),
    AppointmentAutofillHistoryDataService
