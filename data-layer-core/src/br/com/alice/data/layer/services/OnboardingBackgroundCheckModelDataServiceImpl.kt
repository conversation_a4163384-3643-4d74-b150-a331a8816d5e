package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.OnboardingBackgroundCheckModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.OnboardingBackgroundCheckTable

class OnboardingBackgroundCheckModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<OnboardingBackgroundCheckModel>(factory.get(OnboardingBackgroundCheckModel::class, OnboardingBackgroundCheckTable::class)),
    OnboardingBackgroundCheckModelDataService
