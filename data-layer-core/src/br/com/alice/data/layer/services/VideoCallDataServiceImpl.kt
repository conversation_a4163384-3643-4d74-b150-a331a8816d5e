package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.VideoCall
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.VideoCallTable

class VideoCallDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<VideoCall>(factory.get(VideoCall::class, VideoCallTable::class)),
    VideoCallDataService
