package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ConsolidatedHRCompanyReport
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ConsolidatedHrCompanyReportTable

class ConsolidatedHRCompanyReportDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ConsolidatedHRCompanyReport>(factory.get(ConsolidatedHRCompanyReport::class, ConsolidatedHrCompanyReportTable::class)),
    ConsolidatedHRCompanyReportDataService
