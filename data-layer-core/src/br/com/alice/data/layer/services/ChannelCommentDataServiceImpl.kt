package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ChannelComment
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ChannelCommentTable

class ChannelCommentDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ChannelComment>(factory.get(ChannelComment::class, ChannelCommentTable::class)),
    ChannelCommentDataService
