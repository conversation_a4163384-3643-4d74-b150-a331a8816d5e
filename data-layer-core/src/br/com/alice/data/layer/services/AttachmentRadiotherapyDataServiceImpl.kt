package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AttachmentRadiotherapyModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AttachmentRadiotherapyTable

class AttachmentRadiotherapyDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AttachmentRadiotherapyModel>(factory.get(AttachmentRadiotherapyModel::class, AttachmentRadiotherapyTable::class)),
    AttachmentRadiotherapyModelDataService
