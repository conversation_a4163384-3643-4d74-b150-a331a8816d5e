package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ExecIndicatorAuthorizerModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ExecIndicatorAuthorizerTable

class ExecIndicatorAuthorizerDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ExecIndicatorAuthorizerModel>(factory.get(ExecIndicatorAuthorizerModel::class, ExecIndicatorAuthorizerTable::class)),
    ExecIndicatorAuthorizerModelDataService
