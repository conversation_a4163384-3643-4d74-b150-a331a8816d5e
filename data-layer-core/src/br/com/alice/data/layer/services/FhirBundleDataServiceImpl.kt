package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FhirBundle
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FhirBundleTable

class FhirBundleDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
        BaseDataServiceImpl<FhirBundle>(factory.get(FhirBundle::class, FhirBundleTable::class)),
        FhirBundleDataService
