package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CompanyScoreMagenta
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CompanyScoreMagentaTable

class CompanyScoreMagentaDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CompanyScoreMagenta>(factory.get(CompanyScoreMagenta::class, CompanyScoreMagentaTable::class)),
    CompanyScoreMagentaDataService
