package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthSpecialistResourceBundleModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthSpecialistResourceBundleTable

class HealthSpecialistResourceBundleDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthSpecialistResourceBundleModel>(factory.get(HealthSpecialistResourceBundleModel::class, HealthSpecialistResourceBundleTable::class)),
    HealthSpecialistResourceBundleModelDataService
