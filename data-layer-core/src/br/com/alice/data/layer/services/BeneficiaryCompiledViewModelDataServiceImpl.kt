package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.BeneficiaryCompiledViewModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.BeneficiaryCompiledViewTable

class BeneficiaryCompiledViewModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<BeneficiaryCompiledViewModel>(factory.get(BeneficiaryCompiledViewModel::class, BeneficiaryCompiledViewTable::class)),
    BeneficiaryCompiledViewModelDataService
