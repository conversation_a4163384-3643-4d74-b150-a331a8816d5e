package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ActionPlanTask
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ActionPlanTaskTable

class ActionPlanTaskDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ActionPlanTask>(factory.get(ActionPlanTask::class, ActionPlanTaskTable::class)),
    ActionPlanTaskDataService
