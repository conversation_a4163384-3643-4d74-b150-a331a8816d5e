package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.MemberOnboardingTemplate
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.MemberOnboardingTemplateTable

class MemberOnboardingTemplateDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<MemberOnboardingTemplate>(factory.get(MemberOnboardingTemplate::class, MemberOnboardingTemplateTable::class)),
    MemberOnboardingTemplateDataService
