package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AliceAgoraWorkingHours
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AliceAgoraWorkingHoursTable

class AliceAgoraWorkingHoursDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AliceAgoraWorkingHours>(factory.get(AliceAgoraWorkingHours::class, AliceAgoraWorkingHoursTable::class)),
    AliceAgoraWorkingHoursDataService
