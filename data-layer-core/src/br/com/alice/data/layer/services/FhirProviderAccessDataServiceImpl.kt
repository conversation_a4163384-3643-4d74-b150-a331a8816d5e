package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FhirProviderAccess
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FhirProviderAccessTable

class FhirProviderAccessDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FhirProviderAccess>(factory.get(FhirProviderAccess::class, FhirProviderAccessTable::class)),
    FhirProviderAccessDataService
