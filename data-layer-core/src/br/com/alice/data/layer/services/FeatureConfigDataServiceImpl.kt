package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FeatureConfigModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FeatureConfigTable

class FeatureConfigDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FeatureConfigModel>(factory.get(FeatureConfigModel::class, FeatureConfigTable::class)),
    FeatureConfigModelDataService
