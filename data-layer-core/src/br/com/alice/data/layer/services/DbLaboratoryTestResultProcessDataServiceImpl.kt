package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.DbLaboratoryTestResultProcess
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.DbLaboratoryTestResultProcessTable

class DbLaboratoryTestResultProcessDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<DbLaboratoryTestResultProcess>(factory.get(DbLaboratoryTestResultProcess::class, DbLaboratoryTestResultProcessTable::class)),
    DbLaboratoryTestResultProcessDataService
