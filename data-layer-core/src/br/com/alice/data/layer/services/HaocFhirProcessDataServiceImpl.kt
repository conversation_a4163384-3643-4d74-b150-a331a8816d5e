package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HaocFhirProcess
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HaocFhirProcessTable

class HaocFhirProcessDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HaocFhirProcess>(factory.get(HaocFhirProcess::class, HaocFhirProcessTable::class)),
    HaocFhirProcessDataService
