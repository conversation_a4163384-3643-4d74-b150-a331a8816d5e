package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthPlan
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthPlanTable

class HealthPlanDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthPlan>(factory.get(HealthPlan::class, HealthPlanTable::class)),
    HealthPlanDataService
