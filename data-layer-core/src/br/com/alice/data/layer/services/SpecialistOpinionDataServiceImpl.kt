package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.SpecialistOpinion
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.SpecialistOpinionTable

class SpecialistOpinionDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<SpecialistOpinion>(factory.get(SpecialistOpinion::class, SpecialistOpinionTable::class)),
    SpecialistOpinionDataService
