package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ConsentRegistration
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ConsentRegistrationTable

class ConsentRegistrationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ConsentRegistration>(factory.get(ConsentRegistration::class, ConsentRegistrationTable::class)),
    ConsentRegistrationDataService
