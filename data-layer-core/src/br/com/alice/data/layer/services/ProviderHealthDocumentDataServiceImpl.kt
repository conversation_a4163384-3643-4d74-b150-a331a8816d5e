package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ProviderHealthDocumentModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ProviderHealthDocumentTable

class ProviderHealthDocumentDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ProviderHealthDocumentModel>(factory.get(ProviderHealthDocumentModel::class, ProviderHealthDocumentTable::class)),
    ProviderHealthDocumentModelDataService
