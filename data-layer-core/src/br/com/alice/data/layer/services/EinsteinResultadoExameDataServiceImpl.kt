package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EinsteinResultadoExame
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EinsteinResultadoExameTable

class EinsteinResultadoExameDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<EinsteinResultadoExame>(factory.get(EinsteinResultadoExame::class, EinsteinResultadoExameTable::class)),
    EinsteinResultadoExameDataService
