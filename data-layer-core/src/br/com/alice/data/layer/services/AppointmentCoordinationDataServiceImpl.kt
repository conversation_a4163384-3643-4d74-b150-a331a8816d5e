package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AppointmentCoordination
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AppointmentCoordinationTable

class AppointmentCoordinationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AppointmentCoordination>(factory.get(AppointmentCoordination::class, AppointmentCoordinationTable::class)),
    AppointmentCoordinationDataService {
}
