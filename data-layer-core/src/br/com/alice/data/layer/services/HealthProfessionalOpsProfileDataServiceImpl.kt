package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthProfessionalOpsProfileModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthProfessionalOpsProfileTable

class HealthProfessionalOpsProfileDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthProfessionalOpsProfileModel>(factory.get(HealthProfessionalOpsProfileModel::class, HealthProfessionalOpsProfileTable::class)),
    HealthProfessionalOpsProfileModelDataService
