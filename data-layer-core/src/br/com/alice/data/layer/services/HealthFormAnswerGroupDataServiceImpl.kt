package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthFormAnswerGroup
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthFormAnswerGroupTable

class HealthFormAnswerGroupDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthFormAnswerGroup>(factory.get(HealthFormAnswerGroup::class, HealthFormAnswerGroupTable::class)),
    HealthFormAnswerGroupDataService
