package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AppointmentProcedureExecutedAliceCodeSourceModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AppointmentProcedureExecutedAliceCodeSourceTable

class AppointmentProcedureExecutedAliceCodeSourceDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AppointmentProcedureExecutedAliceCodeSourceModel>(factory.get(AppointmentProcedureExecutedAliceCodeSourceModel::class, AppointmentProcedureExecutedAliceCodeSourceTable::class)),
    AppointmentProcedureExecutedAliceCodeSourceModelDataService
