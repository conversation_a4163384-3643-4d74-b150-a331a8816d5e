package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.Timeline
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.TimelineTable

class TimelineDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<Timeline>(factory.get(Timeline::class, TimelineTable::class)),
    TimelineDataService
