package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthcareResourceModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthcareResourceTable

class HealthcareResourceDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthcareResourceModel>(factory.get(HealthcareResourceModel::class, HealthcareResourceTable::class)),
    HealthcareResourceModelDataService
