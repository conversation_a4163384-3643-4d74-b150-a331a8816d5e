package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PublicTokenIntegration
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PublicTokenIntegrationTable

class PublicTokenIntegrationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<PublicTokenIntegration>(factory.get(PublicTokenIntegration::class, PublicTokenIntegrationTable::class)),
    PublicTokenIntegrationDataService
