package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthcareBundleModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthcareBundleTable

class HealthcareBundleDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthcareBundleModel>(factory.get(HealthcareBundleModel::class, HealthcareBundleTable::class)),
        HealthcareBundleModelDataService
