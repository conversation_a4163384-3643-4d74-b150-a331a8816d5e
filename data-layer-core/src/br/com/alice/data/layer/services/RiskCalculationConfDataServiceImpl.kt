package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.RiskCalculationConf
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.RiskCalculationConfTable

class RiskCalculationConfDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<RiskCalculationConf>(factory.get(RiskCalculationConf::class, RiskCalculationConfTable::class)),
    RiskCalculationConfDataService
