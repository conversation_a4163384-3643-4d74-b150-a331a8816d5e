package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.StandardCostModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.StandardCostTable

class StandardCostModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<StandardCostModel>(factory.get(StandardCostModel::class, StandardCostTable::class)),
    StandardCostModelDataService
