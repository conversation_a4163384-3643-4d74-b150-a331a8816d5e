package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.DealSalesInfo
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.DealSalesInfoTable

class DealSalesInfoDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<DealSalesInfo>(factory.get(DealSalesInfo::class, DealSalesInfoTable::class)),
    DealSalesInfoDataService
