package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.GenericFileVault
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.GenericFileVaultTable

class GenericFileVaultDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<GenericFileVault>(factory.get(GenericFileVault::class, GenericFileVaultTable::class)),
    GenericFileVaultDataService
