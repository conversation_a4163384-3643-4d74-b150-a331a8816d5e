package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ProviderReadTracking
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ProviderReadTrackingTable


class ProviderReadTrackingDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ProviderReadTracking>(
        factory.get(ProviderReadTracking::class, ProviderReadTrackingTable::class)
    ), ProviderReadTrackingDataService
