package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.LegalGuardianInfoTempModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.LegalGuardianInfoTempTable

class LegalGuardianInfoTempModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<LegalGuardianInfoTempModel>(factory.get(LegalGuardianInfoTempModel::class, LegalGuardianInfoTempTable::class)),
    LegalGuardianInfoTempModelDataService
