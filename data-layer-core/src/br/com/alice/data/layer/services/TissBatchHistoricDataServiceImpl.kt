package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.TissBatchHistoricModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.TissBatchHistoricTable

class TissBatchHistoricDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
        BaseDataServiceImpl<TissBatchHistoricModel>(
            factory.get(TissBatchHistoricModel::class, TissBatchHistoricTable::class)
        ),
    TissBatchHistoricModelDataService
