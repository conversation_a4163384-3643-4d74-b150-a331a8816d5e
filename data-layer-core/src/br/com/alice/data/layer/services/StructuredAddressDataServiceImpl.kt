package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.StructuredAddress
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.StructuredAddressTable

class StructuredAddressDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<StructuredAddress>(factory.get(StructuredAddress::class, StructuredAddressTable::class)),
    StructuredAddressDataService
