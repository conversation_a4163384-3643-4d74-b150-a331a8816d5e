package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthConditionAxis
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthConditionAxisTable

class HealthConditionAxisDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthConditionAxis>(factory.get(HealthConditionAxis::class, HealthConditionAxisTable::class)),
    HealthConditionAxisDataService
