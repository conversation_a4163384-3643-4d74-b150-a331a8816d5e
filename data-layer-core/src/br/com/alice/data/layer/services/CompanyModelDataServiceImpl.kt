package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CompanyModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CompanyTable

class CompanyModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CompanyModel>(factory.get(CompanyModel::class, CompanyTable::class)),
    CompanyModelDataService
