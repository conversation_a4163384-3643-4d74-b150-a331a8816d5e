package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HaocSumarioDeAltaResult
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HaocSumarioDeAltaResultTable

class HaocSumarioDeAltaResultDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HaocSumarioDeAltaResult>(factory.get(HaocSumarioDeAltaResult::class, HaocSumarioDeAltaResultTable::class)),
    HaocSumarioDeAltaResultDataService
