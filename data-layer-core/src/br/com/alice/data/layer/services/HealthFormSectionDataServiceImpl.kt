package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthFormSection
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthFormSectionTable

class HealthFormSectionDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthFormSection>(factory.get(HealthFormSection::class, HealthFormSectionTable::class)),
    HealthFormSectionDataService
