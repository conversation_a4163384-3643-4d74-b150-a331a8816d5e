package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FleuryTestResultFile
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FleuryTestResultFileTable

class FleuryTestResultFileDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FleuryTestResultFile>(factory.get(FleuryTestResultFile::class, FleuryTestResultFileTable::class)),
    FleuryTestResultFileDataService
