package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AppointmentTemplate
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AppointmentTemplateTable

class AppointmentTemplateDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AppointmentTemplate>(factory.get(AppointmentTemplate::class, AppointmentTemplateTable::class)),
    AppointmentTemplateDataService
