package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ChannelTheme
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ChannelThemeTable

class ChannelThemeDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ChannelTheme>(factory.get(ChannelTheme::class, ChannelThemeTable::class)),
    ChannelThemeDataService
