package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AppointmentFollowUp
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AppointmentFollowUpTable

class AppointmentFollowUpDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AppointmentFollowUp>(factory.get(AppointmentFollowUp::class, AppointmentFollowUpTable::class)),
    AppointmentFollowUpDataService
