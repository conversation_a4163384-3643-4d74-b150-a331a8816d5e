package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AliceTestResultBundle
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AliceTestResultBundleTable

class AliceTestResultBundleDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AliceTestResultBundle>(factory.get(AliceTestResultBundle::class, AliceTestResultBundleTable::class)),
    AliceTestResultBundleDataService
