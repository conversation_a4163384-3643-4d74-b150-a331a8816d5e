package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.MagicNumbersModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.MagicNumbersTable

class MagicNumbersDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<MagicNumbersModel>(factory.get(MagicNumbersModel::class, MagicNumbersTable::class)),
    MagicNumbersModelDataService
