package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CompanyContractModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CompanyContractTable

class CompanyContractModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CompanyContractModel>(factory.get(CompanyContractModel::class, CompanyContractTable::class)),
    CompanyContractModelDataService
