package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HaocProntoAtendimentoResult
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HaocProntoAtendimentoResultTable

class HaocProntoAtendimentoResultDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HaocProntoAtendimentoResult>(factory.get(HaocProntoAtendimentoResult::class, HaocProntoAtendimentoResultTable::class)),
    HaocProntoAtendimentoResultDataService
