package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ScreeningNavigation
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ScreeningNavigationTable

class ScreeningNavigationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ScreeningNavigation>(factory.get(ScreeningNavigation::class, ScreeningNavigationTable::class)),
    ScreeningNavigationDataService
