package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.MemberInvoiceGroupModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.MemberInvoiceGroupTable

class MemberInvoiceGroupModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<MemberInvoiceGroupModel>(factory.get(MemberInvoiceGroupModel::class, MemberInvoiceGroupTable::class)),
    MemberInvoiceGroupModelDataService
