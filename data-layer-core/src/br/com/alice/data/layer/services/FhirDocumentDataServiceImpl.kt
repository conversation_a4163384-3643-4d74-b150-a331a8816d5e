package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FhirDocument
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FhirDocumentTable

class FhirDocumentDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FhirDocument>(factory.get(FhirDocument::class, FhirDocumentTable::class)),
    FhirDocumentDataService
