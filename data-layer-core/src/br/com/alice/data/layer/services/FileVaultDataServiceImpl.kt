package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FileVault
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FileVaultTable

class FileVaultDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FileVault>(factory.get(FileVault::class, FileVaultTable::class)),
    FileVaultDataService
