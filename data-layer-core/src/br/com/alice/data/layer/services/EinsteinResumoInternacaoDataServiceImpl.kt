package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EinsteinResumoInternacao
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EinsteinResumoInternacaoTable


class EinsteinResumoInternacaoDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<EinsteinResumoInternacao>(factory.get(EinsteinResumoInternacao::class, EinsteinResumoInternacaoTable::class)),
    EinsteinResumoInternacaoDataService
