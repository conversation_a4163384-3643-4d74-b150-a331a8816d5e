package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HubspotTicket
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HubspotTicketTable

class HubspotTicketDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HubspotTicket>(factory.get(HubspotTicket::class, HubspotTicketTable::class)),
    HubspotTicketDataService
