package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ServiceScriptRelationship
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ServiceScriptRelationshipTable

class ServiceScriptRelationshipDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ServiceScriptRelationship>(factory.get(ServiceScriptRelationship::class, ServiceScriptRelationshipTable::class)),
    ServiceScriptRelationshipDataService
