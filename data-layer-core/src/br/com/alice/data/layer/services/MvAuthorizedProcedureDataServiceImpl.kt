package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.MvAuthorizedProcedureModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.MvAuthorizedProcedureTable

class MvAuthorizedProcedureDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<MvAuthorizedProcedureModel>(factory.get(MvAuthorizedProcedureModel::class, MvAuthorizedProcedureTable::class)),
    MvAuthorizedProcedureModelDataService
