package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ServiceScriptAction
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ServiceScriptActionTable

class ServiceScriptActionDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ServiceScriptAction>(factory.get(ServiceScriptAction::class, ServiceScriptActionTable::class)),
    ServiceScriptActionDataService
