package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PersonClinicalAccountHistory
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonClinicalAccountHistoryTable

class PersonClinicalAccountHistoryDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<PersonClinicalAccountHistory>(
        factory.get(PersonClinicalAccountHistory::class, PersonClinicalAccountHistoryTable::class)
    ), PersonClinicalAccountHistoryDataService
