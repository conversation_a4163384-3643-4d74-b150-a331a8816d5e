package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CompanyProductPriceListingModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CompanyProductPriceListingTable

class CompanyProductPriceListingModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CompanyProductPriceListingModel>(factory.get(CompanyProductPriceListingModel::class, CompanyProductPriceListingTable::class)),
    CompanyProductPriceListingModelDataService
