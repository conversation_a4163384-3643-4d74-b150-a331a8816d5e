package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthPlanTaskGroup
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthPlanTaskGroupTable

class HealthPlanTaskGroupDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthPlanTaskGroup>(factory.get(HealthPlanTaskGroup::class, HealthPlanTaskGroupTable::class)),
    HealthPlanTaskGroupDataService
