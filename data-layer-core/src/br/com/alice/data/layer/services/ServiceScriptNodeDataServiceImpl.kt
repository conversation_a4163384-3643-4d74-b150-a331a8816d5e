package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ServiceScriptNode
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ServiceScriptNodeTable

class ServiceScriptNodeDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ServiceScriptNode>(factory.get(ServiceScriptNode::class, ServiceScriptNodeTable::class)),
    ServiceScriptNodeDataService
