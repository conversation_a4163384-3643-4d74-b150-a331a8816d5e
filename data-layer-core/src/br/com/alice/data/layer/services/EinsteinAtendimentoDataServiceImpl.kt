package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EinsteinAtendimento
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EinsteinAtendimentoTable

class EinsteinAtendimentoDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<EinsteinAtendimento>(factory.get(EinsteinAtendimento::class, EinsteinAtendimentoTable::class)),
    EinsteinAtendimentoDataService
