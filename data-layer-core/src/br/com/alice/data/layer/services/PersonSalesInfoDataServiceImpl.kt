package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PersonSalesInfo
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonSalesInfoTable

class PersonSalesInfoDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<PersonSalesInfo>(factory.get(PersonSalesInfo::class, PersonSalesInfoTable::class)),
    PersonSalesInfoDataService
