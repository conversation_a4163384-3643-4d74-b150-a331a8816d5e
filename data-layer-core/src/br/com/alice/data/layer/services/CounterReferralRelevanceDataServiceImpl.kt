package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CounterReferralRelevance
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CounterReferralRelevanceTable

class CounterReferralRelevanceDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CounterReferralRelevance>(factory.get(CounterReferralRelevance::class, CounterReferralRelevanceTable::class)),
    CounterReferralRelevanceDataService
