package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ProfessionalTierProcedureValueModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ProfessionalTierProcedureValueTable

class ProfessionalTierProcedureValueDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ProfessionalTierProcedureValueModel>(factory.get(ProfessionalTierProcedureValueModel::class, ProfessionalTierProcedureValueTable::class)),
    ProfessionalTierProcedureValueModelDataService
