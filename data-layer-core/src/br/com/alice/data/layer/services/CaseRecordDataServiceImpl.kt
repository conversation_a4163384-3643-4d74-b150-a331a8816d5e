package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CaseRecord
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CaseRecordTable

class CaseRecordDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CaseRecord>(factory.get(CaseRecord::class, CaseRecordTable::class)),
    CaseRecordDataService
