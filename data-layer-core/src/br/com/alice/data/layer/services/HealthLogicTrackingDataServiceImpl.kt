package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthLogicTracking
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthLogicTrackingTable

class HealthLogicTrackingDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthLogicTracking>(factory.get(HealthLogicTracking::class, HealthLogicTrackingTable::class)),
    HealthLogicTrackingDataService
