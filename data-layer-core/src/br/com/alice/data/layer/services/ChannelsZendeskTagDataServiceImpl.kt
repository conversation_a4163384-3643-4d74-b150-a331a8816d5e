package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ChannelsZendeskTag
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ChannelsZendeskTagTable

class ChannelsZendeskTagDataServiceImpl internal constructor(factory: DatabasePipelineFactory):
    BaseDataServiceImpl<ChannelsZendeskTag>(factory.get(ChannelsZendeskTag::class, ChannelsZendeskTagTable::class)),
        ChannelsZendeskTagDataService

