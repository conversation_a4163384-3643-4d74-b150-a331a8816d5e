package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.SalesFirmAgentPartnership
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.SalesFirmAgentPartnershipTable

class SalesFirmAgentPartnershipDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<SalesFirmAgentPartnership>(factory.get(SalesFirmAgentPartnership::class, SalesFirmAgentPartnershipTable::class)),
    SalesFirmAgentPartnershipDataService
