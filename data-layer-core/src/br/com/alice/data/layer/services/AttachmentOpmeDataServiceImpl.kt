package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AttachmentOpmeModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AttachmentOpmeTable

class AttachmentOpmeDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AttachmentOpmeModel>(factory.get(AttachmentOpmeModel::class, AttachmentOpmeTable::class)),
    AttachmentOpmeModelDataService
