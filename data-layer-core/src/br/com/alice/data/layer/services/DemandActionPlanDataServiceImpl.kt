package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.DemandActionPlan
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.DemandActionPlanTable

class DemandActionPlanDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<DemandActionPlan>(factory.get(DemandActionPlan::class, DemandActionPlanTable::class)),
    DemandActionPlanDataService
