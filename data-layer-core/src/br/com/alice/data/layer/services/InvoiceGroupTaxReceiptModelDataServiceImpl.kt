package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.InvoiceGroupTaxReceiptModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.InvoiceGroupTaxReceiptTable

class InvoiceGroupTaxReceiptModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<InvoiceGroupTaxReceiptModel>(factory.get(InvoiceGroupTaxReceiptModel::class, InvoiceGroupTaxReceiptTable::class)),
    InvoiceGroupTaxReceiptModelDataService
