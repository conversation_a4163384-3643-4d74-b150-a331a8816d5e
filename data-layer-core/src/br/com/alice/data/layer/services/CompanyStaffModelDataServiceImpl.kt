package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CompanyStaffModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CompanyStaffTable

class CompanyStaffModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CompanyStaffModel>(factory.get(CompanyStaffModel::class, CompanyStaffTable::class)),
    CompanyStaffModelDataService
