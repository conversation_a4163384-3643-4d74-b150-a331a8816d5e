package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.BolepixPaymentDetailModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.BolepixPaymentDetailTable

class BolepixPaymentDetailModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
        BaseDataServiceImpl<BolepixPaymentDetailModel>(factory.get(BolepixPaymentDetailModel::class, BolepixPaymentDetailTable::class)),
        BolepixPaymentDetailModelDataService
