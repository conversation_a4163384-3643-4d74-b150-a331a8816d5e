package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthForm
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthFormTable

class HealthFormDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthForm>(factory.get(HealthForm::class, HealthFormTable::class)),
    HealthFormDataService
