package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PreviewEarningSummaryModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PreviewEarningSummaryTable

class PreviewEarningSummaryDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<PreviewEarningSummaryModel>(factory.get(PreviewEarningSummaryModel::class, PreviewEarningSummaryTable::class)),
    PreviewEarningSummaryModelDataService
