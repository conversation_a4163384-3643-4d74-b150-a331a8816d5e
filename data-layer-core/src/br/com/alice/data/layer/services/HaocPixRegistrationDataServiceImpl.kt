package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HaocPixRegistration
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HaocPixRegistrationTable

class HaocPixRegistrationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HaocPixRegistration>(factory.get(HaocPixRegistration::class, HaocPixRegistrationTable::class)),
    HaocPixRegistrationDataService
