package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthcareMap
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthcareMapTable

class HealthcareMapDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthcareMap>(factory.get(HealthcareMap::class, HealthcareMapTable::class)),
    HealthcareMapDataService
