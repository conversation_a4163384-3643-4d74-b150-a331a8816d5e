package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FleuryTestResult
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FleuryTestResultTable

class FleuryTestResultDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FleuryTestResult>(factory.get(FleuryTestResult::class, FleuryTestResultTable::class)),
    FleuryTestResultDataService
