package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.SalesAgent
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.SalesAgentTable

class SalesAgentDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<SalesAgent>(factory.get(SalesAgent::class, SalesAgentTable::class)),
    SalesAgentDataService

