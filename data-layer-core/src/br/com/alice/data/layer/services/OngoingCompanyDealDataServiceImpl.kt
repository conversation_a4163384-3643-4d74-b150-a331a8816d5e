package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.OngoingCompanyDeal
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.OngoingCompanyDealTable

class OngoingCompanyDealDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<OngoingCompanyDeal>(factory.get(OngoingCompanyDeal::class, OngoingCompanyDealTable::class)),
    OngoingCompanyDealDataService
