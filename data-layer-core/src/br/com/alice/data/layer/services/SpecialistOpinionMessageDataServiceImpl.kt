package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.SpecialistOpinionMessage
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.SpecialistOpinionMessageTable

class SpecialistOpinionMessageDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<SpecialistOpinionMessage>(factory.get(SpecialistOpinionMessage::class, SpecialistOpinionMessageTable::class)),
    SpecialistOpinionMessageDataService
