package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CompanyActivationFilesModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CompanyActivationFilesTable

class CompanyActivationFilesModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CompanyActivationFilesModel>(factory.get(CompanyActivationFilesModel::class, CompanyActivationFilesTable::class)),
    CompanyActivationFilesModelDataService
