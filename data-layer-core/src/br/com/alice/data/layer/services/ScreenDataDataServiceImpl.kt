package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ScreenData
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ScreenDataTable

class ScreenDataDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ScreenData>(factory.get(ScreenData::class, ScreenDataTable::class)),
    ScreenDataDataService
