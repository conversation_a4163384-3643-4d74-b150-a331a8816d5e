package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.GenerateExternalAttendancePa
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.GenerateExternalAttendancePaTable

class GenerateExternalAttendancePaDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<GenerateExternalAttendancePa>(factory.get(GenerateExternalAttendancePa::class, GenerateExternalAttendancePaTable::class)),
    GenerateExternalAttendancePaDataService
