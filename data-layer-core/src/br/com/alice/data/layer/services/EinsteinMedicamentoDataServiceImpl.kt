package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.EinsteinMedicamento
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.EinsteinMedicamentoTable

class EinsteinMedicamentoDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<EinsteinMedicamento>(factory.get(EinsteinMedicamento::class, EinsteinMedicamentoTable::class)),
    EinsteinMedicamentoDataService
