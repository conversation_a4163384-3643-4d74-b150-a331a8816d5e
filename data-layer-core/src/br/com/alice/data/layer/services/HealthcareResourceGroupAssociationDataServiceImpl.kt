package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthcareResourceGroupAssociationModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthcareResourceGroupAssociationTable

class HealthcareResourceGroupAssociationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthcareResourceGroupAssociationModel>(factory.get(HealthcareResourceGroupAssociationModel::class, HealthcareResourceGroupAssociationTable::class)),
    HealthcareResourceGroupAssociationModelDataService
