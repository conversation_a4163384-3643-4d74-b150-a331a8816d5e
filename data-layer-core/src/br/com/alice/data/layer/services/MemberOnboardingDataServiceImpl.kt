package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.MemberOnboarding
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.MemberOnboardingTable

class MemberOnboardingDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<MemberOnboarding>(factory.get(MemberOnboarding::class, MemberOnboardingTable::class)),
    MemberOnboardingDataService
