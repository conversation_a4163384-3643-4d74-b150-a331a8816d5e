package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.LegalGuardianAssociationModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.LegalGuardianAssociationTable

class LegalGuardianAssociationModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<LegalGuardianAssociationModel>(factory.get(LegalGuardianAssociationModel::class, LegalGuardianAssociationTable::class)), LegalGuardianAssociationModelDataService
