package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ServiceScriptNavigationGroup
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ServiceScriptNavigationGroupTable

class ServiceScriptNavigationGroupDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ServiceScriptNavigationGroup>(factory.get(ServiceScriptNavigationGroup::class, ServiceScriptNavigationGroupTable::class)),
    ServiceScriptNavigationGroupDataService
