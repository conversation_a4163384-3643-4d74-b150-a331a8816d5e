package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AbTestSpecialistRecommendation
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AbTestSpecialistRecommendationTable

class AbTestSpecialistRecommendationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AbTestSpecialistRecommendation>(factory.get(AbTestSpecialistRecommendation::class, AbTestSpecialistRecommendationTable::class)),
    AbTestSpecialistRecommendationDataService
