package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AITextInferenceFeedback
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AiTextInferenceFeedbackTable

class AITextInferenceFeedbackDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AITextInferenceFeedback>(factory.get(AITextInferenceFeedback::class, AiTextInferenceFeedbackTable::class)),
    AITextInferenceFeedbackDataService
