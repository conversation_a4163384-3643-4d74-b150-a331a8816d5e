package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.MemberTelegramTrackingModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.MemberTelegramTrackingTable

class MemberTelegramTrackingModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<MemberTelegramTrackingModel>(factory.get(MemberTelegramTrackingModel::class, MemberTelegramTrackingTable::class)),
    MemberTelegramTrackingModelDataService
