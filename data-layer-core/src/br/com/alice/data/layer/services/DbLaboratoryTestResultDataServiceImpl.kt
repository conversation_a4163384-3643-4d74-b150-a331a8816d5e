package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.DbLaboratoryTestResult
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.DbLaboratoryTestResultTable

class DbLaboratoryTestResultDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<DbLaboratoryTestResult>(factory.get(DbLaboratoryTestResult::class, DbLaboratoryTestResultTable::class)),
    DbLaboratoryTestResultDataService
