package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthLogicRecord
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthLogicRecordTable

class HealthLogicRecordDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthLogicRecord>(factory.get(HealthLogicRecord::class, HealthLogicRecordTable::class)),
    HealthLogicRecordDataService
