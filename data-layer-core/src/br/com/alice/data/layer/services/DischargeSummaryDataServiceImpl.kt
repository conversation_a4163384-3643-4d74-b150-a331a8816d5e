package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.DischargeSummary
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.DischargeSummaryTable

class DischargeSummaryDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<DischargeSummary>(factory.get(DischargeSummary::class, DischargeSummaryTable::class)),
    DischargeSummaryDataService
