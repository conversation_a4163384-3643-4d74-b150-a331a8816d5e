package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PersonCase
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonCaseTable

class PersonCaseDataServiceImpl internal constructor(factory: DatabasePipelineFactory):
    BaseDataServiceImpl<PersonCase>(factory.get(PersonCase::class, PersonCaseTable::class)),
    PersonCaseDataService
