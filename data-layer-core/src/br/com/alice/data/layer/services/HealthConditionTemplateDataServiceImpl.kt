package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthConditionTemplate
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthConditionTemplateTable

class HealthConditionTemplateDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthConditionTemplate>(factory.get(HealthConditionTemplate::class, HealthConditionTemplateTable::class)),
    HealthConditionTemplateDataService
