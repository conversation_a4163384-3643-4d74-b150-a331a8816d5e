package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PersonTeamAssociation
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonTeamAssociationTable

class PersonTeamAssociationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<PersonTeamAssociation>(factory.get(PersonTeamAssociation::class, PersonTeamAssociationTable::class)),
    PersonTeamAssociationDataService
