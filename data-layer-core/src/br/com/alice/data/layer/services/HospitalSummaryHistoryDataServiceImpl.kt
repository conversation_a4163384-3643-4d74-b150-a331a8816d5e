package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HospitalSummaryHistory
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HospitalSummaryHistoryTable


class HospitalSummaryHistoryDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HospitalSummaryHistory>(factory.get(HospitalSummaryHistory::class, HospitalSummaryHistoryTable::class)),
    HospitalSummaryHistoryDataService
