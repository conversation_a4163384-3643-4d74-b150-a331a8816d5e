package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.OutcomeRequestScheduling
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.OutcomeRequestSchedulingTable

class OutcomeRequestSchedulingDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<OutcomeRequestScheduling>(factory.get(OutcomeRequestScheduling::class, OutcomeRequestSchedulingTable::class)),
    OutcomeRequestSchedulingDataService
