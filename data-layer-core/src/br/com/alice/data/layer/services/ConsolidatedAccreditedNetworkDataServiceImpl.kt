package br.com.alice.data.layer.services

import br.com.alice.data.layer.converters.ConsolidatedAccreditedNetworkConverter
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetwork
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ConsolidatedAccreditedNetworkTable

class ConsolidatedAccreditedNetworkDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ConsolidatedAccreditedNetwork>(
        factory.get(
            ConsolidatedAccreditedNetwork::class,
            ConsolidatedAccreditedNetworkTable::class,
            converter = ConsolidatedAccreditedNetworkConverter()
        )
    ),
    ConsolidatedAccreditedNetworkDataService
