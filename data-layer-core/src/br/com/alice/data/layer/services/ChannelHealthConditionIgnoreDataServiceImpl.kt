package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ChannelHealthConditionIgnore
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ChannelHealthConditionIgnoreTable

class ChannelHealthConditionIgnoreDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ChannelHealthConditionIgnore>(factory.get(ChannelHealthConditionIgnore::class, ChannelHealthConditionIgnoreTable::class)),
    ChannelHealthConditionIgnoreDataService
