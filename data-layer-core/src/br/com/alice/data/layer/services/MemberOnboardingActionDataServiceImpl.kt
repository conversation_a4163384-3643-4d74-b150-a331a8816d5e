package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.MemberOnboardingAction
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.MemberOnboardingActionTable

class MemberOnboardingActionDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<MemberOnboardingAction>(factory.get(MemberOnboardingAction::class, MemberOnboardingActionTable::class)),
    MemberOnboardingActionDataService
