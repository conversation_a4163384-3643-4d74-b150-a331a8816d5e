package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FinancialDataModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FinancialDataTable

class FinancialDataModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FinancialDataModel>(factory.get(FinancialDataModel::class, FinancialDataTable::class)),
    FinancialDataModelDataService
