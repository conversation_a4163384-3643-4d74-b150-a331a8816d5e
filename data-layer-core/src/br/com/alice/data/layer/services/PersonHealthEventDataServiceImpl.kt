package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonHealthEventTable

class PersonHealthEventDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<PersonHealthEvent>(factory.get(PersonHealthEvent::class, PersonHealthEventTable::class)),
    PersonHealthEventDataService
