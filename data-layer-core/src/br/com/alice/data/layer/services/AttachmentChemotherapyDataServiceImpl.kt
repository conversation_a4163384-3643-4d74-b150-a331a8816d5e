package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AttachmentChemotherapyModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AttachmentChemotherapyTable

class AttachmentChemotherapyDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AttachmentChemotherapyModel>(factory.get(AttachmentChemotherapyModel::class, AttachmentChemotherapyTable::class)),
    AttachmentChemotherapyModelDataService
