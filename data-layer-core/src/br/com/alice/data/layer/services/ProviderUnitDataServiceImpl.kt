package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ProviderUnitModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ProviderUnitTable

class ProviderUnitDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ProviderUnitModel>(factory.get(ProviderUnitModel::class, ProviderUnitTable::class)),
    ProviderUnitModelDataService
