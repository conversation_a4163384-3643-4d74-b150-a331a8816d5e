package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.SalesFirm
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.SalesFirmTable

class SalesFirmDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<SalesFirm>(factory.get(SalesFirm::class, SalesFirmTable::class)),
    SalesFirmDataService
