package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.RoutingRule
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.RoutingRuleTable

class RoutingRuleDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<RoutingRule>(factory.get(RoutingRule::class, RoutingRuleTable::class)),
    RoutingRuleDataService
