package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.CsatTemplate
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.CsatTemplateTable

class CsatTemplateDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<CsatTemplate>(
        factory.get(CsatTemplate::class, CsatTemplateTable::class)
    ),
    CsatTemplateDataService
