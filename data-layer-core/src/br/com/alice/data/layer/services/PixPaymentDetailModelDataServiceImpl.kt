package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PixPaymentDetailModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PixPaymentDetailTable

class PixPaymentDetailModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<PixPaymentDetailModel>(factory.get(PixPaymentDetailModel::class, PixPaymentDetailTable::class)),
    PixPaymentDetailModelDataService
