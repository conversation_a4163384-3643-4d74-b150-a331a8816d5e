package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.InsurancePortabilityRequestFileModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.InsurancePortabilityRequestFileTable

class InsurancePortabilityRequestFileModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<InsurancePortabilityRequestFileModel>(
        factory.get(InsurancePortabilityRequestFileModel::class, InsurancePortabilityRequestFileTable::class)
    ), InsurancePortabilityRequestFileModelDataService
