package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthProductSimulationGroup
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthProductSimulationGroupTable

class HealthProductSimulationGroupDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthProductSimulationGroup>(factory.get(HealthProductSimulationGroup::class, HealthProductSimulationGroupTable::class)), HealthProductSimulationGroupDataService

