package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ClinicalOutcomesConsolidatedCalculatorConf
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ClinicalOutcomesConsolidatedCalculatorConfTable

class ClinicalOutcomesConsolidatedCalculatorConfDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ClinicalOutcomesConsolidatedCalculatorConf>(
        factory.get(ClinicalOutcomesConsolidatedCalculatorConf::class, ClinicalOutcomesConsolidatedCalculatorConfTable::class)
    ),
    ClinicalOutcomesConsolidatedCalculatorConfDataService
