package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.InvoiceLiquidationTaxReceiptModel
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.InvoiceLiquidationTaxReceiptTable

class InvoiceLiquidationTaxReceiptModelDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<InvoiceLiquidationTaxReceiptModel>(factory.get(InvoiceLiquidationTaxReceiptModel::class, InvoiceLiquidationTaxReceiptTable::class)),
    InvoiceLiquidationTaxReceiptModelDataService
