package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.PersonHealthLogic
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.PersonHealthLogicTable

class PersonHealthLogicDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
        BaseDataServiceImpl<PersonHealthLogic>(factory.get(PersonHealthLogic::class, PersonHealthLogicTable::class)),
        PersonHealthLogicDataService
