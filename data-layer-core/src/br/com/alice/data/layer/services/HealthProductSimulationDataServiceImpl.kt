package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.HealthProductSimulation
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.HealthProductSimulationTable

class HealthProductSimulationDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<HealthProductSimulation>(factory.get(HealthProductSimulation::class, HealthProductSimulationTable::class)), HealthProductSimulationDataService

