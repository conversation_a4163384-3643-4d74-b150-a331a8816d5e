package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.ChannelsResult
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.ChannelsResultTable

class ChannelsResultDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<ChannelsResult>(factory.get(ChannelsResult::class, ChannelsResultTable::class)),
    ChannelsResultDataService
