package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.FileVault
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.FileVaultPiiTable

class FileVaultPiiDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<FileVault>(factory.get(FileVault::class, FileVaultPiiTable::class)),
    FileVaultPiiDataService
