package app.ehr_api_test

import rego.v1

import data.app.ehr_api

test_unauth_allowed if {
    actions_to_resources := {
        "view": [
            "Appointment",
            "StructuredAddress",
            "ContactModel",
            "HealthPlanTaskTemplate",
            "HealthPlanTaskGroupTemplate",
            "AppointmentTemplate",
            "OutcomeConf",
            "CassiSpecialistModel",
            "HealthEventsModel",
            "HealthCommunitySpecialistModel",
            "HealthProfessionalModel",
            "StaffModel",
            "MagicNumbersModel"
        ],
        "count": [
            "Appointment",
            "ContactModel",
            "HealthEventsModel"
        ],
        "delete": [
            "HealthEventsModel"
        ],
        "update": [
            "MagicNumbersModel"
        ],
        "create": [
            "MagicNumbersModel"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "opaType": "Unauthenticated"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_healthprofessional_allowed if {
    actions_to_resources := {
        "view": [
            "AppointmentMacro",
            "AppointmentProcedureExecuted",
            "AppointmentTemplate",
            "AssistanceSummaryModel",
            "ChannelFup",
            "HealthPlanTaskGroupTemplate",
            "HealthPlanTaskTemplate",
            "RefundCounterReferralModel",
            "ScreeningNavigation",
            "SuggestedProcedure",
            "TimelineAiSummaryReview"
        ],
        "count": [
            "AppointmentMacro",
            "AppointmentProcedureExecuted",
            "PersonHealthEvent",
            "SuggestedProcedure"
        ],
        "create": [
            "AppointmentProcedureExecuted",
            "RefundCounterReferralModel",
            "TimelineAiSummaryReview"
        ],
        "update": [
            "RefundCounterReferralModel",
            "TimelineAiSummaryReview"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "opaType": "HealthProfessionalSubject"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_external_health_professional_with_member_portifolio_allowed if {
    resources := {
        "AliceTestResultBundle",
        "AliceTestResultFile",
        "Appointment",
        "AppointmentEvolution",
        "AppointmentScheduleModel",
        "AssistanceCare",
        "ConsentRegistration",
        "CounterReferral",
        "DasaDiagnosticReport",
        "DbLaboratoryTestResult",
        "EinsteinAlergia",
        "EinsteinAtendimento",
        "EinsteinAvaliacaoInicial",
        "EinsteinDadosDeAlta",
        "EinsteinDiagnostico",
        "EinsteinEncaminhamento",
        "EinsteinMedicamento",
        "EinsteinProcedimento",
        "EinsteinResultadoExame",
        "EinsteinResumoInternacao",
        "EinsteinStructuredTestResult",
        "FhirBundle",
        "FhirDiagnosticReport",
        "FhirDocument",
        "FleuryTestResult",
        "FleuryTestResultFile",
        "HaocDocument",
        "HaocProntoAtendimentoResult",
        "HaocSumarioDeAltaResult",
        "HealthDeclaration",
        "InsurancePortabilityRequestModel",
        "MemberModel",
        "MemberModel",
        "MemberProductPriceModel",
        "OutcomeConf",
        "PersonClinicalAccount",
        "PersonEligibilityDuquesa",
        "PersonModel",
        "PersonOnboardingModel",
        "PersonTaskModel",
        "ProviderHealthDocumentModel",
        "ProviderHealthDocumentModel",
        "PublicTokenIntegration",
        "TertiaryIntentionTouchPoint",
        "TestResultFileModel",
        "Timeline"
    }

    every resource in resources {
        {1} == ehr_api.allow with input as {
            "cases": [
                {
                    "index": 1,
                    "action": "view",
                    "subject": {
                        "opaType": "ExternalHealthProfessionalSubject",
                        "memberInPortfolio": "true"
                    },
                    "resource": {
                        "opaType": resource
                    }
                }
            ]
        }
    }
}

test_external_health_professionals_allowed if {
    actions_to_resources := {
        "view": [
            "Timeline"
        ],
        "create": [
            "AppointmentEvolution",
            "AppointmentProcedureExecuted",
        ],
        "count": [
            "ActionPlanTask",
            "AppContentScreenDetail",
            "Appointment",
            "AppointmentCoordination",
            "AppointmentEvent",
            "AppointmentEvolution",
            "AssistanceCare",
            "CaseRecord",
            "ChannelComment",
            "ChannelHistory",
            "ClinicalBackground",
            "ClinicalOutcomeRecord",
            "ConsentRegistration",
            "ConsolidatedRewards",
            "ConsolidatedRewardsModel",
            "CounterReferral",
            "DasaDiagnosticReport",
            "DischargeSummary",
            "ExternalReferral",
            "ExternalReferralModel",
            "FhirBundle",
            "FhirDiagnosticReport",
            "FhirDocument",
            "GuiaModel",
            "HealthMeasurementModel",
            "HealthPlan",
            "HealthPlanTask",
            "HealthPlanTaskGroup",
            "HealthcareMap",
            "IntentionCoordination",
            "LaboratoryTestResult",
            "LaboratoryTestResultModel",
            "PersonCase",
            "PersonHealthcareTeamRecommendationModel",
            "PersonTeamAssociation",
            "Pregnancy",
            "PregnancyModel",
            "Risk",
            "TertiaryIntentionTouchPoint",
            "TestResultFeedback",
            "TestResultFile",
            "TestResultFileModel",
            "Timeline",
            "VideoCall",
            "WandaComment"
        ],
        "delete": [
            "AppointmentProcedureExecuted"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "opaType": "ExternalHealthProfessionalSubject"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_chief_risk_models_allowed_view if {
    resources := [
        "ActionPlanTask",
        "AliceTestResultBundle",
        "AppContentScreenDetail",
        "Appointment",
        "AppointmentCoordination",
        "AppointmentEvent",
        "AppointmentEvolution",
        "AppointmentMacro",
        "AppointmentProcedureExecuted",
        "AppointmentScheduleModel",
        "EinsteinStructuredTestResult",
        "ExternalReferral",
        "ExternalReferralModel",
        "FhirBundle",
        "FhirDiagnosticReport",
        "FhirDocument",
        "HealthSpecialistResourceBundleModel",
        "HealthcareMap",
        "HealthcareResourceModel",
        "HealthcareTeamModel",
        "InsurancePortabilityRequestModel",
        "IntentionCoordination",
        "LaboratoryTestResult",
        "LaboratoryTestResultModel",
        "MemberModel",
        "MemberProductPriceModel",
        "PersonHealthcareTeamRecommendationModel",
        "PersonIdentityValidationModel",
        "PersonOnboardingModel",
        "PersonTeamAssociation",
        "Timeline",
        "VideoCall",
        "WandaComment"
    ]

    every resource in resources {
        {1} == ehr_api.allow with input as {
            "cases": [
                {
                    "index": 1,
                    "action": "view",
                    "subject": {
                        "opaType": "ChiefRiskSubject"
                    },
                    "resource": {
                        "opaType": resource
                    }
                }
            ]
        }
    }
}

test_chief_risk_models_allowed_create_or_update if {
    actions_to_resources := {
        "create": [
            "Appointment",
            "AppointmentEvent",
            "AppointmentEvolution",
            "AppointmentTemplate",
            "CaseRecord",
            "CuriosityNoteModel",
            "DraftCommandModel",
            "HealthDeclaration",
            "HealthMeasurementModel",
            "HealthPlan",
            "HealthPlanTaskGroup",
            "HealthPlanTaskGroupTemplate",
            "HealthPlanTaskTemplate",
            "PersonAdditionalInfoModel",
            "PersonGracePeriod",
            "PersonInternalReference",
            "PregnancyModel",
            "Risk",
            "TestResultFileModel"
        ],
        "update": [
            "Appointment",
            "AppointmentEvent",
            "AppointmentEvolution",
            "AppointmentTemplate",
            "CuriosityNoteModel",
            "DraftCommandModel",
            "HealthDeclaration",
            "HealthMeasurementModel",
            "HealthPlan",
            "HealthPlanTaskGroup",
            "HealthPlanTaskGroupTemplate",
            "HealthPlanTaskTemplate",
            "PersonAdditionalInfoModel",
            "PersonGracePeriod",
            "PersonModel",
            "PregnancyModel",
            "Risk",
            "TestResultFileModel"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "opaType": "ChiefRiskSubject"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_chief_risk_models_allowed_view_count if {
    resources := [
        "ActionPlanTask",
        "AliceTestResultBundle",
        "AppContentScreenDetail",
        "Appointment",
        "AppointmentCoordination",
        "AppointmentEvent",
        "AppointmentEvolution",
        "ClinicalOutcomeRecord",
        "CompanyModel",
        "ConsentRegistration",
        "ConsolidatedRewards",
        "ConsolidatedRewardsModel",
        "CounterReferral",
        "HealthMeasurementModel",
        "HealthPlan",
        "HealthPlanTask",
        "HealthPlanTask",
        "HealthPlanTaskGroup",
        "HealthPlanTaskReferrals",
        "HealthSpecialistResourceBundleModel",
        "HealthcareMap",
        "HealthcareResourceModel",
        "HealthcareTeamModel",
        "ProviderModel",
        "ProviderUnitModel",
        "Risk",
        "TertiaryIntentionTouchPoint",
        "Timeline",
        "Timeline",
        "VideoCall",
        "WandaComment"
    ]

    every resource in resources {
        {1} == ehr_api.allow with input as {
            "cases": [
                {
                    "index": 1,
                    "action": "count",
                    "subject": {
                        "opaType": "ChiefRiskSubject"
                    },
                    "resource": {
                        "opaType": resource
                    }
                }
            ]
        }
    }
}

test_alice_health_professional_allowed if {
    actions_to_resources := {
        "count": [
            "ActionPlanTask",
            "AppContentScreenDetail",
            "Appointment",
            "AppointmentCoordination",
            "AppointmentEvent",
            "AppointmentEvolution",
            "AssistanceCare",
            "CaseRecord",
            "ChannelComment",
            "ChannelHistory",
            "ChannelTheme",
            "ClinicalBackground",
            "EinsteinDiagnostico",
            "EinsteinEncaminhamento",
            "EinsteinMedicamento",
            "EinsteinProcedimento",
            "EinsteinResultadoExame",
            "EinsteinResumoInternacao",
            "EinsteinStructuredTestResult",
            "LaboratoryTestResult",
            "LaboratoryTestResultModel",
            "PersonCase",
            "TestResultFileModel",
            "SpecialistOpinionMessage",
            "SuggestedProcedure"
        ],
        "create": [
            "GenericFileVault",
            "SpecialistOpinionMessage"
        ],
        "update": [
            "ActionPlanTask",
            "AppContentScreenDetail",
            "Appointment",
            "AppointmentCoordination",
            "AppointmentEvent",
            "AppointmentEvolution",
            "AppointmentTemplate",
            "CaseRecord",
            "ChannelComment",
            "ChannelHistory",
            "ChannelTheme",
            "ClinicalBackground",
            "ClinicalOutcomeRecord",
            "ConsentRegistration",
            "ConsolidatedRewards",
            "HealthMeasurementCategory",
            "HealthMeasurementModel",
            "PregnancyModel",
            "Risk",
            "Risk",
            "ServiceScriptNode",
            "TertiaryIntentionTouchPoint",
            "TestResultFeedback",
            "TestResultFile",
            "TestResultFileModel",
            "Timeline",
            "VideoCall",
            "WandaComment"
        ],
        "delete": [
            "AppointmentProcedureExecuted"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "opaType": "InternalHealthProfessionalSubject"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_alice_health_professional_allowed_update if {
    resources := [
        "ActionPlanTask",
        "AppContentScreenDetail",
        "Appointment",
        "AppointmentCoordination",
        "AppointmentEvent",
        "AppointmentEvolution",
        "AppointmentTemplate",
        "CaseRecord",
        "ChannelComment",
        "ChannelHistory",
        "ChannelTheme",
        "ClinicalBackground",
        "ClinicalOutcomeRecord",
        "ConsentRegistration",
        "ConsolidatedRewards",
        "HealthMeasurementCategory",
        "HealthMeasurementModel",
        "PregnancyModel",
        "Risk",
        "Risk",
        "ServiceScriptNode",
        "TertiaryIntentionTouchPoint",
        "TestResultFeedback",
        "TestResultFile",
        "TestResultFileModel",
        "Timeline",
        "VideoCall",
        "WandaComment"
    ]

    every resource in resources {
        {1} == ehr_api.allow with input as {
            "cases": [
                {
                    "index": 1,
                    "action": "update",
                    "subject": {
                        "opaType": "InternalHealthProfessionalSubject"
                    },
                    "resource": {
                        "opaType": resource
                    }
                }
            ]
        }
    }
}

test_alice_health_professional_allowed_view if {
    resources := [
        "AliceTestResultBundle",
        "AliceTestResultFile",
        "AppointmentMacro",
        "AppointmentScheduleModel",
        "AssistanceCare",
        "AssistanceSummaryModel",
        "CaseRecord",
        "ConsentRegistration",
        "DasaDiagnosticReport",
        "DbLaboratoryTestResult",
        "EinsteinAlergia",
        "EinsteinAtendimento",
        "EinsteinAvaliacaoInicial",
        "EinsteinDadosDeAlta",
        "EinsteinDiagnostico",
        "EinsteinEncaminhamento",
        "EinsteinMedicamento",
        "PersonModel",
        "TertiaryIntentionTouchPoint",
        "TestResultFileModel",
        "Timeline",
        "GenericFileVault",
        "HealthPlanTaskReferrals",
        "ProviderUnitModel",
        "SpecialistOpinion",
        "SpecialistOpinionMessage",
        "SuggestedProcedure"
    ]

    every resource in resources {
        {1} == ehr_api.allow with input as {
            "cases": [
                {
                    "index": 1,
                    "action": "view",
                    "subject": {
                        "opaType": "InternalHealthProfessionalSubject"
                    },
                    "resource": {
                        "opaType": resource
                    }
                }
            ]
        }
    }
}

test_navigator_ops_allowed if {
    actions_to_resources := {
        "view": [
            "CaseRecord",
            "PersonCase"
        ]
    }

    every role in ["NAVIGATOR_OPS", "CHIEF_NAVIGATOR_OPS"] {
        every action, resources in actions_to_resources {
            every resource in resources {
                {
                    1
                } == ehr_api.allow with input as {
                    "cases": [
                        {
                            "index": 1,
                            "action": action,
                            "subject": {
                                "role": role
                            },
                            "resource": {
                                "opaType": resource
                            }
                        }
                    ]
                }
            }
        }
    }
}

test_health_declaration_nurse_allowed if {
    actions_to_resources := {
        "view": [
            "Appointment",
            "HealthDeclaration",
            "InsurancePortabilityRequestModel",
            "MemberContractModel",
            "OnboardingContractModel",
            "PersonGracePeriod"
        ],
        "create": [
            "Appointment",
            "HealthDeclaration",
            "PersonGracePeriod"
        ],
        "update": [
            "Appointment",
            "HealthDeclaration",
            "PersonGracePeriod"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "role": "HEALTH_DECLARATION_NURSE"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_med_nurse_risc_allowed if {
    actions_to_resources := {
        "view": [
            "HealthDeclaration",
            "OnboardingContractModel",
            "PersonGracePeriod"
        ],
        "create": [
            "HealthDeclaration",
            "PersonGracePeriod"
        ],
        "update": [
            "HealthDeclaration",
            "PersonGracePeriod"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "role": "MED_RISK"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_assistance_care_professional_allowed if {
    actions_to_resources := {
        "create": [
            "Appointment",
            "AssistanceCare"
        ],
        "update": [
            "Appointment",
            "AssistanceCare"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "role": "DIGITAL_CARE_NURSE"
                        },
                        "resource": {
                            "opaType": resource,
                            "type": "ANNOTATION"
                        }
                    }
                ]
            }
        }
    }
}

test_physician_health_professional_ops_allowed if {
    actions_to_resources := {
        "create": [
            "Appointment",
            "AssistanceCare"
        ],
        "update": [
            "Appointment",
            "AssistanceCare"
        ],
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "opaType": "OnSiteProfessionalSubject"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_health_care_team_nurse_professional_allowed if {
    actions_to_resources := {
        "create": [
            "Appointment",
            "AssistanceCare"
        ],
        "update": [
            "Appointment",
            "AssistanceCare"
        ],
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "role": "HEALTHCARE_TEAM_NURSE"
                        },
                        "resource": {
                            "opaType": resource,
                            "type": "HEALTH_PLAN_CARE"
                        }
                    }
                ]
            }
        }
    }
}

test_immersion_health_professional_allowed if {
    actions_to_resources := {
        "create": [
            "Appointment"
        ],
        "update": [
            "Appointment"
        ],
    }

    roles := [
        "HEALTHCARE_TEAM_NURSE",
        "MANAGER_PHYSICIAN"
    ]

    every role in roles {
        every action, resources in actions_to_resources {
            every resource in resources {
                {1} == ehr_api.allow with input as {
                    "cases": [
                        {
                            "index": 1,
                            "action": action,
                            "subject": {
                                "role": role
                            },
                            "resource": {
                                "opaType": resource,
                                "type": "IMMERSION"
                            }
                        }
                    ]
                }
            }
        }
    }
}

test_cx_ops_professional_allowed if {
    actions_to_resources := {
        "view": [
            "MemberModel",
            "MemberProductPriceModel",
            "PersonAdditionalInfoModel",
            "PersonClinicalAccount",
            "PersonHealthEvent",
            "PersonInternalReference",
            "PersonModel",
            "PersonOnboardingModel"
        ],
        "count": [
            "PersonHealthEvent"
        ],
        "create": [
            "PersonHealthEvent"
        ],
        "update": [
            "PersonHealthEvent"
        ]
    }

    roles := [
        "CX_OPS"
    ]

    every role in roles {
        every action, resources in actions_to_resources {
            every resource in resources {
                {
                    1
                } == ehr_api.allow with input as {
                    "cases": [
                        {
                            "index": 1,
                            "action": action,
                            "subject": {
                                "role": role
                            },
                            "resource": {
                                "opaType": resource
                            }
                        }
                    ]
                }
            }
        }
    }
}

test_insurance_ops_allowed if {
    actions_to_resources := {
        "view": [
            "Appointment",
            "AppointmentEvolution",
            "AssistanceCare",
            "ClinicalBackground",
            "CounterReferral",
            "CuriosityNoteModel",
            "HealthDeclaration",
            "HealthFormQuestionAnswer",
            "HealthMeasurementModel",
            "HealthPlan",
            "HealthPlanTask",
            "HealthPlanTaskGroup",
            "MemberModel",
            "OngoingCompanyDeal",
            "PersonAdditionalInfoModel",
            "PersonClinicalAccount",
            "PersonInternalReference",
            "PersonModel",
            "PersonTaskModel",
            "TertiaryIntentionTouchPoint",
            "TestResultFileModel",
            "Timeline"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_health_ops_lead_allowed if {
    actions_to_resources := {
        "view": [
            "ActionPlanTask",
            "AppContentScreenDetail",
            "Appointment",
            "AppointmentCoordination",
            "AppointmentEvent",
            "AppointmentEvolution",
            "ClinicalOutcomeRecord",
            "ConsentRegistration",
            "ConsolidatedRewards",
            "ConsolidatedRewardsModel",
            "CounterReferral",
            "CuriosityNoteModel",
            "DasaDiagnosticReport",
            "DischargeSummary",
            "EinsteinAlergia",
            "EinsteinAtendimento",
            "EinsteinAvaliacaoInicial",
            "PersonHealthEvent",
            "WandaComment"
        ],
        "create": [
            "HealthPlan",
            "HealthPlanTask",
            "HealthPlanTaskGroup",
            "PersonTaskModel"
        ],
        "update": [
            "HealthPlan",
            "HealthPlanTask",
            "HealthPlanTaskGroup",
            "PersonTaskModel"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "role": "HEALTH_OPS_LEAD"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_product_and_tech_allowed if {
    actions_to_resources := {
        "view": [
            "HaocDocument",
            "HealthFormQuestionAnswer"
        ],
        "count": [
            "PersonHealthEvent"
        ],
        "create": [
            "AppointmentTemplate",
            "ContactModel",
            "HealthCommunitySpecialistModel",
            "HealthMeasurementCategoryModel",
            "HealthMeasurementTypeModel",
            "HealthPlanTaskGroupTemplate",
            "HealthPlanTaskTemplate",
            "MedicineModel",
            "ProviderTestCodeModel",
            "ProviderUnitModel",
            "ProviderUnitTestCodeModel",
            "ServiceScriptNode",
            "StructuredAddress",
            "TestCodeModel",
            "TestPreparationModel"
        ],
        "update": [
            "AppointmentTemplate",
            "ContactModel",
            "HealthCommunitySpecialistModel",
            "HealthMeasurementCategoryModel",
            "HealthMeasurementTypeModel",
            "HealthPlanTaskGroupTemplate",
            "HealthPlanTaskTemplate",
            "MedicineModel",
            "ProviderTestCodeModel",
            "ProviderUnitModel",
            "ProviderUnitTestCodeModel",
            "ServiceScriptNode",
            "StructuredAddress",
            "TestCodeModel",
            "TestPreparationModel"
        ],
        "delete": [
            "ProviderTestCodeModel"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "role": "PRODUCT_TECH"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_de_identified_hi_viewer_allowed if {
    resources :=  [
        "ActionPlanTask",
        "AppContentScreenDetail",
        "Appointment",
        "AppointmentCoordination",
        "AppointmentEvent",
        "AppointmentEvolution",
        "AssistanceCare",
        "CaseRecord",
        "ChannelComment",
        "ConsolidatedRewards",
        "PersonEligibilityDuquesa",
        "TestResultFeedback",
        "TestResultFile",
        "TestResultFileModel",
        "Timeline",
        "VideoCall",
        "WandaComment"
    ]

    every resource in resources {
        {1} == ehr_api.allow with input as {
            "cases": [
                {
                    "index": 1,
                    "action": "view",
                    "subject": {
                        "role": "DE_IDENTIFIED_HI_VIEWER"
                    },
                    "resource": {
                        "opaType": resource
                    }
                }
            ]
        }
    }
}

test_staff_subject_allowed if {
    actions_to_resources := {
        "view": [
            "AppointmentScheduleEventTypeModel",
            "AppointmentScheduleOptionModel",
            "BeneficiaryModel",
            "BeneficiaryOnboardingModel",
            "BeneficiaryOnboardingPhaseModel",
            "BudNode",
            "CassiMemberModel",
            "HealthFormSection",
            "HealthMeasurementCategoryModel",
            "HealthMeasurementTypeModel",
            "HealthProfessionalModel",
            "HealthcareAdditionalTeam",
            "HealthcareTeamModel",
            "MedicalSpecialtyModel",
            "MedicineModel",
            "MvAuthorizedProcedureModel",
            "PrescriptionSentenceModel",
            "PriceListingModel",
            "TestCodeModel",
            "TestCodePackageModel",
            "TotvsGuiaModel",
            "TrackPersonABModel",
            "TussProcedureSpecialtyModel"
        ],
        "count": [
            "BeneficiaryModel",
            "BeneficiaryOnboardingModel",
            "BeneficiaryOnboardingPhaseModel",
            "CassiMemberModel",
            "CompanyModel",
            "ProviderUnitModel"
        ],
        "create": [
            "AppointmentAutofillHistory",
            "ServiceScriptExecution",
            "ServiceScriptNavigation",
            "ServiceScriptNavigationGroup"
        ],
        "update": [
            "ServiceScriptExecution",
            "ServiceScriptNavigation",
            "ServiceScriptNavigationGroup"
        ],
    }


    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "opaType": "StaffSubject",
                            "opaSuperTypes": [
                                "StaffSubject"
                            ]
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_health_community_specialist_allowed if {
    actions_to_resources := {
        "view": [
            "ActionPlanTask",
            "AliceTestResultBundle",
            "AliceTestResultFile",
            "AppContentScreenDetail",
            "Appointment",
            "AppointmentCoordination",
            "AppointmentEvent",
            "AppointmentEvolution",
            "ContactModel",
            "FhirDocument",
            "FhirDocument",
            "FleuryTestResult",
            "FleuryTestResultFile",
            "HealthLogicRecord",
            "HealthMeasurement",
            "HealthMeasurementModel",
            "PersonCase",
            "PersonClinicalAccount",
            "PersonEligibilityDuquesa",
            "PersonHealthEvent",
            "PersonHealthGoalModel",
            "ProviderHealthDocumentModel",
            "TussProcedureSpecialtyModel",
            "VideoCall",
            "WandaComment"
        ],
        "count": [
            "ActionPlanTask",
            "AppContentScreenDetail",
            "Appointment",
            "AppointmentCoordination",
            "AppointmentEvent",
            "AppointmentEvolution",
            "AssistanceCare",
            "VideoCall",
            "WandaComment"
        ],
        "create": [
            "CounterReferral",
            "CounterReferralRelevance",
            "GenericFileVault",
            "HealthCommunityUnreferencedAccessModel",
            "SpecialistOpinion",
            "SpecialistOpinionMessage"
        ],
        "update": [
            "CounterReferral",
            "CounterReferralRelevance",
            "HealthCommunityUnreferencedAccessModel",
            "SpecialistOpinion"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {
                1
            } == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "opaType": "HealthCommunitySpecialistModel"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_health_specialist_allowed if {
    actions_to_resources := {
        "view": [
            "ActionPlanTask",
            "AliceTestResultBundle",
            "AliceTestResultFile",
            "AppContentScreenDetail",
            "Appointment",
            "AppointmentCoordination",
            "AppointmentEvent",
            "AppointmentEvolution",
            "PublicTokenIntegration",
            "Risk",
            "TertiaryIntentionTouchPoint",
            "TestResultFeedback",
            "TestResultFile",
            "TestResultFileModel",
            "Timeline",
            "VideoCall",
            "WandaComment"
        ],
        "count": [
            "ActionPlanTask",
            "HealthPlanTask",
            "HealthPlanTaskGroup",
            "HealthcareMap",
            "IntentionCoordination",
            "LaboratoryTestResult",
            "TestResultFile",
            "TestResultFileModel",
            "Timeline",
            "VideoCall",
            "WandaComment"
        ],
        "create": [
            "CounterReferral",
            "CounterReferralRelevance",
            "GenericFileVault",
            "HealthCommunityUnreferencedAccessModel",
            "SpecialistOpinion",
            "SpecialistOpinionMessage"
        ],
        "update": [
            "CounterReferral",
            "CounterReferralRelevance",
            "HealthCommunityUnreferencedAccessModel",
            "SpecialistOpinion"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "opaType": "StaffModel",
                            "role": "COMMUNITY"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_all_staff_model_allowed if {
    actions_to_resources := {
        "view": [
            "BeneficiaryModel",
            "BeneficiaryOnboardingModel",
            "BeneficiaryOnboardingPhaseModel",
            "CassiMemberModel",
            "CompanyModel"
        ],
        "count": [
            "BeneficiaryModel",
            "BeneficiaryOnboardingModel",
            "BeneficiaryOnboardingPhaseModel",
            "CassiMemberModel",
            "CompanyModel"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "opaType": "StaffModel"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_insurance_ops_community_success_allowed if {
    actions_to_resources := {
        "view": [
            "AliceTestResultBundle",
            "AliceTestResultFile",
            "Appointment",
            "AppointmentEvolution",
            "AssistanceCare",
            "ClinicalBackground",
            "ConsentRegistration",
            "DbLaboratoryTestResult",
            "EinsteinAlergia",
            "EinsteinAtendimento",
            "TertiaryIntentionTouchPoint",
            "TestResultFileModel",
            "Timeline"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "role": "INSURANCE_OPS_COMMUNITY_SUCCESS"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_health_nurse_alice_house_allowed if {
    actions_to_resources := {
        "view": [
            "AliceTestResultBundle",
            "AliceTestResultFile",
            "Appointment",
            "PersonTaskModel",
            "ProviderHealthDocumentModel",
            "ProviderHealthDocumentModel",
            "PublicTokenIntegration",
            "TertiaryIntentionTouchPoint",
            "TestResultFileModel",
            "Timeline"
        ],
        "create": [
            "ClinicalBackground",
            "CuriosityNoteModel",
            "HealthDeclaration",
            "HealthMeasurementModel",
            "PersonClinicalAccount",
            "PersonInternalReference",
            "PersonTaskModel"
        ],
        "update": [
            "ClinicalBackground",
            "CuriosityNoteModel",
            "PersonInternalReference",
            "PersonTaskModel"
        ]
    }

    every action, resources in actions_to_resources {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "role": "TECHNIQUE_NURSE"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_tertiary_intention_touch_point_allowed if {

    every action in ["view","create","update"] {
        {1} == ehr_api.allow with input as {
            "cases": [
                {
                    "index": 1,
                    "action": action,
                    "subject": {
                        "role": "CHIEF_PHYSICIAN"
                    },
                    "resource": {
                        "opaType": "TertiaryIntentionTouchPoint"
                    }
                }
            ]
        }
    }
}

test_product_tech_health_allowed if {
    resources := [
        "ActionPlanTask",
        "AppContentScreenDetail",
        "Appointment",
        "EinsteinDiagnostico",
        "EinsteinEncaminhamento",
        "EinsteinMedicamento",
        "EinsteinProcedimento",
        "EinsteinResultadoExame",
        "EinsteinResumoInternacao",
        "EinsteinStructuredTestResult",
        "HealthMeasurementModel",
        "HealthPlan",
        "HealthPlanTask",
        "HealthPlanTaskGroup",
        "HealthSpecialistResourceBundleModel",
        "HealthcareMap",
        "Pregnancy",
        "PregnancyModel",
        "Risk",
        "TertiaryIntentionTouchPoint",
        "TestResultFeedback",
        "TestResultFile",
        "TestResultFileModel",
        "Timeline",
        "VideoCall",
        "WandaComment"
    ]

    every resource in resources {
        {1} == ehr_api.allow with input as {
            "cases": [
                {
                    "index": 1,
                    "action": "view",
                    "subject": {
                        "role": "PRODUCT_TECH_HEALTH"
                    },
                    "resource": {
                        "opaType": resource
                    }
                }
            ]
        }
    }
}

test_health_audit_subject_allowed if {
    resources := [
        "ActionPlanTask",
        "AliceTestResultBundle",
        "AliceTestResultFile",
        "AppContentScreenDetail",
        "Appointment",
        "AppointmentCoordination",
        "AppointmentEvent",
        "AppointmentEvolution",
        "AppointmentTemplate",
        "AssistanceCare",
        "CaseRecord",
        "ChannelComment",
        "ChannelHistory",
        "ChannelTheme",
        "ClinicalBackground",
        "ClinicalOutcomeRecord",
        "ConsentRegistration",
        "EinsteinDadosDeAlta",
        "EinsteinDiagnostico",
        "EinsteinDiagnostico",
        "EinsteinEncaminhamento",
        "EinsteinEncaminhamento",
        "EinsteinMedicamento",
        "GuiaModel",
        "HDataOverview",
        "HLAdherence",
        "HaocDocument",
        "HaocFhirProcess",
        "HaocProntoAtendimentoResult",
        "HealthcareMap",
        "InsurancePortabilityRequestModel",
        "IntentionCoordination",
        "LaboratoryTestResult",
        "LaboratoryTestResultModel",
        "ProviderHealthDocumentModel",
        "ProviderHealthDocumentModel",
        "PublicTokenIntegration",
        "Risk",
        "TertiaryIntentionTouchPoint",
        "TestResultFeedback",
        "TestResultFile",
        "TestResultFileModel",
        "Timeline",
        "VideoCall",
        "WandaComment"
    ]

    every action in [
        "view",
        "count"
    ] {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "opaType": "HealthAuditSubject"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_health_ops_subject_allowed if {
    resources := [
        "ActionPlanTask",
        "AliceTestResultBundle",
        "AliceTestResultFile",
        "AppContentScreenDetail",
        "Appointment",
        "AppointmentCoordination",
        "AppointmentEvent",
        "AppointmentEvolution",
        "EinsteinDadosDeAlta",
        "EinsteinDiagnostico",
        "EinsteinDiagnostico",
        "EinsteinEncaminhamento",
        "EinsteinEncaminhamento",
        "EinsteinMedicamento",
        "EinsteinMedicamento",
        "EinsteinProcedimento",
        "EinsteinProcedimento",
        "EinsteinResultadoExame",
        "EinsteinResultadoExame",
        "EinsteinResumoInternacao",
        "EinsteinResumoInternacao",
        "EinsteinStructuredTestResult",
        "EinsteinStructuredTestResult",
        "ExternalReferral",
        "ExternalReferralModel",
        "TestResultFile",
        "TestResultFileModel",
        "Timeline",
        "VideoCall",
        "WandaComment"
    ]

    every action in ["view", "count"] {
        every resource in resources {
            {1} == ehr_api.allow with input as {
                "cases": [
                    {
                        "index": 1,
                        "action": action,
                        "subject": {
                            "opaType": "HealthOpsSubject"
                        },
                        "resource": {
                            "opaType": resource
                        }
                    }
                ]
            }
        }
    }
}

test_alice_health_professional_or_navigator_allowed if {
    every action in ["view", "create"] {
        {1} == ehr_api.allow with input as {
            "cases": [
                {
                    "index": 1,
                    "action": action,
                    "subject": {
                        "role": "MANAGER_NUTRITIONIST"
                    },
                    "resource": {
                        "opaType": "TestResultFeedback"
                    }
                }
            ]
        }
    }
}
