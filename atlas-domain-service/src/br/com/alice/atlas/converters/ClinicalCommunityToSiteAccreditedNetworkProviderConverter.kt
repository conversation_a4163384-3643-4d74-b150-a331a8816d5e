package br.com.alice.atlas.converters

import br.com.alice.data.layer.models.ProductBundle
import br.com.alice.data.layer.models.Provider
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.ReferencedModelClass
import br.com.alice.data.layer.models.SiteAccreditedNetworkProvider
import br.com.alice.data.layer.models.SiteAccreditedNetworkProviderType
import br.com.alice.data.layer.models.StructuredAddress

object ClinicalCommunityToSiteAccreditedNetworkProviderConverter {
    fun convert(
        provider: Provider,
        providerUnits: List<ProviderUnit>,
        productBundle: ProductBundle,
        addresses: List<StructuredAddress>,
        active: Boolean = false
    ): SiteAccreditedNetworkProvider =
        SiteAccreditedNetworkProvider(
            name = provider.name,
            type = convertType(provider),
            categories = defineCategories(providerUnits),
            referencedModelClass = ReferencedModelClass.CLINICAL_COMMUNITY,
            referencedModelId = provider.id,
            active = active,
            icon = provider.icon,
            isFlagship = provider.flagship,
            productBundleId = productBundle.id,
            productBundleIds = listOf(productBundle.id),
            addresses = addresses.map { StructuredAddressToProviderAddressConverter.convert(it) },
        )

    private fun defineCategories(
        providerUnits: List<ProviderUnit>
    ): List<String> =
        providerUnits.map { it.type.description }.distinct().sorted()

    private fun convertType(provider: Provider) =
        when (provider.type) {
            ProviderType.HOSPITAL,
            ProviderType.ACCOMMODATION,
            ProviderType.CHILDREN,
            ProviderType.CLINICAL,
            ProviderType.MATERNITY,
            ProviderType.VACCINE,
            ProviderType.MEDICAL_COMPANY,
            ProviderType.CLINICAL_COMMUNITY -> SiteAccreditedNetworkProviderType.HOSPITAL
            ProviderType.LABORATORY -> SiteAccreditedNetworkProviderType.LABORATORY
        }
}
