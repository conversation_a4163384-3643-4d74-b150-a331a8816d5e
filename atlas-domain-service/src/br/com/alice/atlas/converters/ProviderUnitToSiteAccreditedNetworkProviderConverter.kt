package br.com.alice.atlas.converters

import br.com.alice.data.layer.models.ProductBundle
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.ReferencedModelClass
import br.com.alice.data.layer.models.SiteAccreditedNetworkProvider
import br.com.alice.data.layer.models.SiteAccreditedNetworkProviderType
import br.com.alice.data.layer.models.StructuredAddress


object ProviderUnitToSiteAccreditedNetworkProviderConverter {
    fun convert(
        providerUnit: ProviderUnit,
        productBundle: ProductBundle,
        addresses: List<StructuredAddress>,
        active: Boolean = false
    ): SiteAccreditedNetworkProvider {

        return SiteAccreditedNetworkProvider(
            name = providerUnit.name,
            type = convertType(providerUnit),
            categories = listOf(providerUnit.type.description),
            referencedModelClass = ReferencedModelClass.PROVIDER_UNIT,
            referencedModelId = providerUnit.id,
            active = active,
            isFlagship = false,
            productBundleId = productBundle.id,
            addresses = addresses.map { StructuredAddressToProviderAddressConverter.convert(it) }
        )
    }

    fun updateProviderUnitBasedFields(
        oldModel: SiteAccreditedNetworkProvider,
        providerUnit: ProviderUnit,
        addresses: List<StructuredAddress>,
        active: Boolean = false
    ): SiteAccreditedNetworkProvider {

        return oldModel.copy(
            name = providerUnit.name,
            type = convertType(providerUnit),
            categories = listOf(providerUnit.type.description),
            active = active,
            isFlagship = false,
            addresses = addresses.map { StructuredAddressToProviderAddressConverter.convert(it) }
        )
    }

    private fun convertType(providerUnit: ProviderUnit) =
        when (providerUnit.type) {
            ProviderUnit.Type.HOSPITAL,
            ProviderUnit.Type.HOSPITAL_CHILDREN,
            ProviderUnit.Type.CLINICAL,
            ProviderUnit.Type.MATERNITY,
            ProviderUnit.Type.ALICE_HOUSE,
            ProviderUnit.Type.EMERGENCY_UNITY,
            ProviderUnit.Type.EMERGENCY_UNITY_CHILDREN,
            ProviderUnit.Type.VACCINE,
            ProviderUnit.Type.MEDICAL_COMPANY,
            ProviderUnit.Type.CLINICAL_COMMUNITY -> SiteAccreditedNetworkProviderType.HOSPITAL
            ProviderUnit.Type.LABORATORY -> SiteAccreditedNetworkProviderType.LABORATORY
        }
}
