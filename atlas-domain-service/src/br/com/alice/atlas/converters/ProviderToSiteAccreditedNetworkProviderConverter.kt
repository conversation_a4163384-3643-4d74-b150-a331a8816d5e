package br.com.alice.atlas.converters

import br.com.alice.data.layer.models.ProductBundle
import br.com.alice.data.layer.models.Provider
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.ReferencedModelClass
import br.com.alice.data.layer.models.SiteAccreditedNetworkProvider
import br.com.alice.data.layer.models.SiteAccreditedNetworkProviderType
import br.com.alice.data.layer.models.StructuredAddress


object ProviderToSiteAccreditedNetworkProviderConverter {
    fun convert(
        provider: Provider,
        providerUnits: List<ProviderUnit>,
        productBundle: ProductBundle,
        addresses: List<StructuredAddress>,
        active: Boolean = false
    ): SiteAccreditedNetworkProvider {

        return SiteAccreditedNetworkProvider(
            name = provider.name,
            type = convertType(provider),
            categories = defineCategories(providerUnits),
            referencedModelClass = ReferencedModelClass.PROVIDER,
            referencedModelId = provider.id,
            active = active,
            icon = provider.icon,
            isFlagship = provider.flagship,
            productBundleId = productBundle.id,
            addresses = addresses.map { StructuredAddressToProviderAddressConverter.convert(it) }
        )
    }

    fun updateProviderBasedFields(
        oldModel: SiteAccreditedNetworkProvider,
        provider: Provider,
        providerUnits: List<ProviderUnit>,
        addresses: List<StructuredAddress>,
        active: Boolean = false
    ): SiteAccreditedNetworkProvider {

        return oldModel.copy(
            name = provider.name,
            type = convertType(provider),
            categories = defineCategories(providerUnits),
            active = active,
            icon = provider.icon,
            isFlagship = provider.flagship,
            addresses = addresses.map { StructuredAddressToProviderAddressConverter.convert(it) }
        )
    }

    private fun defineCategories(
        providerUnits: List<ProviderUnit>
    ): List<String> =
        providerUnits.map { it.type.description }.distinct().sorted()

    private fun convertType(provider: Provider) =
        when (provider.type) {
            ProviderType.HOSPITAL,
            ProviderType.ACCOMMODATION,
            ProviderType.CHILDREN,
            ProviderType.CLINICAL,
            ProviderType.MATERNITY,
            ProviderType.VACCINE,
            ProviderType.MEDICAL_COMPANY,
            ProviderType.CLINICAL_COMMUNITY -> SiteAccreditedNetworkProviderType.HOSPITAL
            ProviderType.LABORATORY -> SiteAccreditedNetworkProviderType.LABORATORY
        }


}
