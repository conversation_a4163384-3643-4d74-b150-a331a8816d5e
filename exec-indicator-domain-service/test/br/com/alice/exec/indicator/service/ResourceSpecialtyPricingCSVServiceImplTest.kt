package br.com.alice.exec.indicator.service

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CSVPricingUpdateError
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPrice
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdate
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdateStatus
import br.com.alice.data.layer.models.TierType
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleService
import br.com.alice.exec.indicator.client.UploadPriceChangesRequest
import br.com.alice.exec.indicator.client.PricingUpdateHistoryFilters
import br.com.alice.exec.indicator.models.ProcessingResourceBundleSpecialtyPricingUpdateResponse
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryItem
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyPricingUpdateHistoryWithCount
import br.com.alice.exec.indicator.service.ResourceSpecialtyPricingCSVServiceImpl.Companion.EITA_FILE_VAULT_STORAGE_DOMAIN
import br.com.alice.exec.indicator.service.ResourceSpecialtyPricingCSVServiceImpl.Companion.EITA_FILE_VAULT_STORAGE_NAMESPACE
import br.com.alice.exec.indicator.service.internal.ResourceBundleSpecialtyPricingService
import br.com.alice.exec.indicator.service.internal.ResourceBundleSpecialtyPricingUpdateService
import br.com.alice.exec.indicator.service.internal.ResourceBundleSpecialtyService
import br.com.alice.filevault.client.FileVaultActionService
import br.com.alice.filevault.models.FileType
import br.com.alice.filevault.models.GenericVaultUploadByteArray
import br.com.alice.filevault.models.VaultResponse
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import java.io.ByteArrayInputStream
import java.io.File
import java.io.InputStreamReader
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class ResourceSpecialtyPricingCSVServiceImplTest {

    private val resourceBundleSpecialtyService: ResourceBundleSpecialtyService = mockk()
    private val resourceBundleSpecialtyPricingService: ResourceBundleSpecialtyPricingService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val healthSpecialistResourceBundleService: HealthSpecialistResourceBundleService = mockk()
    private val resourceBundleSpecialtyPricingUpdateService: ResourceBundleSpecialtyPricingUpdateService = mockk()
    private val fileVaultActionService: FileVaultActionService = mockk()
    private val staffService: StaffService = mockk()

    private val service = ResourceSpecialtyPricingCSVServiceImpl(
        resourceBundleSpecialtyService,
        resourceBundleSpecialtyPricingService,
        medicalSpecialtyService,
        healthSpecialistResourceBundleService,
        resourceBundleSpecialtyPricingUpdateService,
        fileVaultActionService,
        staffService
    )

    private val healthSpecialistResourceBundle = TestModelFactory.buildHealthSpecialistResourceBundle()
    private val medicalSpecialty = TestModelFactory.buildMedicalSpecialty()

    private val resourceBundleSpecialty = TestModelFactory.buildResourceBundleSpecialty(
        resourceBundleId = healthSpecialistResourceBundle.id,
        specialtyId = medicalSpecialty.id,
    )

    private val pricing = TestModelFactory.buildResourceBundleSpecialtyPricing(
        resourceBundleSpecialtyId = resourceBundleSpecialty.id,
        prices = listOf(
            ResourceBundleSpecialtyPrice(
                tier = SpecialistTier.TALENTED,
                price = BigDecimal(100.15),
                productTier = TierType.TIER_0
            ),
            ResourceBundleSpecialtyPrice(
                tier = SpecialistTier.SUPER_EXPERT,
                price = BigDecimal(200.39),
                productTier = TierType.TIER_1
            )
        )
    )

    private val resourcePricingCSV = ResourceSpecialtyPricingCSV(
        resourceBundleSpecialtyId = resourceBundleSpecialty.id,
        medicalSpecialtyName = medicalSpecialty.name,
        aliceCode = healthSpecialistResourceBundle.code,
        primaryTuss = healthSpecialistResourceBundle.primaryTuss,
        description = healthSpecialistResourceBundle.description,
        executionEnvironment = healthSpecialistResourceBundle.executionEnvironment,
        talentedT3 = pricing.getPriceByTierAndProductTier(SpecialistTier.TALENTED, TierType.TIER_3),
        talentedT2 = pricing.getPriceByTierAndProductTier(SpecialistTier.TALENTED, TierType.TIER_2),
        talentedT1 = pricing.getPriceByTierAndProductTier(SpecialistTier.TALENTED, TierType.TIER_1),
        talentedT0 = pricing.getPriceByTierAndProductTier(SpecialistTier.TALENTED, TierType.TIER_0),
        expertT2 = pricing.getPriceByTierAndProductTier(SpecialistTier.EXPERT, TierType.TIER_2),
        expertT1 = pricing.getPriceByTierAndProductTier(SpecialistTier.EXPERT, TierType.TIER_1),
        expertT0 = pricing.getPriceByTierAndProductTier(SpecialistTier.EXPERT, TierType.TIER_0),
        superExpertT1 = pricing.getPriceByTierAndProductTier(SpecialistTier.SUPER_EXPERT, TierType.TIER_1),
        superExpertT0 = pricing.getPriceByTierAndProductTier(SpecialistTier.SUPER_EXPERT, TierType.TIER_0),
        ultraExpertT0 = pricing.getPriceByTierAndProductTier(SpecialistTier.ULTRA_EXPERT, TierType.TIER_0)
    )

    private val csvFile = File(javaClass.classLoader.getResource("generated-csv-example.csv")!!.path)
    private val fileVaultId = RangeUUID.generate()
    private val fileName = "generated-csv-example.csv"

    val genericVaultByteArray = GenericVaultUploadByteArray(
        domain = EITA_FILE_VAULT_STORAGE_DOMAIN,
        namespace = EITA_FILE_VAULT_STORAGE_NAMESPACE,
        originalFileName = csvFile.name,
        fileContent = ByteArray(0),
        fileType = FileType.fromExtension("csv")!!,
        fileSize = 0L
    )

    private val genericFileVault = TestModelFactory.buildGenericFileVault(
        id = fileVaultId,
        domain = EITA_FILE_VAULT_STORAGE_DOMAIN,
        namespace = EITA_FILE_VAULT_STORAGE_NAMESPACE,
        originalFileName = fileName,
        fileSize = 0L
    )

    @Test
    fun `generate all`(): Unit = runBlocking {
        coEvery {
            resourceBundleSpecialtyService.findAllActive()
        } returns listOf(resourceBundleSpecialty)

        coEvery {
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(listOf(resourceBundleSpecialty.id))
        } returns listOf(pricing)

        coEvery {
            medicalSpecialtyService.getByIds(listOf(medicalSpecialty.id))
        } returns listOf(medicalSpecialty)

        coEvery {
            healthSpecialistResourceBundleService.findByIds(listOf(healthSpecialistResourceBundle.id))
        } returns listOf(healthSpecialistResourceBundle)

        val result = service.generate(emptyList())

        assertThat(result).isSuccess()

        val csvBytes = result.get().bytes
        val reader = InputStreamReader(ByteArrayInputStream(csvBytes))
        val csvFormat = CSVFormat.DEFAULT
            .withFirstRecordAsHeader()
            .withDelimiter(',')
            .withIgnoreSurroundingSpaces()
            .withRecordSeparator('\n')

        val parser = CSVParser(reader, csvFormat)

        val records = parser.records
        Assertions.assertThat(records).hasSize(1)

        val record = records[0]

        Assertions.assertThat(record.get("RESOURCE_BUNDLE_SPECIALTY_ID"))
            .isEqualTo(resourceBundleSpecialty.id.toString())
        Assertions.assertThat(record.get("Especialidade")).isEqualTo(medicalSpecialty.name)
        Assertions.assertThat(record.get("Alice Code")).isEqualTo(healthSpecialistResourceBundle.code)
        Assertions.assertThat(record.get("TALENTED T0")).isEqualTo(resourcePricingCSV.talentedT0.toString())
        Assertions.assertThat(record.get("SUPER EXPERT T1")).isEqualTo(resourcePricingCSV.superExpertT1.toString())

        parser.close()

        coVerifyOnce {
            resourceBundleSpecialtyService.findAllActive()
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(listOf(resourceBundleSpecialty.id))
            medicalSpecialtyService.getByIds(listOf(medicalSpecialty.id))
            healthSpecialistResourceBundleService.findByIds(listOf(healthSpecialistResourceBundle.id))
        }

    }

    @Test
    fun `generate passing id if resource bundle specialties is not found`() = runBlocking {
        val resourceBundleSpecialtyIds = listOf(resourceBundleSpecialty.id)
        coEvery {
            resourceBundleSpecialtyService.findByIds(resourceBundleSpecialtyIds)
        } returns emptyList()

        coEvery {
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(resourceBundleSpecialtyIds)
        } returns emptyList()

        val result = service.generate(resourceBundleSpecialtyIds)
        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce {
            resourceBundleSpecialtyService.findByIds(resourceBundleSpecialtyIds)
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(resourceBundleSpecialtyIds)
        }

    }

    @Test
    fun `generate CSV response correctly`() = runBlocking {
        val resourceBundleSpecialtyIds = listOf(resourceBundleSpecialty.id)
        coEvery {
            resourceBundleSpecialtyService.findByIds(resourceBundleSpecialtyIds)
        } returns listOf(resourceBundleSpecialty)

        coEvery {
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(resourceBundleSpecialtyIds)
        } returns listOf(pricing)

        val medicalSpecialtyIds = listOf(medicalSpecialty.id)
        coEvery {
            medicalSpecialtyService.getByIds(medicalSpecialtyIds)
        } returns listOf(medicalSpecialty)

        val resourceBundleIds = listOf(healthSpecialistResourceBundle.id)
        coEvery {
            healthSpecialistResourceBundleService.findByIds(resourceBundleIds)
        } returns listOf(healthSpecialistResourceBundle)

        val result = service.generate(resourceBundleSpecialtyIds)
        assertThat(result).isSuccess()

        val csvBytes = result.get().bytes
        val reader = InputStreamReader(ByteArrayInputStream(csvBytes))

        val csvFormat = CSVFormat.DEFAULT
            .withFirstRecordAsHeader()
            .withDelimiter(',')
            .withIgnoreSurroundingSpaces()
            .withRecordSeparator('\n')

        val parser = CSVParser(reader, csvFormat)
        val records = parser.records

        Assertions.assertThat(records).hasSize(1)

        val record = records[0]

        Assertions.assertThat(record.get("RESOURCE_BUNDLE_SPECIALTY_ID"))
            .isEqualTo(resourceBundleSpecialty.id.toString())
        Assertions.assertThat(record.get("Especialidade")).isEqualTo(medicalSpecialty.name)
        Assertions.assertThat(record.get("Alice Code")).isEqualTo(healthSpecialistResourceBundle.code)

        Assertions.assertThat(record.get("TALENTED T0")).isEqualTo(resourcePricingCSV.talentedT0.toString())
        Assertions.assertThat(record.get("SUPER EXPERT T1")).isEqualTo(resourcePricingCSV.superExpertT1.toString())

        parser.close()

        coVerifyOnce {
            resourceBundleSpecialtyService.findByIds(resourceBundleSpecialtyIds)
            resourceBundleSpecialtyPricingService.getCurrentlyActiveByResourceBundleSpecialtyId(resourceBundleSpecialtyIds)
            medicalSpecialtyService.getByIds(medicalSpecialtyIds)
            healthSpecialistResourceBundleService.findByIds(resourceBundleIds)
        }

    }


    @Test
    fun `getProcessingResourceBundleSpecialtyPricingUpdate should return processing response when update exists`() = runBlocking {
        val update = TestModelFactory.buildResourceBundleSpecialtyPricingUpdate()

        coEvery { resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate() } returns update.success()

        val result = service.getProcessingResourceBundleSpecialtyPricingUpdate()

        val expectedResponse = ProcessingResourceBundleSpecialtyPricingUpdateResponse(
            isProcessing = true,
            resourceBundleSpecialtyPricingUpdate = update
        )

        assertEquals(com.github.kittinunf.result.Result.success(expectedResponse), result)
    }

    @Test
    fun `getProcessingResourceBundleSpecialtyPricingUpdate should return not processing response when no update exists`() = runBlocking {
        coEvery {
            resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate()
        } returns com.github.kittinunf.result.Result.failure(NotFoundException())

        val result = service.getProcessingResourceBundleSpecialtyPricingUpdate()

        val expectedResponse = ProcessingResourceBundleSpecialtyPricingUpdateResponse(
            isProcessing = false
        )

        assertEquals(Result.success(expectedResponse), result)
    }

    @Test
    fun `generateFailedLinesFile should return csv file`() = runBlocking {
        val resourceBundleSpecialtyPricingUpdateId = RangeUUID.generate()
        val resourceBundleSpecialtyPricingUpdate = TestModelFactory.buildResourceBundleSpecialtyPricingUpdate(
            failedRowsErrors = listOf(
                CSVPricingUpdateError(
                    row = 1,
                    error = "Error 1",
                )
            )
        )

        val vaultResponse = VaultResponse(
            id = resourceBundleSpecialtyPricingUpdate.fileVaultId,
            fileName = "failed_lines.csv",
            type = "text/csv",
            vaultUrl = "example.com",
            fileSize = 1234,
            url = "file://${System.getProperty("user.dir")}/testResources/failed_lines.csv"
        )

        coEvery {
            resourceBundleSpecialtyPricingUpdateService.get(resourceBundleSpecialtyPricingUpdateId)
        } returns resourceBundleSpecialtyPricingUpdate.success()

        coEvery {
            fileVaultActionService.securedGenericLink(resourceBundleSpecialtyPricingUpdate.fileVaultId)
        } returns vaultResponse.success()

        val result = service.generateFailedLinesFile(resourceBundleSpecialtyPricingUpdateId)

        assertThat(result).isSuccess()

        val csvBytes = result.get().bytes
        val reader = InputStreamReader(ByteArrayInputStream(csvBytes))

        val csvFormat = CSVFormat.DEFAULT
            .withFirstRecordAsHeader()
            .withDelimiter(',')
            .withIgnoreSurroundingSpaces()
            .withRecordSeparator('\n')

        val parser = CSVParser(reader, csvFormat)
        val records = parser.records

        val expectedLine = mapOf(
            "RESOURCE_BUNDLE_SPECIALTY_ID" to "f1a0ecf5-eabf-4393-bbe0-fb66bcf69400",
            "Especialidade" to "Ortopedia",
            "Alice Code" to "code",
            "Tuss Primário" to "primaryTuss",
            "Descricao" to "description",
            "Ambiente" to "Não se aplica",
            "TALENTED T3" to "0.00",
            "TALENTED T2" to "0.00",
            "TALENTED T1" to "0.00",
            "TALENTED T0" to "100.15",
            "EXPERT T2" to "0.00",
            "EXPERT T1" to "0.00",
            "EXPERT T0" to "0.00",
            "SUPER EXPERT T1" to "0.00",
            "SUPER EXPERT T0" to "200.39",
            "ULTRA EXPERT T0" to "0.00",
            "Observações" to "Error 1"
        )
        val containsExpectedLine = records.any { record ->
            expectedLine.all { (key, value) -> record.get(key) == value }
        }

        Assertions.assertThat(containsExpectedLine).isTrue()

        parser.close()

        coVerifyOnce {
            resourceBundleSpecialtyPricingUpdateService.get(resourceBundleSpecialtyPricingUpdateId)
        }

        coVerifyOnce {
            fileVaultActionService.securedGenericLink(resourceBundleSpecialtyPricingUpdate.fileVaultId)
        }
    }

    @Test
    fun `uploadPriceChanges should add pricing update to the database`() = runBlocking {
        val effectiveDate = LocalDate.of(2023, 10, 8)
        val staffId = RangeUUID.generate()

        val requestBody = UploadPriceChangesRequest(
            fileName = csvFile.name,
            content = csvFile.readBytes(),
            pricesBeginAt = effectiveDate,
            fileType = FileType.fromExtension(csvFile.extension)!!,
            staffId = staffId
        )

        val pricingUpdate = TestModelFactory.buildResourceBundleSpecialtyPricingUpdate(
            fileVaultId = fileVaultId,
            fileName = fileName,
            createdByStaffId = staffId,
            failedRowsErrors = emptyList(),
        )

        coEvery {
            resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate()
        } returns NotFoundException("").failure()

        coEvery {
            fileVaultActionService.uploadGenericFile(
                match {
                    it.domain == genericVaultByteArray.domain &&
                            it.namespace == genericVaultByteArray.namespace &&
                            it.originalFileName == genericVaultByteArray.originalFileName &&
                            it.fileType == genericVaultByteArray.fileType
                }
            )
        } returns genericFileVault.success()

        coEvery {
            fileVaultActionService.uploadGenericFile(
                match {
                    it.domain == genericVaultByteArray.domain &&
                            it.namespace == genericVaultByteArray.namespace &&
                            it.originalFileName == genericVaultByteArray.originalFileName &&
                            it.fileType == genericVaultByteArray.fileType
                }
            )
        } returns genericFileVault.success()

        coEvery {
            resourceBundleSpecialtyPricingUpdateService.add(match {
                it.fileVaultId == fileVaultId &&
                        it.fileName == fileName &&
                        it.createdByStaffId == staffId &&
                        it.pricesBeginAt == effectiveDate
            })
        } returns pricingUpdate.success()

        val result = service.uploadPriceChanges(requestBody)

        assertThat(result).isSuccess()

        coVerifyOnce {
            resourceBundleSpecialtyPricingUpdateService.add(any())
        }

        coVerifyOnce {
            resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate()
        }

        coVerifyOnce {
            fileVaultActionService.uploadGenericFile(any())
        }
    }

    @Test
    fun `uploadPriceChanges should do nothing if there is a file already being processed`() = runBlocking {
        val effectiveDate = LocalDate.of(2023, 10, 8)
        val staffId = RangeUUID.generate()

        val requestBody = UploadPriceChangesRequest(
            fileName = csvFile.name,
            content = csvFile.readBytes(),
            pricesBeginAt = effectiveDate,
            fileType = FileType.fromExtension(csvFile.extension)!!,
            staffId = staffId
        )

        coEvery {
            resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate()
        } returns TestModelFactory.buildResourceBundleSpecialtyPricingUpdate()

        val result = service.uploadPriceChanges(requestBody)

        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyNone {
            resourceBundleSpecialtyPricingUpdateService.add(any())
        }

        coVerifyOnce {
            resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate()
        }

        coVerifyNone {
            fileVaultActionService.uploadGenericFile(any())
        }
    }

    @Test
    fun `getResourceBundleSpecialtyPricingUpdateHistory should return history with count`() = runBlocking {
        val filters = PricingUpdateHistoryFilters(
            startDate = LocalDate.now().minusDays(1),
            endDate = LocalDate.now(),
            status = listOf(ResourceBundleSpecialtyPricingUpdateStatus.PROCESSING)
        )
        val range = IntRange(0, 10)

        val staff = TestModelFactory.buildStaff()

        val resource = ResourceBundleSpecialtyPricingUpdate(
            id = RangeUUID.generate(),
            fileName = "history_update.csv",
            fileVaultId = RangeUUID.generate(),
            createdByStaffId = staff.id,
            processingAt = LocalDateTime.now(),
            completedAt = LocalDateTime.now(),
            rowsCount = 100,
            failedRowsCount = 0,
            failedRowsErrors = emptyList(),
            parsingError = null,
            pricesBeginAt = LocalDate.now(),
        )

        val vaultResponse = VaultResponse(
            id = resource.fileVaultId,
            fileName = "failed_lines.csv",
            type = "text/csv",
            vaultUrl = "example.com",
            fileSize = 1234,
            url = "file://${System.getProperty("user.dir")}/testResources/failed_lines.csv"
        )

        coEvery {
            resourceBundleSpecialtyPricingUpdateService.getResourceBundleSpecialtyPricingUpdateHistory(
                filters,
                range
            )
        } returns listOf(resource)

        coEvery {
            resourceBundleSpecialtyPricingUpdateService.countResourceBundleSpecialtyPricingUpdateHistory(filters)
        } returns 10

        coEvery {
            staffService.findByList(listOf(staff.id))
        } returns listOf(staff)

        coEvery {
            fileVaultActionService.securedGenericLinks(listOf(resource.fileVaultId))
        } returns listOf(vaultResponse)

        val result = service.getPricingUpdateHistory(filters, range)

        val expected = ResourceBundleSpecialtyPricingUpdateHistoryItem(
            id = resource.id,
            fileName = resource.fileName,
            fileUrl = vaultResponse.url,
            createdByStaff = staff,
            processingAt = resource.processingAt,
            completedAt = resource.completedAt,
            rowsCount = resource.rowsCount,
            failedRowsCount = resource.failedRowsCount,
            parsingError = resource.parsingError,
            pricesBeginAt = resource.pricesBeginAt,
            createdAt = resource.createdAt
        )

        assertThat(result).isSuccessWithData(
            ResourceBundleSpecialtyPricingUpdateHistoryWithCount(
                count = 10,
                items = listOf(expected)
            )
        )

        coVerifyOnce {
            resourceBundleSpecialtyPricingUpdateService.getResourceBundleSpecialtyPricingUpdateHistory(any(), any())
            resourceBundleSpecialtyPricingUpdateService.countResourceBundleSpecialtyPricingUpdateHistory(any())
            staffService.findByList(any())
            fileVaultActionService.securedGenericLinks(any())
        }
    }

}
