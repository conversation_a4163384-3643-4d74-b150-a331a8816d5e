package br.com.alice.exec.indicator.service.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.returns
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.data.layer.models.ResourceBundleSpecialty
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPrice
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricing
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricingUpdate
import br.com.alice.data.layer.models.TierType
import br.com.alice.filevault.client.FileVaultActionService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class ProcessPricingCSVServiceTest {

    private val resourceBundleSpecialtyPricingUpdateService: ResourceBundleSpecialtyPricingUpdateService = mockk()
    private val resourceBundleSpecialtyService: ResourceBundleSpecialtyService = mockk()
    private val resourceBundleSpecialtyPricingService: ResourceBundleSpecialtyPricingService = mockk()
    private val fileVaultActionService: FileVaultActionService = mockk()

    private val service = ProcessPricingCSVService(
        resourceBundleSpecialtyPricingUpdateService,
        resourceBundleSpecialtyService,
        resourceBundleSpecialtyPricingService,
        fileVaultActionService
    )

    private val resourceBundleSpecialtyId = RangeUUID.generate()
    private val fileVaultId = RangeUUID.generate()
    private val staffId = RangeUUID.generate()
    private val effectiveDate = LocalDate.now()

    private val resourceBundleSpecialtyPricingUpdate = ResourceBundleSpecialtyPricingUpdate(
        fileName = "pricing_update.csv",
        fileVaultId = fileVaultId,
        createdByStaffId = staffId,
        processingAt = null,
        completedAt = null,
        rowsCount = 0,
        failedRowsCount = 0,
        failedRowsErrors = emptyList(),
        parsingError = null,
        pricesBeginAt = effectiveDate
    )

    private val resourceBundleSpecialty = ResourceBundleSpecialty(
        id = resourceBundleSpecialtyId,
        healthSpecialistResourceBundleId = UUID.randomUUID(),
        medicalSpecialtyId = UUID.randomUUID(),
        status = Status.ACTIVE,
        pricingStatus = PricingStatus.PRICED
    )

    private val resourceBundleSpecialtyPricing = ResourceBundleSpecialtyPricing(
        resourceBundleSpecialtyId = resourceBundleSpecialtyId,
        beginAt = effectiveDate,
        endAt = null,
        prices = emptyList()
    )

    private val csvContent = """
        RESOURCE_BUNDLE_SPECIALTY_ID,Especialidade,Alice Code,Tuss Primário,Descricao,Ambiente,TALENTED T3,TALENTED T2,TALENTED T1,TALENTED T0,EXPERT T2,EXPERT T1,EXPERT T0,SUPER EXPERT T1,SUPER EXPERT T0,ULTRA EXPERT T0,Observacoes
        $resourceBundleSpecialtyId,Agendamentos,80856432,10106170,Pacote consulta ocupacional bilateral - editado,Não se aplica,30.00,40.00,50.00,60.00,70.00,80.00,90.00,100.00,120.00,150.00,
    """.trimIndent().toByteArray()

    @BeforeEach
    fun setup() {
        coEvery { fileVaultActionService.genericFileContentById(any()) } returns csvContent
        coEvery { resourceBundleSpecialtyService.get(any()) } returns resourceBundleSpecialty
        coEvery { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyIdAndEffectiveDate(any(), any()) } returns emptyList()
        coEvery { resourceBundleSpecialtyPricingService.update(any()) } returns resourceBundleSpecialtyPricing
        coEvery { resourceBundleSpecialtyPricingService.add(any()) } returns resourceBundleSpecialtyPricing
        coEvery { resourceBundleSpecialtyService.update(any()) } returns resourceBundleSpecialty
    }

    @AfterEach
    fun tearDown() = clearAllMocks()


    @Test
    fun `process should update pricing update with processing time and process file content`() = runBlocking {
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate

        val result = service.process(resourceBundleSpecialtyPricingUpdate.id)

        ResultAssert.assertThat(result).isSuccess()

        coVerify {
            resourceBundleSpecialtyPricingUpdateService.get(resourceBundleSpecialtyPricingUpdate.id)
            resourceBundleSpecialtyPricingUpdateService.update(match { it.processingAt != null })
            fileVaultActionService.genericFileContentById(fileVaultId)
            resourceBundleSpecialtyService.get(resourceBundleSpecialtyId)
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyIdAndEffectiveDate(
                listOf(resourceBundleSpecialtyId),
                resourceBundleSpecialtyPricingUpdate.pricesBeginAt,
            )
            resourceBundleSpecialtyPricingService.add(match { pricing ->
                pricing.resourceBundleSpecialtyId == resourceBundleSpecialtyId &&
                        pricing.beginAt == effectiveDate &&
                        pricing.endAt == null &&
                        pricing.prices.size == 10 &&
                        pricing.prices.any {
                            it.tier == SpecialistTier.TALENTED
                                    && it.productTier == TierType.TIER_3
                                    && it.price == BigDecimal("30.00")
                        } &&
                        pricing.prices.any {
                            it.tier == SpecialistTier.TALENTED
                                    && it.productTier == TierType.TIER_2
                                    && it.price == BigDecimal("40.00")
                        } &&
                        pricing.prices.any {
                            it.tier == SpecialistTier.TALENTED
                                    && it.productTier == TierType.TIER_1
                                    && it.price == BigDecimal("50.00")
                        } &&
                        pricing.prices.any {
                            it.tier == SpecialistTier.TALENTED
                                    && it.productTier == TierType.TIER_0
                                    && it.price == BigDecimal("60.00")
                        } &&
                        pricing.prices.any {
                            it.tier == SpecialistTier.EXPERT
                                    && it.productTier == TierType.TIER_2
                                    && it.price == BigDecimal("70.00")
                        } &&
                        pricing.prices.any {
                            it.tier == SpecialistTier.EXPERT
                                    && it.productTier == TierType.TIER_1
                                    && it.price == BigDecimal("80.00")
                        } &&
                        pricing.prices.any {
                            it.tier == SpecialistTier.EXPERT
                                    && it.productTier == TierType.TIER_0
                                    && it.price == BigDecimal("90.00")
                        } &&
                        pricing.prices.any {
                            it.tier == SpecialistTier.SUPER_EXPERT
                                    && it.productTier == TierType.TIER_1
                                    && it.price == BigDecimal("100.00")
                        } &&
                        pricing.prices.any {
                            it.tier == SpecialistTier.SUPER_EXPERT
                                    && it.productTier == TierType.TIER_0
                                    && it.price == BigDecimal("120.00")
                        } &&
                        pricing.prices.any {
                            it.tier == SpecialistTier.ULTRA_EXPERT
                                    && it.productTier == TierType.TIER_0
                                    && it.price == BigDecimal("150.00")
                        }
            })
            resourceBundleSpecialtyPricingUpdateService.update(match {
                it.completedAt != null && it.rowsCount == 1 && it.failedRowsCount == 0
            })
            resourceBundleSpecialtyService.update(
                match { it.id == resourceBundleSpecialtyId && it.pricingStatus == PricingStatus.PRICED }
            )
        }
    }

    @Test
    fun `process should create new price if the price is there is a previous price that ends before added effective date and there is a price that starts after effective date`() = runBlocking {
        val existingPricing = ResourceBundleSpecialtyPricing(
            id = RangeUUID.generate(),
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate.minusDays(20),
            endAt = effectiveDate.minusDays(1),
            prices = listOf(
                ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_0, BigDecimal("50.00"))
            )
        )

        val futurePrice = ResourceBundleSpecialtyPricing(
            id = RangeUUID.generate(),
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate,
            endAt = null,
            prices = listOf(
                ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_0, BigDecimal("60.00"))
            )
        )

        val resourceBundleSpecialtyPricingUpdate = resourceBundleSpecialtyPricingUpdate.copy(
            pricesBeginAt = effectiveDate.minusDays(10)
        )

        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate

        coEvery { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyIdAndEffectiveDate(any(), any()) } returns Result.success(
            listOf(existingPricing)
        )

        coEvery {
            resourceBundleSpecialtyPricingService.getMostRecentByResourceBundleSpecialtyId(resourceBundleSpecialtyId)
        } returns futurePrice.success()

        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerify {
            resourceBundleSpecialtyPricingService.update(match { pricing ->
                pricing.resourceBundleSpecialtyId == resourceBundleSpecialtyId &&
                        pricing.endAt == effectiveDate.minusDays(11)
            })
            resourceBundleSpecialtyPricingService.update(match {
                it.resourceBundleSpecialtyId == futurePrice.resourceBundleSpecialtyId &&
                        it.beginAt == effectiveDate.minusDays(10) &&
                        it.prices.first {
                            it.tier == SpecialistTier.TALENTED && it.productTier == TierType.TIER_3
                        }.price == BigDecimal("30.00")
            })
        }
    }

    @Test
    fun `process should handle resource not found error`() = runBlocking {
        coEvery { resourceBundleSpecialtyService.get(any()) } returns NotFoundException("Resource not found")
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate

        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerify {
            resourceBundleSpecialtyPricingUpdateService.update(match {
                it.completedAt != null && it.failedRowsCount == 1 && it.failedRowsErrors.isNotEmpty()
            })
        }
    }

    @Test
    fun `process should handle parsing error`() = runBlocking {
        val invalidCsvContent = "Invalid CSV Content".toByteArray()

        coEvery { fileVaultActionService.genericFileContentById(any()) } returns Result.success(invalidCsvContent)
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate

        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerify {
            resourceBundleSpecialtyPricingUpdateService.update(match {
                it.completedAt != null && it.parsingError != null
            })
        }
    }

    @Test
    fun `process should handle missing columns`() = runBlocking {
        val csvWithMissingColumns = """
            RESOURCE_BUNDLE_SPECIALTY_ID,Especialidade,Alice Code,Tuss Primário,Descricao,Ambiente,TALENTED T3,TALENTED T2,TALENTED T1,TALENTED T0,EXPERT T2,EXPERT T1,SUPER EXPERT T1,SUPER EXPERT T0,ULTRA EXPERT T0,Observacoes
            $resourceBundleSpecialtyId,Agendamentos,80856432,10106170,Pacote consulta ocupacional bilateral - editado,Não se aplica,-30.00,40.00,50.00,60.00,70.00,80.00,100.00,120.00,150.00,
        """.trimIndent().toByteArray()

        coEvery { fileVaultActionService.genericFileContentById(any()) } returns csvWithMissingColumns
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate

        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerify {
            resourceBundleSpecialtyPricingUpdateService.update(match {
                it.completedAt != null
                        && it.failedRowsCount == 1
                        && it.failedRowsErrors.any { error -> error.error.contains("Número de colunas inválido") }
            })
        }
    }

    @Test
    fun `process should handle invalid price values`() = runBlocking {
        val csvWithInvalidValues = """
            RESOURCE_BUNDLE_SPECIALTY_ID,Especialidade,Alice Code,Tuss Primário,Descricao,Ambiente,TALENTED T3,TALENTED T2,TALENTED T1,TALENTED T0,EXPERT T2,EXPERT T1,EXPERT T0,SUPER EXPERT T1,SUPER EXPERT T0,ULTRA EXPERT T0,Observacoes
            $resourceBundleSpecialtyId,Agendamentos,80856432,10106170,Pacote consulta ocupacional bilateral - editado,Não se aplica,30.00,40.00,50.00,60.00,70.00,80.00,INVALID,100.00,120.00,150.00,
        """.trimIndent().toByteArray()

        coEvery { fileVaultActionService.genericFileContentById(any()) } returns Result.success(csvWithInvalidValues)
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate

        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerify {
            resourceBundleSpecialtyPricingUpdateService.update(match {
                it.completedAt != null
                        && it.failedRowsCount == 1
                        && it.failedRowsErrors.any { error -> error.error.contains("EXPERT T0") }
            })
        }
    }

    @Test
    fun `process should handle duplicated line`() = runBlocking {
        val csvWithInvalidValues = """
            RESOURCE_BUNDLE_SPECIALTY_ID,Especialidade,Alice Code,Tuss Primário,Descricao,Ambiente,TALENTED T3,TALENTED T2,TALENTED T1,TALENTED T0,EXPERT T2,EXPERT T1,EXPERT T0,SUPER EXPERT T1,SUPER EXPERT T0,ULTRA EXPERT T0,Observacoes
            $resourceBundleSpecialtyId,Agendamentos,80856432,10106170,Pacote consulta ocupacional bilateral - editado,Não se aplica,60.00,40.00,50.00,60.00,70.00,80.00,1,100.00,120.00,150.00,
            $resourceBundleSpecialtyId,Agendamentos,80856432,10106170,Pacote consulta ocupacional bilateral - editado,Não se aplica,40.00,40.00,50.00,60.00,70.00,80.00,3,100.00,120.00,150.00,
        """.trimIndent().toByteArray()

        coEvery { fileVaultActionService.genericFileContentById(any()) } returns Result.success(csvWithInvalidValues)
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate

        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerify {
            resourceBundleSpecialtyPricingUpdateService.update(match {
                it.completedAt != null
                        && it.failedRowsCount == 1
                        && it.failedRowsErrors.any { error -> error.error.contains("duplicado") }
            })

            resourceBundleSpecialtyPricingService.add(match { pricing ->
                pricing.resourceBundleSpecialtyId == resourceBundleSpecialtyId &&
                        pricing.beginAt == effectiveDate &&
                        pricing.endAt == null &&
                        pricing.prices.size == 10 &&
                        pricing.prices.any {
                            it.tier == SpecialistTier.TALENTED
                                    && it.productTier == TierType.TIER_3
                                    && it.price == BigDecimal("60.00")
                        }
            })
        }
    }

    @Test
    fun `process should expire existing pricing and create new one`() = runBlocking {
        val existingPricing = ResourceBundleSpecialtyPricing(
            id = RangeUUID.generate(),
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = LocalDate.now().minusDays(10),
            endAt = null,
            prices = listOf(
                ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_0, BigDecimal("50.00"))
            )
        )

        coEvery { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyIdAndEffectiveDate(any(), any()) } returns Result.success(
            listOf(existingPricing)
        )
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate

        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerify {
            resourceBundleSpecialtyPricingService.update(match { pricing ->
                pricing.resourceBundleSpecialtyId == resourceBundleSpecialtyId &&
                        pricing.endAt == effectiveDate.minusDays(1)
            })
            resourceBundleSpecialtyPricingService.add(match { pricing ->
                pricing.resourceBundleSpecialtyId == resourceBundleSpecialtyId &&
                        pricing.beginAt == effectiveDate &&
                        pricing.endAt == null &&
                        pricing.prices.size == 10 &&
                        pricing.prices.any {
                            it.tier == SpecialistTier.TALENTED
                                    && it.productTier == TierType.TIER_3
                                    && it.price == BigDecimal("30.00")
                        } &&
                        pricing.prices.any {
                            it.tier == SpecialistTier.EXPERT
                                    && it.productTier == TierType.TIER_1
                                    && it.price == BigDecimal("80.00")
                        } &&
                        pricing.prices.any {
                            it.tier == SpecialistTier.ULTRA_EXPERT
                                    && it.productTier == TierType.TIER_0
                                    && it.price == BigDecimal("150.00")
                        }
            })
            resourceBundleSpecialtyService.update(match {
                it.id == resourceBundleSpecialtyId && it.pricingStatus == PricingStatus.PRICED
            })
        }
    }

    @Test
    fun `process should update existing pricing if effective date is the same as the current pricing`() = runBlocking {
        val existingPricing = ResourceBundleSpecialtyPricing(
            id = RangeUUID.generate(),
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = effectiveDate,
            endAt = null,
            prices = listOf(
                ResourceBundleSpecialtyPrice(SpecialistTier.TALENTED, TierType.TIER_0, BigDecimal("50.00"))
            )
        )

        coEvery { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyIdAndEffectiveDate(any(), any()) } returns Result.success(
            listOf(existingPricing)
        )
        coEvery { resourceBundleSpecialtyPricingUpdateService.get(any()) } returns resourceBundleSpecialtyPricingUpdate
        coEvery { resourceBundleSpecialtyPricingUpdateService.update(any()) } returns resourceBundleSpecialtyPricingUpdate

        service.process(resourceBundleSpecialtyPricingUpdate.id)

        coVerify {
            resourceBundleSpecialtyPricingService.update(match { pricing ->
                pricing.resourceBundleSpecialtyId == resourceBundleSpecialtyId &&
                        pricing.beginAt == effectiveDate &&
                        pricing.endAt == null &&
                        pricing.prices.size == 10 &&
                        pricing.id == existingPricing.id
            })

            resourceBundleSpecialtyService.update(match {
                it.id == resourceBundleSpecialtyId && it.pricingStatus == PricingStatus.PRICED
            })
        }

        coVerifyNone {
            resourceBundleSpecialtyPricingService.add(any())
        }
    }
}
