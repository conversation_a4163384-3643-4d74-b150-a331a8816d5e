import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricing
import br.com.alice.data.layer.services.ResourceBundleSpecialtyPricingModelDataService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.service.internal.ResourceBundleSpecialtyPricingService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test

class ResourceBundleSpecialtyPricingServiceTest {

    private val dataService: ResourceBundleSpecialtyPricingModelDataService = mockk()
    private val service = ResourceBundleSpecialtyPricingService(dataService)

    @Test
    fun `getCurrentlyActiveByResourceBundleSpecialtyId should return only active pricing`() = runBlocking {
        val resourceBundleSpecialtyId = UUID.randomUUID()
        val resourceBundleSpecialtyIds = listOf(resourceBundleSpecialtyId)

        val pricing1 = ResourceBundleSpecialtyPricing(
            id = UUID.randomUUID(),
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = LocalDate.now().minusDays(10),
            endAt = null // Active pricing
        )
        val pricing2 = ResourceBundleSpecialtyPricing(
            id = UUID.randomUUID(),
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = LocalDate.now().minusDays(20),
            endAt = LocalDate.now().minusDays(5) // Inactive pricing
        )

        coEvery { dataService.find(any()) } returns listOf(pricing1, pricing2).map { it.toModel() }.success()

        val result = service.getCurrentlyActiveByResourceBundleSpecialtyId(resourceBundleSpecialtyIds)

        assertEquals(Result.success(listOf(pricing1)), result)
        coVerify(exactly = 1) { dataService.find(any()) }
    }

    @Test
    fun `getCurrentlyActiveAndFuturePricesByResourceBundleSpecialtyId should return active and future pricing`() = runBlocking {
        val resourceBundleSpecialtyId = UUID.randomUUID()
        val resourceBundleSpecialtyIds = listOf(resourceBundleSpecialtyId)

        val pricing1 = ResourceBundleSpecialtyPricing(
            id = UUID.randomUUID(),
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = LocalDate.now().minusDays(10),
            endAt = null // Active pricing
        )
        val pricing2 = ResourceBundleSpecialtyPricing(
            id = UUID.randomUUID(),
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = LocalDate.now().plusDays(5), // Future pricing
            endAt = null
        )

        coEvery { dataService.find(any()) } returns listOf(pricing1, pricing2).map { it.toModel() }.success()

        val result = service.getCurrentlyActiveAndFuturePricesByResourceBundleSpecialtyId(resourceBundleSpecialtyIds)

        assertEquals(Result.success(listOf(pricing1, pricing2)), result)
        coVerify(exactly = 1) { dataService.find(any()) }
    }

    @Test
    fun `getActiveByResourceBundleSpecialtyIdAndEffectiveDate should return only active pricing`() = runBlocking {
        val resourceBundleSpecialtyId = UUID.randomUUID()
        val resourceBundleSpecialtyIds = listOf(resourceBundleSpecialtyId)

        val pricing1 = ResourceBundleSpecialtyPricing(
            id = UUID.randomUUID(),
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = LocalDate.now().minusDays(10),
            endAt = null // Active pricing
        )
        val pricing2 = ResourceBundleSpecialtyPricing(
            id = UUID.randomUUID(),
            resourceBundleSpecialtyId = resourceBundleSpecialtyId,
            beginAt = LocalDate.now().minusDays(20),
            endAt = LocalDate.now().minusDays(11) // Inactive pricing
        )

        coEvery { dataService.find(any()) } returns listOf(pricing1, pricing2).map { it.toModel() }.success()

        val result = service.getActiveByResourceBundleSpecialtyIdAndEffectiveDate(resourceBundleSpecialtyIds, LocalDate.now().minusDays(10))

        assertEquals(Result.success(listOf(pricing1)), result)
        coVerify(exactly = 1) { dataService.find(any()) }
    }

    @Test
    fun `#updateList should update the list of specialties`() = runBlocking {
        val specialties = listOf(
            ResourceBundleSpecialtyPricing(
                id = UUID.randomUUID(),
                resourceBundleSpecialtyId = UUID.randomUUID(),
                beginAt = LocalDate.now(),
                endAt = null
            )
        )

        coEvery { dataService.updateList(any()) } returns Result.success(specialties.map { it.toModel() })

        val result = service.updateList(specialties)

        assertEquals(Result.success(specialties), result)
        coVerify(exactly = 1) { dataService.updateList(any()) }
    }

    @Test
    fun `#add should add the specialty`() = runBlocking {
        val specialty = ResourceBundleSpecialtyPricing(
            id = UUID.randomUUID(),
            resourceBundleSpecialtyId = UUID.randomUUID(),
            beginAt = LocalDate.now(),
            endAt = null
        )

        coEvery { dataService.add(any()) } returns Result.success(specialty.toModel())

        val result = service.add(specialty)

        assertEquals(Result.success(specialty), result)
        coVerify(exactly = 1) { dataService.add(any()) }
    }

    @Test
    fun `#update should update the specialty`() = runBlocking {
        val specialty = ResourceBundleSpecialtyPricing(
            id = UUID.randomUUID(),
            resourceBundleSpecialtyId = UUID.randomUUID(),
            beginAt = LocalDate.now(),
            endAt = null
        )

        coEvery { dataService.update(any()) } returns Result.success(specialty.toModel())

        val result = service.update(specialty)

        assertEquals(Result.success(specialty), result)
        coVerify(exactly = 1) { dataService.update(any()) }
    }

    @Test
    fun `#deleteList should delete the list of specialties`() = runBlocking {
        val specialties = listOf(
            ResourceBundleSpecialtyPricing(
                id = UUID.randomUUID(),
                resourceBundleSpecialtyId = UUID.randomUUID(),
                beginAt = LocalDate.now(),
                endAt = null
            )
        )

        coEvery { dataService.delete(any()) } returns true.success()

        val result = service.deleteList(specialties)

        assertEquals(Result.success(listOf(true)), result)
        coVerify(exactly = 1) { dataService.delete(any()) }
    }
}
