package br.com.alice.exec.indicator.service.internal


import br.com.alice.common.core.Status
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.data.layer.models.AppointmentRecommendationLevel
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.data.layer.models.ResourceBundleSpecialty
import br.com.alice.data.layer.services.ResourceBundleSpecialtyModelDataService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.util.UUID

class ResourceBundleSpecialtyService(
    private val resourceBundleSpecialtyModelDataService: ResourceBundleSpecialtyModelDataService,
) {

    suspend fun get(id: UUID): Result<ResourceBundleSpecialty, Throwable> =
        resourceBundleSpecialtyModelDataService.get(id).map { it.toTransport() }

    suspend fun findByIds(
        ids: List<UUID>,
        filterActive: Boolean = true
    ): Result<List<ResourceBundleSpecialty>, Throwable> =
        resourceBundleSpecialtyModelDataService
            .find {
                where {
                    val activePredicate = if (filterActive) status.eq(Status.ACTIVE) else null

                    id.inList(ids).and(activePredicate)
                }
            }.map { it.toTransport() }

    suspend fun findAllActive(): Result<List<ResourceBundleSpecialty>, Throwable> =
        resourceBundleSpecialtyModelDataService.find {
            where { status.eq(Status.ACTIVE) }.orderBy { createdAt }.sortOrder { desc }
        }.map { it.toTransport() }

    suspend fun getByResourceBundleId(id: UUID): Result<List<ResourceBundleSpecialty>, Throwable> =
        resourceBundleSpecialtyModelDataService.find {
            where {
                healthSpecialistResourceBundleId.eq(id)
            }
        }.map {
            it.toTransport()
        }

    suspend fun update(
        resourceBundleSpecialty: ResourceBundleSpecialty,
    ) = resourceBundleSpecialtyModelDataService.update(resourceBundleSpecialty.toModel())
        .map { it.toTransport() }

    suspend fun updateList(
        resourceBundleSpecialties: List<ResourceBundleSpecialty>,
    ) = resourceBundleSpecialtyModelDataService.updateList(resourceBundleSpecialties.map { it.toModel() })
        .map { it.toTransport() }

    suspend fun addList(
        resourceBundleSpecialties: List<ResourceBundleSpecialty>,
    ) = resourceBundleSpecialtyModelDataService.addList(resourceBundleSpecialties.map { it.toModel() })
        .map { it.toTransport() }

    suspend fun getByResourceBundleIds(
        ids: List<UUID>,
        filterActive: Boolean = true
    ): Result<List<ResourceBundleSpecialty>, Throwable> =
        resourceBundleSpecialtyModelDataService.find {
            where {
                healthSpecialistResourceBundleId.inList(ids).and(
                    if (filterActive) {
                        status.eq(Status.ACTIVE)
                    } else {
                        null
                    }
                )
            }
        }.map {
            it.toTransport()
        }

    suspend fun getByPricingStatus(
        pricingStatus: PricingStatus,
        filterActive: Boolean = true
    ): Result<List<ResourceBundleSpecialty>, Throwable> =
        resourceBundleSpecialtyModelDataService.find {
            where {
                this.pricingStatus.eq(pricingStatus).and(
                    if (filterActive) {
                        status.eq(Status.ACTIVE)
                    } else {
                        null
                    }
                )
            }
        }.map {
            it.toTransport()
        }

    suspend fun getByFilters(
        medicalSpecialtyIds: List<UUID>? = null,
        pricingStatus: PricingStatus? = null,
        resourceBundleIds: List<UUID>? = null,
        status: Status = Status.ACTIVE,
    ): Result<List<ResourceBundleSpecialty>, Throwable> =
        resourceBundleSpecialtyModelDataService.find {
            where {
                this.status.eq(status).and(
                    if (medicalSpecialtyIds != null) {
                        medicalSpecialtyId.inList(medicalSpecialtyIds)
                    } else {
                        null
                    }
                ).and(
                    if (pricingStatus != null) {
                        this.pricingStatus.eq(pricingStatus)
                    } else {
                        null
                    }
                ).and(
                    if (resourceBundleIds != null) {
                        healthSpecialistResourceBundleId.inList(resourceBundleIds)
                    } else {
                        null
                    }
                )
            }
        }.map {
            it.toTransport()
        }

    @OptIn(WithFilterPredicateUsage::class)
    suspend fun findBySpecialtyId(
        specialtyId: UUID,
        appointmentLevel: List<AppointmentRecommendationLevel> = emptyList()
    ): Result<List<ResourceBundleSpecialty>, Throwable> =
        resourceBundleSpecialtyModelDataService.find {
            where {
                medicalSpecialtyId.eq(specialtyId)
                    .withFilter(appointmentLevel) { this.appointmentRecommendationLevel.inList(appointmentLevel) }!!
            }
        }.map {
            it.toTransport()
        }

}

