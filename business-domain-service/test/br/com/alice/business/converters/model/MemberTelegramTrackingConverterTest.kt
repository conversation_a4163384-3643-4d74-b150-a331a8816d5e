package br.com.alice.business.converters.model

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.MemberTelegramInfo
import br.com.alice.data.layer.models.MemberTelegramPostalType
import br.com.alice.data.layer.models.MemberTelegramTracking
import br.com.alice.data.layer.models.MemberTelegramTrackingEvents
import br.com.alice.data.layer.models.MemberTelegramTrackingModel
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class MemberTelegramTrackingConverterTest {
    private val memberTelegramTracking = MemberTelegramTracking(
        id = RangeUUID.generate(),
        version = 4,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        externalId = "externalId",
        name = "name",
        address = "address",
        streetNumber = "streetNumber",
        addressComplement = "addressComplement",
        zip = "zip",
        city = "city",
        state = "state",
        country = "country",
        nameSender = "nameSender",
        addressSender = "addressSender",
        zipSender = "zipSender",
        citySender = "citySender",
        stateSender = "stateSender",
        streetNumberSender = "streetNumberSender",
        addressComplementSender = "addressComplementSender",
        countrySender = "countrySender",
        shipType = "shipType",
        tracking = "tracking",
        status = "status",
        statusDelivery = "statusDelivery",
        trackingRecordsLastUpdate = LocalDateTime.now(),
        dateSubmit = LocalDateTime.now(),
        dateLastChange = LocalDateTime.now(),
        tag = "tag",
        trackingEvents = listOf(MemberTelegramTrackingEvents(
            code = "code",
            type = 1,
            createdDateTime = LocalDateTime.now(),
            description = "description"
        )),
        postalType = MemberTelegramPostalType(
            acronym = "acronym",
            description = "description",
            category = "category"
        ),
        telegramInfo = MemberTelegramInfo(
            id = RangeUUID.generate().toString(),
            jobVisualId = RangeUUID.generate().toString(),
            msg = "msg",
            senderCopy = true,
            deliveryConfirmation = true,
        ),
        deliveryEstimate = LocalDateTime.now(),
        searchTokens = "value"
    )

    private val memberTelegramTrackingModel = MemberTelegramTrackingModel(
        id = memberTelegramTracking.id,
        version = memberTelegramTracking.version,
        createdAt = memberTelegramTracking.createdAt,
        updatedAt = memberTelegramTracking.updatedAt,
        externalId = memberTelegramTracking.externalId,
        name = memberTelegramTracking.name,
        address = memberTelegramTracking.address,
        streetNumber = memberTelegramTracking.streetNumber,
        addressComplement = memberTelegramTracking.addressComplement,
        zip = memberTelegramTracking.zip,
        city = memberTelegramTracking.city,
        state = memberTelegramTracking.state,
        country = memberTelegramTracking.country,
        nameSender = memberTelegramTracking.nameSender,
        addressSender = memberTelegramTracking.addressSender,
        zipSender = memberTelegramTracking.zipSender,
        citySender = memberTelegramTracking.citySender,
        stateSender = memberTelegramTracking.stateSender,
        streetNumberSender = memberTelegramTracking.streetNumberSender,
        addressComplementSender = memberTelegramTracking.addressComplementSender,
        countrySender = memberTelegramTracking.countrySender,
        shipType = memberTelegramTracking.shipType,
        tracking = memberTelegramTracking.tracking,
        status = memberTelegramTracking.status,
        statusDelivery = memberTelegramTracking.statusDelivery,
        trackingRecordsLastUpdate = memberTelegramTracking.trackingRecordsLastUpdate,
        dateSubmit = memberTelegramTracking.dateSubmit,
        dateLastChange = memberTelegramTracking.dateLastChange,
        tag = memberTelegramTracking.tag,
        trackingEvents = memberTelegramTracking.trackingEvents.map { it.toModel() },
        postalType = memberTelegramTracking.postalType?.toModel(),
        telegramInfo = memberTelegramTracking.telegramInfo?.toModel(),
        deliveryEstimate = memberTelegramTracking.deliveryEstimate,
        searchTokens = memberTelegramTracking.searchTokens
    )

    @Test
    fun testToTransport() {
        assertThat(memberTelegramTrackingModel.toTransport()).isEqualTo(memberTelegramTracking)
    }

    @Test
    fun testToModel() {
        assertThat(memberTelegramTracking.toModel()).isEqualTo(memberTelegramTrackingModel)
    }
}
