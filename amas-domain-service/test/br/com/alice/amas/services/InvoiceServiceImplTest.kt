package br.com.alice.amas.services

import br.com.alice.amas.converters.toModel
import br.com.alice.amas.services.InvoiceServiceImpl.Companion.SEARCH_INVOICE_MONTHS_TO_SUBTRACT
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDate
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InvoiceExpenseType
import br.com.alice.data.layer.models.InvoiceType
import br.com.alice.data.layer.models.TissInvoiceStatus
import br.com.alice.data.layer.services.InvoiceModelDataService
import br.com.alice.exec.indicator.client.ExecIndicatorAuthorizerService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test


@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class InvoiceServiceImplTest {

    private val data: InvoiceModelDataService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val staffService: StaffService = mockk()
    private val authorizerService: ExecIndicatorAuthorizerService = mockk()

    private val service = InvoiceServiceImpl(data, providerUnitService, staffService, authorizerService)

    @AfterTest
    fun clear() = clearAllMocks()

    @Test
    fun `#add should add with the correct search tokens - without staff and provider unit`() = runBlocking {
        val providerUnit = TestModelFactory.buildProviderUnit()
        val authorizer = TestModelFactory.buildExecIndicatorAuthorizer(providerUnitId = providerUnit.id)
        val invoice = TestModelFactory.buildTissInvoice(
            staffId = null,
            providerUnitId = null,
            execIndicatorAuthorizerId = authorizer.id
        )

        coEvery { authorizerService.get(authorizer.id) } returns authorizer.success()
        coEvery { providerUnitService.get(authorizer.providerUnitId) } returns providerUnit.success()

        val searchTokens = "${invoice.code} ${providerUnit.name} ${providerUnit.cnpj}"
        val expected = invoice.copy(searchTokens = searchTokens.unaccent())

        coEvery { data.add(any()) } returns expected.toModel().success()

        val result = service.add(expected)

        ResultAssert.assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { authorizerService.get(any()) }
        coVerifyOnce { providerUnitService.get(any()) }
        coVerifyNone { staffService.get(any()) }
    }

    @Test
    fun `#add should add with the correct search tokens - without authorizer`() = runBlocking {
        val providerUnit = TestModelFactory.buildProviderUnit()
        val staff = TestModelFactory.buildStaff()
        val invoice = TestModelFactory.buildTissInvoice(
            staffId = staff.id,
            providerUnitId = providerUnit.id,
            execIndicatorAuthorizerId = null
        )

        coEvery { staffService.get(staff.id) } returns staff.success()
        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()

        val searchTokens = "${invoice.code} ${providerUnit.name} ${providerUnit.cnpj}"
        val expected = invoice.copy(searchTokens = searchTokens.unaccent())

        coEvery { data.add(any()) } returns expected.toModel().success()

        val result = service.add(expected)

        ResultAssert.assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { providerUnitService.get(any()) }
        coVerifyNone { authorizerService.get(any()) }
    }

    @Test
    fun `#update should update invoice successfully`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()

        val searchTokens = invoice.code
        val expected = invoice.copy(searchTokens = searchTokens.unaccent())

        coEvery { data.update(any()) } returns expected.toModel().success()

        val result = service.update(expected)

        ResultAssert.assertThat(result).isSuccessWithData(expected)
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findByExecAuthorizerIdForStaffView should create query successfully - without invoice type`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()
        val authorizerId = RangeUUID.generate()

        coEvery {
            data.find(queryEq {
                where {
                    scope(
                        scope(execIndicatorAuthorizer.diff(authorizerId).or(execIndicatorAuthorizer.isNull()))
                            .and(
                                status.notInList(listOf(TissInvoiceStatus.DRAFT))
                                    .and(type.eq(InvoiceType.HEALTH_INSTITUTION))
                                    .or(type.eq(InvoiceType.HEALTH_SPECIALIST))
                            )
                            .or(execIndicatorAuthorizer.eq(authorizerId))
                    )
                }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
            })
        } returns listOf(invoice.toModel()).success()

        service.findByExecAuthorizerIdForStaffView(authorizerId)

        coVerifyOnce { data.find(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findByExecAuthorizerIdForStaffView should create query successfully - with invoice type eq HEALTH_INSTITUTION`() =
        runBlocking {
            val invoice = TestModelFactory.buildTissInvoice()
            val authorizerId = RangeUUID.generate()
            val invoiceType = InvoiceType.HEALTH_INSTITUTION

            coEvery {
                data.find(queryEq {
                    where {
                        scope(
                            scope(execIndicatorAuthorizer.diff(authorizerId).or(execIndicatorAuthorizer.isNull()))
                                .and(status.notInList(listOf(TissInvoiceStatus.DRAFT)).and(type.eq(invoiceType)))
                                .or(execIndicatorAuthorizer.eq(authorizerId)).and(type.eq(invoiceType))
                        )
                    }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
                })
            } returns listOf(invoice.toModel()).success()

            service.findByExecAuthorizerIdForStaffView(authorizerId, invoiceType)

            coVerifyOnce { data.find(any()) }
        }


    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findByExecAuthorizerIdForStaffView should create query successfully - with invoice type eq HEALTH_SPECIALIST`() =
        runBlocking {
            val invoice = TestModelFactory.buildTissInvoice()
            val authorizerId = RangeUUID.generate()
            val invoiceType = InvoiceType.HEALTH_SPECIALIST

            coEvery {
                data.find(queryEq {
                    where {
                        scope(
                            scope(execIndicatorAuthorizer.diff(authorizerId).or(execIndicatorAuthorizer.isNull()))
                                .and(type.eq(invoiceType))
                                .or(execIndicatorAuthorizer.eq(authorizerId)).and(type.eq(invoiceType))
                        )
                    }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
                })
            } returns listOf(invoice.toModel()).success()

            service.findByExecAuthorizerIdForStaffView(authorizerId, invoiceType)

            coVerifyOnce { data.find(any()) }
        }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findByExecAuthorizerIdForStaffView should create query successfully - with search tokens`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()
        val authorizerId = RangeUUID.generate()
        val query = "search token"

        coEvery {
            data.find(queryEq {
                where {
                    scope(
                        scope(execIndicatorAuthorizer.diff(authorizerId).or(execIndicatorAuthorizer.isNull()))
                            .and(
                                status.notInList(listOf(TissInvoiceStatus.DRAFT))
                                    .and(type.eq(InvoiceType.HEALTH_INSTITUTION))
                                    .or(type.eq(InvoiceType.HEALTH_SPECIALIST))
                            )
                            .or(execIndicatorAuthorizer.eq(authorizerId))
                    ).and(scope(searchTokens.search(query)))
                }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
            })
        } returns listOf(invoice.toModel()).success()

        service.findByExecAuthorizerIdForStaffView(
            execIndicatorAuthorizerId = authorizerId,
            query = query
        )

        coVerifyOnce { data.find(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findByExecAuthorizerIdForStaffView should create query successfully - with range`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()
        val authorizerId = RangeUUID.generate()
        val range = IntRange(1, 19)

        coEvery {
            data.find(queryEq {
                where {
                    scope(
                        scope(execIndicatorAuthorizer.diff(authorizerId).or(execIndicatorAuthorizer.isNull()))
                            .and(
                                status.notInList(listOf(TissInvoiceStatus.DRAFT))
                                    .and(type.eq(InvoiceType.HEALTH_INSTITUTION))
                                    .or(type.eq(InvoiceType.HEALTH_SPECIALIST))
                            )
                            .or(execIndicatorAuthorizer.eq(authorizerId))
                    )
                }.orderBy { createdAt }.sortOrder { SortOrder.Descending }.offset { range.first }
                    .limit { range.count() }
            })
        } returns listOf(invoice.toModel()).success()

        service.findByExecAuthorizerIdForStaffView(
            execIndicatorAuthorizerId = authorizerId,
            range = range
        )

        coVerifyOnce { data.find(any()) }
    }

    @Test
    fun `#findByExecAuthorizerId should create query successfully - without query and range`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()
        val execIndicatorAuthorizerId = RangeUUID.generate()

        coEvery {
            data.find(queryEq {
                where {
                    execIndicatorAuthorizer.eq(execIndicatorAuthorizerId)
                }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
            })
        } returns listOf(invoice.toModel()).success()

        service.findByExecAuthorizerId(execIndicatorAuthorizerId)

        coVerifyOnce { data.find(any()) }
    }

    @Test
    fun `#findByExecAuthorizerId should create query successfully - with query and range`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()
        val execIndicatorAuthorizerId = RangeUUID.generate()
        val range = IntRange(1, 19)
        val query = "search token"

        coEvery {
            data.find(queryEq {
                where {
                    execIndicatorAuthorizer.eq(execIndicatorAuthorizerId)
                        .and(searchTokens.search(query))
                }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
                    .offset { range.first }.limit { range.count() }
            })
        } returns listOf(invoice.toModel()).success()

        service.findByExecAuthorizerId(execIndicatorAuthorizerId, query, range)

        coVerifyOnce { data.find(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findByProviderUnitIdForProviderView should create query successfully`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()
        val providerUnitId = RangeUUID.generate()

        coEvery {
            data.find(queryEq {
                where {
                    scope(
                        providerUnit.eq(providerUnitId).and(status.notInList(listOf(TissInvoiceStatus.DRAFT)))
                            .and(type.eq(InvoiceType.HEALTH_SPECIALIST))
                            .or(providerUnit.eq(providerUnitId).and(type.eq(InvoiceType.HEALTH_INSTITUTION)))
                    ).and(automaticGenerated.eq(false))
                }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
            })
        } returns listOf(invoice.toModel()).success()

        service.findByProviderUnitIdForProviderView(providerUnitId)

        coVerifyOnce { data.find(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findByProviderUnitIdForProviderView should create query successfully with staff`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()
        val providerUnitId = RangeUUID.generate()
        val staffId = RangeUUID.generate()

        coEvery {
            data.find(queryEq {
                where {
                    scope(
                        providerUnit.eq(providerUnitId).and(status.notInList(listOf(TissInvoiceStatus.DRAFT)))
                            .and(type.eq(InvoiceType.HEALTH_SPECIALIST))
                            .and(scope(staff.eq(staffId).or(staff.isNull())))
                    ).and(automaticGenerated.eq(false))
                }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
            })
        } returns listOf(invoice.toModel()).success()

        service.findByProviderUnitIdForProviderView(providerUnitId, staffId)

        coVerifyOnce { data.find(any()) }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findByProviderUnitIdForProviderView should create query successfully with staff and feature flag`() =
        runBlocking {
            val invoice = TestModelFactory.buildTissInvoice()
            val providerUnitId = RangeUUID.generate()
            val staffId = RangeUUID.generate()

            coEvery {
                data.find(queryEq {
                    where {
                        scope(
                            providerUnit.eq(providerUnitId).and(status.notInList(listOf(TissInvoiceStatus.DRAFT)))
                                .and(type.eq(InvoiceType.HEALTH_SPECIALIST))
                                .and(scope(staff.eq(staffId).or(staff.isNull())))
                        ).and(automaticGenerated.eq(true))
                    }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
                })
            } returns listOf(invoice.toModel()).success()

            val featureFlagsMap = mapOf(
                "release_automatic_generated_invoice_staff_ids" to listOf(staffId)
            )
            withFeatureFlags(namespace = FeatureNamespace.AMAS, keyValueMap = featureFlagsMap) {
                service.findByProviderUnitIdForProviderView(providerUnitId, staffId)
            }

            coVerifyOnce { data.find(any()) }
        }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#findByProviderUnitIdForStaffView should create query successfully`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()
        val providerUnitId = RangeUUID.generate()

        coEvery {
            data.find(queryEq {
                where {
                    scope(
                        providerUnit.diff(providerUnitId)
                            .and(status.notInList(listOf(TissInvoiceStatus.DRAFT)))
                            .and(type.eq(InvoiceType.HEALTH_INSTITUTION))
                            .or(type.eq(InvoiceType.HEALTH_SPECIALIST))
                            .or(providerUnit.eq(providerUnitId))
                    )
                }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
            })
        } returns listOf(invoice.toModel()).success()

        service.findByProviderUnitIdForStaffView(providerUnitId)

        coVerifyOnce { data.find(any()) }
    }

    @Test
    fun `#findByProviderUnitIdForStaffView should create query successfully with invoice type eq HEALTH_INSTITUTION`() =
        runBlocking {
            val invoice = TestModelFactory.buildTissInvoice()
            val providerUnitId = RangeUUID.generate()
            val invoiceType = InvoiceType.HEALTH_INSTITUTION

            coEvery {
                data.find(queryEq {
                    where {
                        scope(
                            status.notInList(listOf(TissInvoiceStatus.DRAFT)).and(type.eq(invoiceType))
                        )
                    }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
                })
            } returns listOf(invoice.toModel()).success()

            service.findByProviderUnitIdForStaffView(providerUnitId, invoiceType)

            coVerifyOnce { data.find(any()) }
        }

    @Test
    fun `#findByProviderUnitIdForStaffView should create query successfully with invoice type eq HEALTH_SPECIALIST`() =
        runBlocking {
            val invoice = TestModelFactory.buildTissInvoice()
            val providerUnitId = RangeUUID.generate()
            val invoiceType = InvoiceType.HEALTH_SPECIALIST

            coEvery {
                data.find(queryEq {
                    where {
                        scope(
                            type.eq(invoiceType)
                        )
                    }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
                })
            } returns listOf(invoice.toModel()).success()

            service.findByProviderUnitIdForStaffView(providerUnitId, invoiceType)

            coVerifyOnce { data.find(any()) }
        }

    @Test
    fun `#findByProviderUnitIdForStaffView should create query successfully with invoice type eq HEALTH_SPECIALIST and search token`() =
        runBlocking {
            val invoice = TestModelFactory.buildTissInvoice()
            val providerUnitId = RangeUUID.generate()
            val invoiceType = InvoiceType.HEALTH_SPECIALIST
            val query = "Test"

            coEvery {
                data.find(queryEq {
                    where {
                        scope(
                            type.eq(invoiceType)
                        ).and(searchTokens.search(query))
                    }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
                })
            } returns listOf(invoice.toModel()).success()

            service.findByProviderUnitIdForStaffView(providerUnitId, invoiceType, query)

            coVerifyOnce { data.find(any()) }
        }

    @Test
    fun `#findByProviderUnitIdForStaffView should create query successfully with invoice type eq HEALTH_SPECIALIST and range`() =
        runBlocking {
            val invoice = TestModelFactory.buildTissInvoice()
            val providerUnitId = RangeUUID.generate()
            val invoiceType = InvoiceType.HEALTH_SPECIALIST
            val range = IntRange(1, 19)

            coEvery {
                data.find(queryEq {
                    where {
                        scope(
                            type.eq(invoiceType)
                        )

                    }.orderBy { createdAt }.sortOrder { SortOrder.Descending }.offset { range.first }
                        .limit { range.count() }
                })
            } returns listOf(invoice.toModel()).success()

            service.findByProviderUnitIdForStaffView(providerUnitId, invoiceType, null, range)

            coVerifyOnce { data.find(any()) }
        }

    @Test
    fun `#findByExpenseTypeStaffIdAndAutomaticGenerated should create query successfully`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()
        val staffId = RangeUUID.generate()
        val expenseType = InvoiceExpenseType.PROCEDURES
        val automaticGenerated = true
        val status = TissInvoiceStatus.DRAFT

        coEvery {
            data.find(queryEq {
                where {
                    staff.eq(staffId)
                        .and(this.expenseType.eq(expenseType))
                        .and(this.automaticGenerated.eq(automaticGenerated))
                        .and(this.status.eq(status))
                }.orderBy { createdAt }.sortOrder { desc }
            })
        } returns listOf(invoice.toModel()).success()

        service.findByExpenseTypeStaffIdAutomaticGeneratedAndStatus(staffId, expenseType, automaticGenerated, status)

        coVerifyOnce { data.find(any()) }
    }

    @Test
    fun `#findByExpenseTypeStaffIdAutomaticGeneratedAndReferenceDate should create query successfully`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()
        val staffId = RangeUUID.generate()
        val expenseType = InvoiceExpenseType.PROCEDURES
        val automaticGenerated = true
        val referenceDate = LocalDate.now()

        coEvery {
            data.find(queryEq {
                where {
                    staff.eq(staffId)
                        .and(this.expenseType.eq(expenseType))
                        .and(this.automaticGenerated.eq(automaticGenerated))
                        .and(this.referenceDate.greaterEq(referenceDate.minusMonths(SEARCH_INVOICE_MONTHS_TO_SUBTRACT)))
                }.orderBy { createdAt }.sortOrder { desc }
            })
        } returns listOf(invoice.toModel()).success()

        mockLocalDate(referenceDate) {
            service.findLastThreeMonthsByExpenseTypeStaffIdAutomaticGenerated(
                staffId,
                expenseType,
                automaticGenerated
            )
        }
        
        coVerifyOnce { data.find(any()) }
    }

    @Test
    fun `#findByProviderUnitIdAndStaffIdPaginated should get invoices by provider unit id and staff id if role is community`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()
        val providerUnitId = RangeUUID.generate()
        val staffId = RangeUUID.generate()
        val staffRole = br.com.alice.common.core.Role.COMMUNITY
        val range = IntRange(0, 10)
        val query = "search token"

        coEvery {
            data.find(
                queryEq {
                    where {
                        status.notInList(listOf(TissInvoiceStatus.DRAFT))
                            .and(staff.eq(staffId))
                            .and(query?.let { searchTokens.search(it) })
                    }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
                        .offset { range.first }.limit { range.count() }
                }
            )
        } returns listOf(invoice.toModel()).success()

        coEvery {
            data.count(queryEq {
                where {
                    status.notInList(listOf(TissInvoiceStatus.DRAFT))
                        .and(staff.eq(staffId))
                        .and(query?.let { searchTokens.search(it) })
                }
            })
        } returns 1.success()

        service.findByProviderUnitIdAndStaffIdPaginated(providerUnitId, staffId, staffRole, range, query)

        coVerifyOnce { data.find(any()) }
        coVerifyOnce { data.count(any()) }
    }

    @Test
    fun `#findByProviderUnitIdAndStaffIdPaginated should get invoices by provider unit id if role is HEALTH_ADMINISTRATIVE`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()
        val providerUnitId = RangeUUID.generate()
        val staffId = RangeUUID.generate()
        val staffRole = br.com.alice.common.core.Role.HEALTH_ADMINISTRATIVE
        val range = IntRange(0, 10)
        val query = "search token"

        coEvery {
            data.find(
                queryEq {
                    where {
                        status.notInList(listOf(TissInvoiceStatus.DRAFT))
                            .and(providerUnit.eq(providerUnitId))
                            .and(query.let { searchTokens.search(it) })
                    }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
                        .offset { range.first }.limit { range.count() }
                }
            )
        } returns listOf(invoice.toModel()).success()

        coEvery {
            data.count(queryEq {
                where {
                    status.notInList(listOf(TissInvoiceStatus.DRAFT))
                        .and(providerUnit.eq(providerUnitId))
                        .and(query.let { searchTokens.search(it) })
                }
            })
        } returns 1.success()

        service.findByProviderUnitIdAndStaffIdPaginated(providerUnitId, staffId, staffRole, range, query)

        coVerifyOnce { data.find(any()) }
        coVerifyOnce { data.count(any()) }
    }

    @Test
    fun `#findForOpsView should get invoices by query and range`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()
        val query = "search token"
        val range = IntRange(0, 10)

        coEvery {
            data.find(queryEq {
                where { type.eq(InvoiceType.HEALTH_SPECIALIST).and(query?.let { searchTokens.search(it) }) }
                    .orderBy { createdAt }
                    .sortOrder { SortOrder.Descending }
                    .offset { range.first }
                    .limit { range.count() }
            })
        } returns listOf(invoice.toModel()).success()

        coEvery {
            data.count(queryEq {
                where { type.eq(InvoiceType.HEALTH_SPECIALIST).and(query?.let { searchTokens.search(it) }) }
            })
        } returns 1.success()

        service.findForOpsView(query, range)

        coVerifyOnce { data.find(any()) }
        coVerifyOnce { data.count(any()) }
    }
}
