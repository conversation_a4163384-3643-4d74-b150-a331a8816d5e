package br.com.alice.amas.controllers

import br.com.alice.amas.client.InvoiceService
import br.com.alice.amas.client.NationalReceiptService
import br.com.alice.amas.client.TotvsNfsService
import br.com.alice.amas.services.internal.HealthProfessionalTierHistoryService
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthProfessionalTierHistory
import br.com.alice.exec.indicator.client.ExecIndicatorAuthorizerService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class BackFillControllerTest : RecurrentControllerTestHelper() {

    private val totvsNfsService: TotvsNfsService = mockk()
    private val invoiceService: InvoiceService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val staffService: StaffService = mockk()
    private val execIndicatorAuthorizerService: ExecIndicatorAuthorizerService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val healthProfessionalTierHistoryService: HealthProfessionalTierHistoryService = mockk()
    private val nationalReceiptService: NationalReceiptService = mockk()

    private val controller = BackFillController(
        totvsNfsService,
        invoiceService,
        providerUnitService,
        staffService,
        execIndicatorAuthorizerService,
        healthProfessionalService,
        healthProfessionalTierHistoryService,
        nationalReceiptService
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @AfterTest
    fun afterTest() {
        clearAllMocks()
    }

    @Test
    fun `#backFillGetPaidTotvsNfsByPeriod - call service for range`() = runBlocking {
        val request = BackFillGetPaidTotvsNfsByPeriodRequest(
            start = LocalDate.now().minusDays(2),
            end = LocalDate.now()
        )

        coEvery {
            totvsNfsService.searchAndUpdate(request.start, request.end)
        } returns true.success()

        internalAuthentication {
            post("/backfill/get_paid_totvs_nfs_by_period", request) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            totvsNfsService.searchAndUpdate(any(), any())
        }
    }

    @Test
    fun `#populateInvoiceSearchTokens - call service and update Invoice search tokens`() = runBlocking {
        val providerUnit = TestModelFactory.buildProviderUnit()
        val authorizer = TestModelFactory.buildExecIndicatorAuthorizer(providerUnitId = providerUnit.id)
        val staff = TestModelFactory.buildStaff()

        val invoice = TestModelFactory.buildTissInvoice(
            execIndicatorAuthorizerId = authorizer.id,
            providerUnitId = providerUnit.id,
            staffId = staff.id
        )

        val request = DefaultBackFillRequest(
            limit = 10,
            offset = 0
        )

        coEvery { staffService.findByList(listOf(staff.id)) } returns listOf(staff).success()
        coEvery { invoiceService.listPaginated(request.limit, request.offset) } returns listOf(invoice).success()
        coEvery { execIndicatorAuthorizerService.findByIds(listOf(authorizer.id)) } returns listOf(authorizer).success()
        coEvery { providerUnitService.getByIds(listOf(providerUnit.id)) } returns listOf(providerUnit).success()

        val expected = invoice.copy(
            searchTokens = "${invoice.code} ${staff.fullName} ${providerUnit.name} ${providerUnit.cnpj}".unaccent()
        )

        coEvery { invoiceService.updateList(listOf(expected)) } returns listOf(invoice).success()

        internalAuthentication {
            post("/backfill/update_invoice_search_tokens", request) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }


        coVerifyOnce { invoiceService.updateList(any()) }
    }

    @Test
    fun `#populateInvoiceSearchTokens - call service and update Invoice search tokens with only authorizer`() =
        runBlocking {
            val providerUnit = TestModelFactory.buildProviderUnit()
            val authorizer = TestModelFactory.buildExecIndicatorAuthorizer(providerUnitId = providerUnit.id)
            val staff = TestModelFactory.buildStaff()

            val invoice = TestModelFactory.buildTissInvoice(
                execIndicatorAuthorizerId = authorizer.id,
                providerUnitId = null,
                staffId = staff.id
            )

            val request = DefaultBackFillRequest(
                limit = 10,
                offset = 0
            )

            coEvery { staffService.findByList(listOf(staff.id)) } returns listOf(staff).success()
            coEvery { invoiceService.listPaginated(request.limit, request.offset) } returns listOf(invoice).success()
            coEvery { execIndicatorAuthorizerService.findByIds(listOf(authorizer.id)) } returns listOf(authorizer).success()
            coEvery {
                providerUnitService.getByIds(listOf(authorizer.providerUnitId))
            } returns listOf(providerUnit).success()

            val expected = invoice.copy(
                searchTokens = "${invoice.code} ${staff.fullName} ${providerUnit.name} ${providerUnit.cnpj}".unaccent()
            )

            coEvery { invoiceService.updateList(listOf(expected)) } returns listOf(invoice).success()

            internalAuthentication {
                post("/backfill/update_invoice_search_tokens", request) { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }


            coVerifyOnce { invoiceService.updateList(any()) }
        }

    @Test
    fun `#populateHealthProfessionalTierHistory should call createIfNecessary for each hp`() = runBlocking {
        val request = DefaultBackFillRequest(
            limit = 10,
            offset = 0
        )
        val hp = TestModelFactory.buildHealthProfessional(
            tier = SpecialistTier.EXPERT,
            theoristTier = SpecialistTier.EXPERT
        )
        val hp2 = TestModelFactory.buildHealthProfessional(
            tier = SpecialistTier.EXPERT,
            theoristTier = SpecialistTier.SUPER_EXPERT
        )

        coEvery {
            healthProfessionalService.getByRange(IntRange(request.offset, request.offset + request.limit))
        } returns listOf(hp, hp2).success()
        coEvery {
            healthProfessionalTierHistoryService.createFromHealthProfessionalIfNecessary(hp)
        } returns HealthProfessionalTierHistory(
            staffId = hp.staffId,
            tier = hp.tier!!,
            theoristTier = hp.theoristTier!!,
            activatedAt = hp.updatedAt
        ).success()
        coEvery {
            healthProfessionalTierHistoryService.createFromHealthProfessionalIfNecessary(hp2)
        } returns HealthProfessionalTierHistory(
            staffId = hp2.staffId,
            tier = hp2.tier!!,
            theoristTier = hp2.theoristTier!!,
            activatedAt = hp2.updatedAt
        ).success()

        internalAuthentication {
            post("/backfill/populate_health_professional_tier_history", request) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerify(exactly = 2) { healthProfessionalTierHistoryService.createFromHealthProfessionalIfNecessary(any()) }
    }

    @Test
    fun `#populateInvoiceReferenceDate should update invoice reference dates`() = runBlocking {
        val invoice = TestModelFactory.buildTissInvoice()

        coEvery {
            invoiceService.findByIds(listOf(invoice.id))
        } returns listOf(invoice).success()

        val referenceDate = invoice.createdAt.minusMonths(1).toLocalDate()
        coEvery {
            invoiceService.updateList(match {
                it.first().referenceDate == referenceDate
            })
        } returns listOf(invoice).success()

        val request = BackFillInvoicesRequest(
            ids = listOf(invoice.id)
        )

        internalAuthentication {
            post("/backfill/populate_invoice_reference_date", request) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            invoiceService.updateList(any())
        }
    }

    @Test
    fun `#updateNationalReceipts should update national receipts`() = runBlocking {
        val nationalReceipts = listOf(
            TestModelFactory.buildNationalReceipt(),
            TestModelFactory.buildNationalReceipt(),
            TestModelFactory.buildNationalReceipt()
        )

        coEvery {
            nationalReceiptService.findAll(0, 10)
        } returns nationalReceipts.success()

        coEvery {
            nationalReceiptService.updateList(nationalReceipts)
        } returns nationalReceipts.success()

        val request = DefaultBackFillRequest(
            limit = 10,
            offset = 0
        )

        internalAuthentication {
            post("/backfill/update_national_receipts", request) { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            nationalReceiptService.updateList(any())
        }
    }
}
