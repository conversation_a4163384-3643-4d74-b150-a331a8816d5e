package br.com.alice.amas.services

import br.com.alice.amas.client.InvoiceService
import br.com.alice.amas.client.InvoicesListWithCount
import br.com.alice.amas.converters.toModel
import br.com.alice.amas.converters.toTransport
import br.com.alice.common.core.Role
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Invoice
import br.com.alice.data.layer.models.InvoiceExpenseType
import br.com.alice.data.layer.models.InvoiceType
import br.com.alice.data.layer.models.TissInvoiceStatus
import br.com.alice.data.layer.services.InvoiceModelDataService
import br.com.alice.exec.indicator.client.ExecIndicatorAuthorizerService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.util.UUID

class InvoiceServiceImpl(
    private val data: InvoiceModelDataService,
    private val providerUnitService: ProviderUnitService,
    private val staffService: StaffService,
    private val authorizerService: ExecIndicatorAuthorizerService,
) : InvoiceService {

    override suspend fun add(model: Invoice): Result<Invoice, Throwable> =
        coResultOf<Invoice, Throwable> {
            coroutineScope {
                val authorizerDeferred = model.execIndicatorAuthorizerId?.let { async { authorizerService.get(it) } }
                val staffDeferred = model.staffId?.let { async { staffService.get(it) } }

                val authorizer = authorizerDeferred?.await()?.get()

                val providerUnitDeferred = if (authorizer != null) {
                    providerUnitService.get(authorizer.providerUnitId)
                } else model.providerUnitId?.let { providerUnitService.get(it) }

                val staff = staffDeferred?.await()?.get()?.fullName ?: ""

                val providerUnit = providerUnitDeferred?.get()
                val providerUnitName = providerUnit?.name ?: ""
                val providerUnitCnpj = providerUnit?.cnpj ?: ""

                val searchTokens = "${model.code} $staff $providerUnitName $providerUnitCnpj"

                model.copy(searchTokens = searchTokens.unaccent())
            }
        }.flatMap { data.add(it.toModel()) }.map { it.toTransport() }

    override suspend fun get(id: UUID): Result<Invoice, Throwable> =
        data.get(id).map { it.toTransport() }

    override suspend fun update(model: Invoice): Result<Invoice, Throwable> =
        data.update(model.defaultSearchTokens().toModel()).map { it.toTransport() }

    override suspend fun delete(model: Invoice): Result<Boolean, Throwable> =
        data.delete(model.toModel())

    override suspend fun updateList(models: List<Invoice>, returnOnFailure: Boolean) =
        data.updateList(models.map { it.defaultSearchTokens() }.map { it.toModel() }, returnOnFailure)
            .mapEach { it.toTransport() }

    override suspend fun findByIds(ids: List<UUID>) =
        data.find { where { id.inList(ids) } }.mapEach { it.toTransport() }

    override suspend fun findByIdsAndStatus(ids: List<UUID>, status: TissInvoiceStatus) =
        data.find { where { id.inList(ids).and(this.status.eq(status)) } }.mapEach { it.toTransport() }

    override suspend fun findForOpsView(query: String?, range: IntRange) = coroutineScope {
        val rowsDef = async {
            data.find {
                where {
                    type.eq(InvoiceType.HEALTH_SPECIALIST).and(query?.let { searchTokens.search(it) })
                }
                    .orderBy { createdAt }
                    .sortOrder { SortOrder.Descending }
                    .offset { range.first }
                    .limit { range.count() }
            }.mapEach { it.toTransport() }
        }

        val countDef = async {
            data.count {
                where {
                    type.eq(InvoiceType.HEALTH_SPECIALIST).and(query?.let { searchTokens.search(it) })
                }
            }
        }

        coResultOf<InvoicesListWithCount, Throwable> {
            InvoicesListWithCount(
                list = rowsDef.await().get(),
                count = countDef.await().get()
            )
        }
    }

    override suspend fun findByExecAuthorizerIdForStaffView(
        execIndicatorAuthorizerId: UUID,
        invoiceType: InvoiceType?,
        query: String?,
        range: IntRange?
    ): Result<List<Invoice>, Throwable> =
        data.find {
            val whereCondition = where {
                this.execAuthorizerIdForStaffView(execIndicatorAuthorizerId, invoiceType, query)
            }.orderBy { createdAt }.sortOrder { SortOrder.Descending }

            range?.let { whereCondition.offset { it.first }.limit { it.count() } } ?: whereCondition
        }.mapEach { it.toTransport() }

    override suspend fun countByExecAuthorizerIdForStaffView(
        execIndicatorAuthorizerId: UUID,
        invoiceType: InvoiceType?,
        query: String?
    ): Result<Int, Throwable> =
        data.count { where { this.execAuthorizerIdForStaffView(execIndicatorAuthorizerId, invoiceType, query) } }


    override suspend fun findByExecAuthorizerId(
        execIndicatorAuthorizerId: UUID,
        query: String?,
        range: IntRange?
    ): Result<List<Invoice>, Throwable> =
        data.find {
            val whereCondition = where {
                execIndicatorAuthorizer.eq(execIndicatorAuthorizerId)
                    .and(query?.let { searchTokens.search(it) })
            }.orderBy { createdAt }.sortOrder { SortOrder.Descending }

            range?.let { whereCondition.offset { it.first }.limit { it.count() } } ?: whereCondition
        }.mapEach { it.toTransport() }

    override suspend fun countByExecAuthorizerId(
        execIndicatorAuthorizerId: UUID,
        query: String?
    ): Result<Int, Throwable> =
        data.count {
            where {
                execIndicatorAuthorizer.eq(execIndicatorAuthorizerId)
                    .and(query?.let { searchTokens.search(it) })
            }
        }

    override suspend fun findByProviderUnitId(providerUnitId: UUID) =
        data.find { where { providerUnit.eq(providerUnitId) }.orderBy { updatedAt }.sortOrder { desc } }
            .mapEach { it.toTransport() }

    override suspend fun findByProviderUnitIdAndStaffIdPaginated(
        providerUnitId: UUID,
        staffId: UUID,
        staffRole: Role,
        range: IntRange,
        query: String?,
    ): Result<InvoicesListWithCount, Throwable> = useReadDatabase {
        logger.info(
            "Searching invoices for provider unit",
            "provider_unit_id" to providerUnitId,
            "staff_id" to staffId,
            "role" to staffRole,
            "range" to range,
            "query" to query
        )
        coroutineScope {
            val isCommunityStaff = staffRole != Role.HEALTH_ADMINISTRATIVE

            val rowsDeferred = async {
                data.find {
                    where {
                        status.notInList(listOf(TissInvoiceStatus.DRAFT))
                            .and(if (!isCommunityStaff) providerUnit.eq(providerUnitId) else null)
                            .and(if (isCommunityStaff) staff.eq(staffId) else null)
                            .and(query?.let { searchTokens.search(it) })
                    }.orderBy { createdAt }.sortOrder { SortOrder.Descending }
                        .offset { range.first }.limit { range.count() }
                }
            }

            val countDeferred = async {
                data.count {
                    where {
                        status.notInList(listOf(TissInvoiceStatus.DRAFT))
                            .and(if (!isCommunityStaff) providerUnit.eq(providerUnitId) else null)
                            .and(if (isCommunityStaff) staff.eq(staffId) else null)
                            .and(query?.let { searchTokens.search(it) })
                    }
                }
            }

            coResultOf {
                InvoicesListWithCount(
                    list = rowsDeferred.await().mapEach { it.toTransport() }.get(),
                    count = countDeferred.await().get()
                )
            }
        }
    }

    override suspend fun findByProviderUnitIdForProviderView(
        providerUnitIdValue: UUID,
        staffId: UUID?,
        query: String?,
        range: IntRange?
    ): Result<List<Invoice>, Throwable> =
        data.find {
            val whereCondition = where {
                this.providerUnitIdForProviderView(providerUnitIdValue, staffId, query)
            }.orderBy { createdAt }.sortOrder { SortOrder.Descending }

            range?.let { whereCondition.offset { range.first }.limit { range.count() } } ?: whereCondition
        }.mapEach { it.toTransport() }

    override suspend fun countByProviderUnitIdForProviderView(
        providerUnitIdValue: UUID,
        staffId: UUID?,
        query: String?
    ): Result<Int, Throwable> =
        data.count { where { this.providerUnitIdForProviderView(providerUnitIdValue, staffId, query) } }

    override suspend fun findByProviderUnitIdForStaffView(
        providerUnitId: UUID,
        invoiceType: InvoiceType?,
        query: String?,
        range: IntRange?
    ): Result<List<Invoice>, Throwable> =
        data.find {
            val whereCondition = where {
                this.providerUnitIdForStaffView(providerUnitId, invoiceType, query)
            }.orderBy { createdAt }.sortOrder { SortOrder.Descending }

            range?.let { whereCondition.offset { range.first }.limit { range.count() } } ?: whereCondition
        }.mapEach { it.toTransport() }

    override suspend fun countByProviderUnitIdForStaffView(
        providerUnitId: UUID,
        invoiceType: InvoiceType?,
        query: String?
    ) = data.count { where { this.providerUnitIdForStaffView(providerUnitId, invoiceType, query) } }

    @OptIn(QueryAllUsage::class)
    override suspend fun listPaginated(limit: Int, offset: Int): Result<List<Invoice>, Throwable> =
        data.find { all().limit { limit }.offset { offset }.orderBy { createdAt }.sortOrder { SortOrder.Descending } }
            .mapEach { it.toTransport() }

    override suspend fun findByExpenseTypeStaffIdAutomaticGeneratedAndStatus(
        staffId: UUID,
        expenseType: InvoiceExpenseType,
        automaticGenerated: Boolean,
        status: TissInvoiceStatus
    ) = data.find {
        where {
            staff.eq(staffId)
                .and(this.expenseType.eq(expenseType))
                .and(this.automaticGenerated.eq(automaticGenerated))
                .and(this.status.eq(status))
        }.orderBy { createdAt }.sortOrder { desc }
    }.mapEach { it.toTransport() }

    override suspend fun findLastThreeMonthsByExpenseTypeStaffIdAutomaticGenerated(
        staffId: UUID,
        expenseType: InvoiceExpenseType,
        automaticGenerated: Boolean
    ) = data.find {
        where {
            staff.eq(staffId)
                .and(this.expenseType.eq(expenseType))
                .and(this.automaticGenerated.eq(automaticGenerated))
                .and(this.referenceDate.greaterEq(LocalDate.now().minusMonths(SEARCH_INVOICE_MONTHS_TO_SUBTRACT)))
        }.orderBy { createdAt }.sortOrder { desc }
    }.mapEach { it.toTransport() }

    private fun Invoice.defaultSearchTokens() = this.copy(searchTokens = this.searchTokens ?: this.code)

    @OptIn(OrPredicateUsage::class)
    private fun InvoiceModelDataService.FieldOptions.execAuthorizerIdForStaffView(
        execIndicatorAuthorizerId: UUID,
        invoiceType: InvoiceType?,
        query: String?
    ): Predicate {
        val invoiceTypeCondition = when (invoiceType) {
            InvoiceType.HEALTH_SPECIALIST -> type.eq(InvoiceType.HEALTH_SPECIALIST)
            InvoiceType.HEALTH_INSTITUTION -> status.notInList(listOf(TissInvoiceStatus.DRAFT))
                .and(type.eq(InvoiceType.HEALTH_INSTITUTION))

            else -> status.notInList(listOf(TissInvoiceStatus.DRAFT))
                .and(type.eq(InvoiceType.HEALTH_INSTITUTION))
                .or(type.eq(InvoiceType.HEALTH_SPECIALIST))
        }

        val search = query?.let { scope(searchTokens.search(it)) }

        val queryBuilder = scope(
            scope(execIndicatorAuthorizer.diff(execIndicatorAuthorizerId).or(execIndicatorAuthorizer.isNull()))
                .and(invoiceTypeCondition)
                .or(execIndicatorAuthorizer.eq(execIndicatorAuthorizerId))
                .and(invoiceType?.let { type.eq(it) })
        )

        return queryBuilder.and(search)
    }

    @OptIn(OrPredicateUsage::class)
    private fun InvoiceModelDataService.FieldOptions.providerUnitIdForStaffView(
        providerUnitId: UUID,
        invoiceType: InvoiceType?,
        query: String?
    ): Predicate {
        val condition = scope(
            when (invoiceType) {
                InvoiceType.HEALTH_INSTITUTION -> status.notInList(listOf(TissInvoiceStatus.DRAFT))
                    .and(type.eq(invoiceType))

                InvoiceType.HEALTH_SPECIALIST -> type.eq(invoiceType)

                else -> providerUnit.diff(providerUnitId)
                    .and(status.notInList(listOf(TissInvoiceStatus.DRAFT)))
                    .and(type.eq(InvoiceType.HEALTH_INSTITUTION))
                    .or(type.eq(InvoiceType.HEALTH_SPECIALIST))
                    .or(providerUnit.eq(providerUnitId))
            }
        )

        val searchTokens = query?.let { searchTokens.search(it) }

        return condition.and(searchTokens)
    }

    @OptIn(OrPredicateUsage::class)
    private fun InvoiceModelDataService.FieldOptions.providerUnitIdForProviderView(
        providerUnitIdValue: UUID,
        staffId: UUID?,
        query: String?
    ): Predicate {
        val condition = providerUnit.eq(providerUnitIdValue).and(status.notInList(listOf(TissInvoiceStatus.DRAFT)))
            .and(type.eq(InvoiceType.HEALTH_SPECIALIST))
            .and(staffId?.let { scope(staff.eq(staffId).or(staff.isNull())) })

        val staffCondition = providerUnit.eq(providerUnitIdValue).and(type.eq(InvoiceType.HEALTH_INSTITUTION))
        val queryBuilder = scope(staffId?.let { condition } ?: condition.or(staffCondition))

        val searchTokens = query?.let { searchTokens.search(it) }

        return if (shouldSearchForAutomaticGenerated(staffId)) {
            queryBuilder.and(searchTokens).and(automaticGenerated.eq(true))
        } else queryBuilder.and(searchTokens).and(automaticGenerated.eq(false))

    }

    private fun shouldSearchForAutomaticGenerated(staffId: UUID?) =
        FeatureService.anyInList(
            FeatureNamespace.AMAS,
            "release_automatic_generated_invoice_staff_ids",
            staffId?.let { listOf("*", staffId.toString()) } ?: listOf("*"),
            false
        )

    companion object {
        const val SEARCH_INVOICE_MONTHS_TO_SUBTRACT = 3L
    }

}
