package br.com.alice.amas.controllers

import br.com.alice.amas.client.InvoiceService
import br.com.alice.amas.client.NationalReceiptService
import br.com.alice.amas.client.TotvsNfsService
import br.com.alice.amas.services.internal.HealthProfessionalTierHistoryService
import br.com.alice.common.Response
import br.com.alice.common.asyncLayer
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.toResponse
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.AMAS_BACKFILL_SERVICE_NAME
import br.com.alice.exec.indicator.client.ExecIndicatorAuthorizerService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.util.UUID

class BackFillController(
    private val totvsNfsService: TotvsNfsService,
    private val invoiceService: InvoiceService,
    private val providerUnitService: ProviderUnitService,
    private val staffService: StaffService,
    private val execIndicatorAuthorizerService: ExecIndicatorAuthorizerService,
    private val healthProfessionalService: HealthProfessionalService,
    private val healthProfessionalTierHistoryService: HealthProfessionalTierHistoryService,
    private val nationalReceiptService: NationalReceiptService
) : Controller() {

    private suspend fun withBackFillEnvironment(func: suspend () -> Response) =
        asyncLayer {
            withRootServicePolicy(AMAS_BACKFILL_SERVICE_NAME) {
                withUnauthenticatedTokenWithKey(AMAS_BACKFILL_SERVICE_NAME) {
                    func.invoke()
                }
            }
        }

    suspend fun backFillGetPaidTotvsNfsByPeriod(request: BackFillGetPaidTotvsNfsByPeriodRequest): Response =
        withBackFillEnvironment {
            logger.info(
                "Start backFillGetPaidTotvsNfsByPeriod",
                "start" to request.start,
                "end" to request.end
            )

            totvsNfsService.searchAndUpdate(
                request.start,
                request.end
            )

            logger.info(
                "end backFillGetPaidTotvsNfsByPeriod",
                "start" to request.start,
                "end" to request.end
            )
            true.toResponse()
        }

    suspend fun populateInvoiceSearchTokens(request: DefaultBackFillRequest): Response =
        withBackFillEnvironment {
            val invoices = invoiceService.listPaginated(request.limit, request.offset).get()

            logger.info("BackFillController#populateInvoiceSearchTokens", "invoices" to invoices.size)

            val staffIds = invoices.mapNotNull { it.staffId }.distinct()
            val invoiceProviderUnitIds = invoices.mapNotNull { it.providerUnitId }
            val authorizerIds = invoices.mapNotNull { it.execIndicatorAuthorizerId }.distinct()

            coroutineScope {
                val staffsDeferred = async { staffService.findByList(staffIds) }
                val authorizersDeferred = async { execIndicatorAuthorizerService.findByIds(authorizerIds) }

                val authorizers = authorizersDeferred.await().get()
                val authorizerProviderUnitIds = authorizers.map { it.providerUnitId }
                val providerUnitIds = invoiceProviderUnitIds.plus(authorizerProviderUnitIds).distinct()

                val providerUnitsDeferred = async { providerUnitService.getByIds(providerUnitIds) }

                val authorizerMap = authorizers.associateBy { it.id }
                val staffMap = staffsDeferred.await().get().associateBy { it.id }
                val providerUnitMap = providerUnitsDeferred.await().get().associateBy { it.id }

                val invoicesToUpdate = invoices.map {
                    val staff = staffMap[it.staffId]?.fullName ?: ""
                    val providerUnit = providerUnitMap[it.providerUnitId]
                        ?: providerUnitMap[authorizerMap[it.execIndicatorAuthorizerId]?.providerUnitId]
                    val providerUnitName = providerUnit?.name ?: ""
                    val providerUnitCnpj = providerUnit?.cnpj ?: ""

                    val searchTokens = "${it.code} $staff $providerUnitName $providerUnitCnpj"

                    it.copy(searchTokens = searchTokens.unaccent())
                }

                invoicesToUpdate.chunked(50).pmap { invoiceService.updateList(it) }

                true.toResponse()
            }
        }

    suspend fun populateHealthProfessionalTierHistory(request: DefaultBackFillRequest) =
        withBackFillEnvironment {
            healthProfessionalService.getByRange(IntRange(request.offset, request.offset + request.limit)).mapEach {
                healthProfessionalTierHistoryService.createFromHealthProfessionalIfNecessary(it)
            }.foldResponse()
        }

    suspend fun populateInvoiceReferenceDate(request: BackFillInvoicesRequest) =
        withBackFillEnvironment {
            invoiceService.findByIds(request.ids).flatMap {
                val updatedInvoices = it.map {
                    val referenceDate = if (it.automaticGenerated) {
                        it.createdAt.toLocalDate()
                    } else {
                        it.createdAt.minusMonths(1).toLocalDate()
                    }
                    it.copy(referenceDate = referenceDate)
                }
                updatedInvoices.chunked(50).pmap {
                    invoiceService.updateList(it)
                }
                true.success()
            }.foldResponse()
        }

    suspend fun updateNationalReceipts(request: DefaultBackFillRequest) =
        withBackFillEnvironment {
            nationalReceiptService.findAll(request.offset, request.limit).flatMap { nationalReceipts ->
                nationalReceipts.chunked(50).pmap {
                    nationalReceiptService.updateList(it)
                }
                true.success()
            }.foldResponse()
        }
}

data class DefaultBackFillRequest(
    val offset: Int,
    val limit: Int
)

data class BackFillGetPaidTotvsNfsByPeriodRequest(
    val start: LocalDate,
    val end: LocalDate
)

data class BackFillInvoicesRequest(
    val ids: List<UUID>
)
