package br.com.alice.hr.core.util

import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem

sealed class ValidationResult<out T> {
    data class Success<out T>(val value: T) : ValidationResult<T>()
    data class Failure(val errors: List<BeneficiaryBatchValidationErrorItem>) : ValidationResult<Nothing>()
}

data class RuleAndMessage<T>(
    val rule: (T) -> <PERSON><PERSON><PERSON>,
    val errorMessage: String,
)

class Validator<T> private constructor(
    private val value: T,
    private val field: String,
    private val errors: List<BeneficiaryBatchValidationErrorItem> = emptyList()
) {
    fun validate(rules: List<RuleAndMessage<T>>): Validator<T> {
        for ((rule, message) in rules) {
            if (rule(value)) {
                return Validator(
                    value = value,
                    field = field,
                    errors = errors + BeneficiaryBatchValidationErrorItem(field, message)
                )
            }
        }
        return this
    }

    fun getResult(): ValidationResult<T> {
        return if (errors.isEmpty()) {
            ValidationResult.Success(value)
        } else {
            ValidationResult.Failure(errors)
        }
    }

    companion object {
        fun <T> of(value: T, field: String): Validator<T> = Validator(value, field)
    }
}

fun <T> validateField(
    value: T,
    field: String,
    rules: List<RuleAndMessage<T>>
) = Validator.of(value, field)
    .validate(rules)
    .getResult()
    .let { if (it is ValidationResult.Failure) it.errors else emptyList() }

class ValidationCollector(private val index: Int) {
    private val errors = mutableListOf<BeneficiaryBatchValidationErrorItem>()

    fun <T> check(
        value: T,
        field: String,
        rules: List<RuleAndMessage<T>>
    ) {
        errors += validateField(value, field, rules)
    }

    fun getErrors(): BeneficiaryBatchValidationError? {
        return if (errors.isEmpty()) null else BeneficiaryBatchValidationError(index, errors)
    }
}
