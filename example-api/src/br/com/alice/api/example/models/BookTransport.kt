package br.com.alice.api.example.models

import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.Nested
import br.com.alice.data.layer.models.Reference
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CreateBookRequest(
    val book: BookRequest
)

data class BookRequest(
    val name: String,
    val author: String,
    val isbn: String,
    val age: Int?,
    val available: Boolean,
    val genres: List<String>,
    val nested: Nested? = null,
    val jsonObjectArray: List<Reference> = emptyList(),
    val launchDate: LocalDate = LocalDate.now(),
    val personId: PersonId = PersonId(),
)

data class CreateBookResponse(
    val book: BookResponse
)

data class BookResponse(
    val id: UUID,
    val name: String,
    val author: String,
    val isbn: String,
    val age: Int?,
    val available: Boolean,
    val genres: List<String>,
    val nested: Nested? = null,
    val launchDate: LocalDate,
    val createdAt: LocalDateTime,
    val personId: PersonId,
    val searchTokens: String?
)
