package br.com.alice.ehr.clients.memed

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.ehr.ServiceConfig
import br.com.alice.ehr.clients.memed.MemedStaffConverter.healthProfessionalToMemedUserRequest
import br.com.alice.ehr.exceptions.MemedApiCallException
import br.com.alice.ehr.exceptions.MemedNotActiveUserException
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.mapError
import com.github.kittinunf.result.success
import io.ktor.client.HttpClient
import io.ktor.client.plugins.ResponseException
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.headers
import io.ktor.client.request.request
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.client.utils.EmptyContent
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.contentType

private const val MEMED_USERS_ROUTE = "v1/sinapse-prescricao/usuarios"
private const val MEMED_PRESCRIPTIONS_ROUTE = "v1/prescricoes"
private const val MEMED_LAYOUT_CONFIGURATION_ROUTE = "v1/opcoes-receituario"

class MemedClientImpl(
    private val httpClient: HttpClient = DefaultHttpClient({
        install(ContentNegotiation) {
            gsonSnakeCase()
        }
    }, timeoutInMillis = 15_000)
) : MemedClient {
    private val credentials = ServiceConfig.MemedCredentials
    private val templates = ServiceConfig.MemedTemplates

    override suspend fun createUser(healthProfessional: HealthProfessional): Result<MemedUser, Throwable> =
        span("createUser") { span ->
            span.setAttribute("staff_id", healthProfessional.staff?.id)
            span.setAttribute("health_professional_id", healthProfessional.id)
            span.setAttribute("national_id", healthProfessional.staff?.nationalId)

            logger.info(
                "Creating user in Memed",
                "health_professional_id" to healthProfessional.id,
                "staff_id" to healthProfessional.staff?.id,
                "national_id" to healthProfessional.staff?.nationalId,
                "external_id" to healthProfessional.staff?.id?.toString()
            )

            postMemedUser(healthProfessionalToMemedUserRequest(healthProfessional)).map {
                MemedUserResponse(it).deserialize()
            }.recordResult(span)
        }

    override suspend fun updateUser(healthProfessional: HealthProfessional): Result<MemedUser, Throwable> =
        span("updateUser") { span ->
            span.setAttribute("staff_id", healthProfessional.staff?.id)
            span.setAttribute("health_professional_id", healthProfessional.id)
            span.setAttribute("national_id", healthProfessional.staff?.nationalId)

            logger.info(
                "Updating user in Memed",
                "health_professional_id" to healthProfessional.id,
                "staff_id" to healthProfessional.staff?.id,
                "national_id" to healthProfessional.staff?.nationalId,
                "external_id" to healthProfessional.staff?.id?.toString()
            )

            patchMemedUser(healthProfessionalToMemedUserRequest(healthProfessional)).map {
                MemedUserResponse(it).deserialize()
            }.recordResult(span)
        }

    override suspend fun getUser(nationalId: String): Result<MemedUser, Throwable> =
        span("getUser") { span ->
            span.setAttribute("national_id", nationalId)

            callMemedApi(
                HttpMethod.Get,
                apiKeyUrl("$MEMED_USERS_ROUTE/${nationalId.onlyNumbers()}")
            ).map {
                MemedUserResponse(it).deserialize()
            }.mapError {
                if (it is NotFoundException) it
                else MemedApiCallException(it.message ?: "failed to call Memed API", cause = it)
            }.recordResult(span)
        }

    override suspend fun getUserToken(nationalId: String): Result<String, Throwable> =
        getUser(nationalId).map { memedUser ->
            val inactiveUserStatus = memedUser.status() != MemedUserStatus.ACTIVE

            if (inactiveUserStatus) throw MemedNotActiveUserException()

            memedUser.token()
        }

    override suspend fun getUserStatus(nationalId: String): Result<MemedUserStatus, Throwable> =
        getUser(nationalId).map {
            it.status()
        }.coFoldNotFound {
            MemedUserStatus.NOT_FOUND.success()
        }.mapError {
            MemedApiCallException(it.message ?: "failed to call Memed API", cause = it)
        }

    override suspend fun getPrescription(
        prescriptionId: String,
        physicianToken: String,
    ): Result<MemedPrescription, Throwable> =
        callMemedApi(
            HttpMethod.Get,
            prescriptionUrl(prescriptionId, physicianToken)
        ).map {
            MemedPrescriptionResponse(it).deserialize()
        }

    override suspend fun getPrescriptionLink(
        prescriptionId: String,
        physicianToken: String,
    ): Result<MemedPrescriptionLink, Throwable> =
        callMemedApi(
            HttpMethod.Get,
            prescriptionLinkUrl(prescriptionId, physicianToken)
        ).map { MemedPrescriptionLinkResponse(it).deserialize() }

    override suspend fun getPrescriptionDocument(
        prescriptionID: String,
        physicianToken: String
    ): Result<MemedPrescriptionDocument, Throwable> =
        callMemedApi(
            HttpMethod.Get,
            prescriptionDocumentUrl(prescriptionID, physicianToken)
        ).map { MemedPrescriptionDocumentResponse(it).deserialize() }

    override suspend fun setPrescriptionLayout(
        physicianToken: String
    ): Result<MemedLayoutConfig, Throwable> =
        createLayoutConfiguration(
            getDefaultPrescriptionLayout(),
            physicianToken
        ).map {
            MemedLayoutConfigResponse(it).deserialize()
        }

    override suspend fun setEmptyPrescriptionLayout(
        physicianToken: String
    ): Result<MemedLayoutConfig, Throwable> =
        createLayoutConfiguration(
            getEmptyPrescriptionLayout(),
            physicianToken
        ).map {
            MemedLayoutConfigResponse(it).deserialize()
        }

    private suspend fun createLayoutConfiguration(
        memedLayout: MemedLayoutConfigRequest,
        physicianToken: String
    ): Result<HttpResponse, Throwable> =
        callMemedApi(
            httpMethod = HttpMethod.Post,
            url = layoutConfigurationUrl(physicianToken),
            payload = memedLayout
        )

    private suspend fun postMemedUser(
        memedUser: MemedUserRequest,
    ): Result<HttpResponse, Throwable> =
        callMemedApi(
            httpMethod = HttpMethod.Post,
            url = apiKeyUrl(MEMED_USERS_ROUTE),
            payload = memedUser
        )

    private suspend fun patchMemedUser(
        memedUser: MemedUserRequest,
    ): Result<HttpResponse, Throwable> =
        callMemedApi(
            httpMethod = HttpMethod.Patch,
            url = apiKeyUrl("$MEMED_USERS_ROUTE/${memedUser.data.attributes.cpf}"),
            payload = memedUser
        )

    private fun apiKeyUrl(route: String) =
        "https://${credentials.domain}/$route" +
                "?api-key=${credentials.apiKey}" +
                "&secret-key=${credentials.secretKey}"

    private fun prescriptionUrl(prescriptionId: String, physicianToken: String) =
        "https://${credentials.domain}/$MEMED_PRESCRIPTIONS_ROUTE/$prescriptionId" +
                "?token=$physicianToken"

    private fun prescriptionLinkUrl(prescriptionId: String, physicianToken: String) =
        "https://${credentials.domain}/$MEMED_PRESCRIPTIONS_ROUTE/$prescriptionId" +
                "/get-digital-prescription-link" +
                "?token=$physicianToken"

    private fun prescriptionDocumentUrl(prescriptionId: String, physicianToken: String) =
        "https://${credentials.domain}/$MEMED_PRESCRIPTIONS_ROUTE/$prescriptionId" +
                "/url-document/full" +
                "?token=$physicianToken"

    private fun layoutConfigurationUrl(physicianToken: String) =
        "https://${credentials.domain}/$MEMED_LAYOUT_CONFIGURATION_ROUTE" +
                "?token=$physicianToken"

    private fun getDefaultPrescriptionLayout() =
        MemedLayoutConfigRequest(
            data = MemedLayoutConfigData(
                attributes = MemedLayoutConfigAttributes(
                    footerImage = templates.defaultFooterLink,
                    headerImage = templates.defaultHeaderLink
                )
            )
        )

    private fun getEmptyPrescriptionLayout() =
        MemedLayoutConfigRequest(
            data = MemedLayoutConfigData(
                attributes = MemedLayoutConfigAttributes(
                    footerImage = "",
                    headerImage = ""
                )
            )
        )

    private suspend fun callMemedApi(
        httpMethod: HttpMethod,
        url: String,
        payload: Any = EmptyContent,
    ): Result<HttpResponse, Throwable> = span("callMemedApi:$url") { span ->
        coResultOf<HttpResponse, Throwable> {
            try {
                httpClient.request(url) {
                    method = httpMethod
                    headers {
                        append(HttpHeaders.Accept, "application/vnd.api+json")
                        append(HttpHeaders.CacheControl, "no-cache")
                    }
                    contentType(ContentType.Application.Json)
                    setBody(payload)
                }
            } catch (e: ResponseException) {
                if (e.response.status == HttpStatusCode.NotFound) {
                    throw NotFoundException("entity not found")
                } else {
                    logger.error(
                        "Memed API call failed",
                        "url" to url,
                        "method" to httpMethod.value,
                        "status_code" to e.response.status.value,
                        "error_message" to e.message,
                        "response_body" to runCatching { e.response.bodyAsText() }.getOrNull()
                    )
                    throw MemedApiCallException(e.message ?: "failed to call Memed API", cause = e)
                }
            }
        }.recordResult(span)
    }
}
