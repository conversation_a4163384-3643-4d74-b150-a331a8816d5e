package br.com.alice.member.api.services

import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.action.plan.model.ActionPlanTaskFilters
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ActionPlanTask
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.HealthPlanTaskTemplate
import br.com.alice.data.layer.models.MemberOnboardingReferencedLink
import br.com.alice.data.layer.models.MemberOnboardingReferencedLinkModel
import br.com.alice.healthplan.client.HealthPlanTaskTemplateService
import br.com.alice.member.onboarding.model.PhysicianStaff
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.Test
import kotlinx.coroutines.runBlocking

class MemberOnboardingConclusionServiceTest {

   private val actionPlanTaskService: ActionPlanTaskService = mockk()
   private val healthPlanTaskTemplateService: HealthPlanTaskTemplateService = mockk()
   private val healthProfessionalService: HealthProfessionalService = mockk()

   val service = MemberOnboardingConclusionService(
      actionPlanTaskService,
      healthPlanTaskTemplateService,
      healthProfessionalService
   )

   private val referencedLink = MemberOnboardingReferencedLink(
      id = RangeUUID.generate(),
      model = MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK
   )
   private val memberOnboarding = TestModelFactory.buildMemberOnboarding(
      referencedLinks = listOf(referencedLink)
   )

   @Test
   fun `#getTasks should return a pair of ActionPlanTask and empty list of HealthPlanTaskTemplate`() = runBlocking {
      val actionPlanTask = TestModelFactory.buildActionPlanTask(id = referencedLink.id)

      coEvery {
         actionPlanTaskService.getTasksByFilters(filters = ActionPlanTaskFilters(taskIds = listOf(referencedLink.id)))
      } returns listOf(actionPlanTask).success()

      val expected = (listOf(actionPlanTask) to emptyList<HealthPlanTaskTemplate>())

      val result = service.getTasks(memberOnboarding)

      assertThat(result).isSuccessWithData(expected)

      coVerifyOnce { actionPlanTaskService.getTasksByFilters(any()) }
      coVerifyNone { healthPlanTaskTemplateService.findByIds(any()) }
   }

   @Test
   fun `#getTasks should return a pair of HealthPlanTaskTemplate and empty list of ActionPlanTask`() = runBlocking {
      val referencedLink = referencedLink.copy(model = MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK_TEMPLATE)
      val memberOnboarding = memberOnboarding.copy(referencedLinks = listOf(referencedLink))
      val healthPlanTaskTemplate = TestModelFactory.buildHealthPlanTaskTemplate(id = referencedLink.id)

      coEvery {
         healthPlanTaskTemplateService.findByIds(listOf(referencedLink.id))
      } returns listOf(healthPlanTaskTemplate).success()

      val expected = (emptyList<ActionPlanTask>() to listOf(healthPlanTaskTemplate))

      val result = service.getTasks(memberOnboarding)

      assertThat(result).isSuccessWithData(expected)

      coVerifyOnce { healthPlanTaskTemplateService.findByIds(any()) }
      coVerifyNone { actionPlanTaskService.getTasksByFilters(any()) }
   }

   @Test
   fun `#getPhysicianStaff should return PhysicianStaff`() = runBlocking {
      val healthProfessional = TestModelFactory.buildHealthProfessional(
         council = Council(number = "0001", state = State.SP, type = CouncilType.CRM)
      )

      val physicianStaff = PhysicianStaff(
         name = healthProfessional.name,
         council = "CRM 0001-SP",
         profileImageUrl = healthProfessional.imageUrl
      )

      coEvery {
         healthProfessionalService.findByStaffId(healthProfessional.id)
      } returns healthProfessional.success()

      val result = service.getPhysicianStaff(healthProfessional.id)

      assertThat(result).isSuccessWithData(physicianStaff)

      coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
   }
}
