package br.com.alice.member.api.builders

import br.com.alice.app.content.model.BackgroundColor
import br.com.alice.app.content.model.CalloutVariant
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.ScreenType
import br.com.alice.app.content.model.Tag
import br.com.alice.app.content.model.TagColorScheme
import br.com.alice.app.content.model.section.CalloutSection
import br.com.alice.common.RangeUUID
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.common.models.Gender
import br.com.alice.coverage.br.com.alice.coverage.converter.HealthProfessionalConsolidatedLightConverter.toConsolidatedType
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AvailableDay
import br.com.alice.data.layer.models.ContactWeekday
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.ModalityType
import br.com.alice.data.layer.models.PhoneNumber
import br.com.alice.data.layer.models.PhoneType
import br.com.alice.data.layer.models.Qualification
import br.com.alice.data.layer.models.RatingDetail
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.accreditedNetwork.AccreditedNetworkDetailsSection
import br.com.alice.member.api.models.accreditedNetwork.ServiceLocation
import br.com.alice.member.api.models.accreditedNetwork.SpecialistDetailsTransport
import br.com.alice.member.api.models.accreditedNetwork.SpecialistSchedulingInformationTransport
import br.com.alice.member.api.models.appContent.RemoteAction
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals

class AccreditedNetworkSpecialistResponseBuilderTest {
    private val appVersion = SemanticVersion("4.0.0")

    companion object {
        const val SPECIALIST_PLACEHOLDER_URL =
            "https://alice-member-app-assets.s3.amazonaws.com/placeholder/providers/Professional.svg"

        private val ROOT_NODE_ID = RangeUUID.generate()

        private val PRE_TRIAGE_SCHEDULE_NAVIGATION = ServiceLocation.ScheduleNavigation(
            mobileRoute = MobileRouting.CHESHIRE_SCREEN,
            properties = mapOf(
                "action" to RemoteAction(
                    method = RemoteActionMethod.POST,
                    endpoint = ServiceConfig.url("/bud/start/${ROOT_NODE_ID}"),
                    params = mapOf("screen_id" to ScreenType.SCREENING_SCHEDULE_REDIRECT)
                )
            )
        )
    }

    private val latitude = "-12.***************"
    private val longitude = "-38.**************"
    private val specialistEducation = "Educação"
    private val address = TestModelFactory.buildStructuredAddress(
        latitude = latitude,
        longitude = longitude
    )
    private val remoteContact = TestModelFactory.buildContact()
    private val person = TestModelFactory.buildPerson()
    private val member = TestModelFactory.buildMember()
    private val product = TestModelFactory.buildProduct()
    private val specialty = TestModelFactory.buildMedicalSpecialty(name = "Specialty")
    private val subspecialties = listOf(
        TestModelFactory.buildMedicalSpecialty(name = "Subspecialty 1"),
        TestModelFactory.buildMedicalSpecialty(name = "Subspecialty 2")
    )

    @TestInstance(TestInstance.Lifecycle.PER_CLASS)
    @Nested
    inner class TestBuildSpecialistResponse {

        @Test
        fun `Should return specialist as SpecialistDetailsTransport`(): Unit = runBlocking {
            val presentialContact = TestModelFactory.buildContact(
                modality = ModalityType.PRESENTIAL,
                addressId = address.id
            ).copy(
                address = address,
                availableDays = listOf(
                    AvailableDay(weekDay = ContactWeekday.MONDAY),
                    AvailableDay(weekDay = ContactWeekday.WEDNESDAY),
                    AvailableDay(weekDay = ContactWeekday.FRIDAY)
                ),
                scheduleAvailabilityDays = 1,
                website = "http://pudim.com.br/",
                phones = listOf(
                    PhoneNumber(phone = "71999999999", type = PhoneType.PHONE),
                    PhoneNumber(phone = "(71)99999-9998", type = PhoneType.WHATSAPP)
                )
            )
            val specialist = TestModelFactory.buildHealthProfessional(
                gender = Gender.MALE,
                education = listOf(specialistEducation),
                qualifications = listOf(Qualification.ISO_9001, Qualification.JOINT_COMMISSION_INTERNATIONAL),
                appointmentTypes = listOf(
                    SpecialistAppointmentType.REMOTE,
                    SpecialistAppointmentType.PRESENTIAL
                ),
                addressesStructured = listOf(address),
                imageUrl = "http://pudim.com.br/"
            ).copy(contacts = listOf(remoteContact, presentialContact))
            val consolidatedRating = TestModelFactory.buildConsolidatedRating(
                ratingDetail = RatingDetail(
                    appointmentCount = 300,
                    evaluationsCount = 30,
                    trustScore = 0.89,
                    recommendationScore = 0.92
                )
            )

            val result = AccreditedNetworkSpecialistResponseBuilder.buildSpecialistResponse(
                person = person,
                member = member,
                product = product,
                specialist = specialist,
                favorite = null,
                consolidatedRating = consolidatedRating,
                specialty = specialty,
                subSpecialties = subspecialties,
                memberLat = latitude.toDouble(),
                memberLng = longitude.toDouble(),
                schedulingInfo = SpecialistSchedulingInformationTransport(
                    appointmentType = SpecialistAppointmentType.REMOTE,
                    schedulingUrl = "http://pudim.com.br/",
                    nextAvailableDate = LocalDateTime.of(2024, 7, 12, 9, 30)
                ),
                serviceDays = listOf("seg"),
                callout = CalloutSection(
                    title = "Subespecialidade incorreta",
                    calloutBody = "Não perca viagem! Esse profissional pode não atender a demanda específica do seu encaminhamento.",
                    calloutVariant = CalloutVariant.TUTORIAL,
                ),
                appVersion = appVersion
            )

            val expectedSpecialistDetailsTransport = SpecialistDetailsTransport(
                id = specialist.id,
                providerType = specialist.type.toConsolidatedType(),
                title = specialty.name,
                name = specialist.name,
                imageUrl = specialist.imageUrl ?: SPECIALIST_PLACEHOLDER_URL,
                crm = specialist.council.toString(),
                favoriteInfo = FavoriteInfoTransportBuilder.build(
                    referenceId = specialist.id,
                    specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                    referenceType = specialist.type.toConsolidatedType(),
                    isFavorite = false,
                    appVersion = appVersion,
                ),
                subspecialties = listOf("Subspecialty 1", "Subspecialty 2"),
                rating = SpecialistDetailsTransport.SpecialistRating(
                    backgroundColor = BackgroundColor.BACKGROUND_DEFAULT,
                    aiGeneratedComment = "comment",
                    appointmentCount = 300,
                    evaluationsCount = 30,
                    trustScore = 0.89,
                    recommendationScore = 0.92
                ),
                services = listOf(
                    ServiceLocation(
                        title = "Por vídeo chamada",
                        details = "Mais rápido",
                        nextDateAvailable = "2024-07-12T09:30",
                        serviceDays = listOf("seg"),
                        scheduleNavigation = ServiceLocation.ScheduleNavigation(
                            mobileRoute = MobileRouting.WEBVIEW,
                            properties = mapOf(
                                "link" to "http://pudim.com.br/",
                                "pop_on_complete" to true,
                                "feedback_message" to "Agendamento efetuado com sucesso!",
                                "token" to "true",
                            )
                        )
                    ),
                    ServiceLocation(
                        title = "Presencial",
                        details = "0 m de você",
                        address = address.formattedAddress(),
                        serviceDays = listOf("seg", "qua", "sex"),
                        tag = Tag(
                            icon = "calendar",
                            text = "Previsão de agenda: 1 dia",
                            colorScheme = TagColorScheme.GRAY
                        ),
                        contacts = listOf(
                            ServiceLocation.ServiceContact(
                                text = "(071) 99999-9999",
                                url = "tel:************",
                                type = ServiceLocation.ServiceContactType.PHONE
                            ),
                            ServiceLocation.ServiceContact(
                                text = "(071) 99999-9998",
                                url = "https://wa.me/55***********",
                                type = ServiceLocation.ServiceContactType.WHATSAPP
                            ),
                            ServiceLocation.ServiceContact(
                                text = "http://pudim.com.br/",
                                url = "http://pudim.com.br/",
                                type = ServiceLocation.ServiceContactType.WEBSITE
                            )
                        ),
                    )
                ),
                membershipCard = MembershipCardBuilder.buildAliceMembershipCard(person, product),
                sections = listOf(
                    AccreditedNetworkDetailsSection(
                        title = "Biografia",
                        icon = "user",
                        content = listOf(
                            AccreditedNetworkDetailsSection.Content(
                                text = specialist.profileBio.orEmpty()
                            )
                        )
                    ),
                    AccreditedNetworkDetailsSection(
                        title = "Educação",
                        icon = "paper",
                        content = listOf(
                            AccreditedNetworkDetailsSection.Content(
                                text = specialistEducation
                            )
                        )
                    ),
                    AccreditedNetworkDetailsSection(
                        title = "Certificações e qualificações",
                        icon = "script",
                        content = listOf(
                            AccreditedNetworkDetailsSection.Content(
                                text = "${Qualification.ISO_9001.description}, ${Qualification.JOINT_COMMISSION_INTERNATIONAL.description}"
                            )
                        ),
                        isExpandable = false,
                    )
                ),
                callout = CalloutSection(
                    title = "Subespecialidade incorreta",
                    calloutBody = "Não perca viagem! Esse profissional pode não atender a demanda específica do seu encaminhamento.",
                    calloutVariant = CalloutVariant.TUTORIAL,
                ),
                showMembershipCardShareButton = false
            )

            assertThat(result).isEqualTo(expectedSpecialistDetailsTransport)
        }

        @Test
        fun `Should return specialist as SpecialistDetailsTransport with not enough rating evaluations`() =
            runBlocking {
                withFeatureFlag(
                    namespace = FeatureNamespace.ALICE_APP,
                    key = "specialist_details_service_location_tag_color",
                    value = "green"
                ) {
                    val presentialContact = TestModelFactory.buildContact(
                        modality = ModalityType.PRESENTIAL,
                        addressId = address.id
                    ).copy(
                        address = address,
                        availableDays = listOf(
                            AvailableDay(weekDay = ContactWeekday.MONDAY),
                            AvailableDay(weekDay = ContactWeekday.WEDNESDAY),
                            AvailableDay(weekDay = ContactWeekday.FRIDAY)
                        ),
                        scheduleAvailabilityDays = 0,
                        website = "http://pudim.com.br/",
                        phones = listOf(
                            PhoneNumber(phone = "71999999999", type = PhoneType.PHONE),
                            PhoneNumber(phone = "***********", type = PhoneType.WHATSAPP)
                        )
                    )
                    val specialist = TestModelFactory.buildHealthProfessional(
                        gender = Gender.MALE,
                        education = listOf(specialistEducation),
                        qualifications = listOf(Qualification.ISO_9001, Qualification.JOINT_COMMISSION_INTERNATIONAL),
                        appointmentTypes = listOf(
                            SpecialistAppointmentType.REMOTE,
                            SpecialistAppointmentType.PRESENTIAL
                        ),
                        addressesStructured = listOf(address),
                        imageUrl = "http://pudim.com.br/"
                    ).copy(contacts = listOf(remoteContact, presentialContact))
                    val consolidatedRating = TestModelFactory.buildConsolidatedRating(
                        ratingDetail = RatingDetail(
                            appointmentCount = 300,
                            evaluationsCount = 3,
                            trustScore = 0.89,
                            recommendationScore = 0.92
                        )
                    )

                    val result = AccreditedNetworkSpecialistResponseBuilder.buildSpecialistResponse(
                        person = person,
                        member = member,
                        product = product,
                        specialist = specialist,
                        favorite = TestModelFactory.buildAccreditedNetworkFavorite(),
                        consolidatedRating = consolidatedRating,
                        memberLat = latitude.toDouble(),
                        memberLng = longitude.toDouble(),
                        schedulingInfo = null,
                        serviceDays = emptyList(),
                        appVersion = appVersion,
                    )

                    val expectedSpecialistDetailsTransport = SpecialistDetailsTransport(
                        id = specialist.id,
                        providerType = specialist.type.toConsolidatedType(),
                        title = specialist.type.toConsolidatedType().title,
                        name = specialist.name,
                        imageUrl = specialist.imageUrl ?: SPECIALIST_PLACEHOLDER_URL,
                        crm = specialist.council.toString(),
                        favoriteInfo = FavoriteInfoTransportBuilder.build(
                            referenceId = specialist.id,
                            specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                            referenceType = specialist.type.toConsolidatedType(),
                            isFavorite = true,
                            appVersion = appVersion,
                        ),
                        emptyRating = SpecialistDetailsTransport.EmptySpecialistRating(
                            value = "Faltam **7 avaliações** dos membros para disponibilizar os dados",
                            backgroundColor = BackgroundColor.BACKGROUND_DEFAULT
                        ),
                        services = listOf(
                            ServiceLocation(
                                title = "Por vídeo chamada",
                                details = "Mais rápido"
                            ),
                            ServiceLocation(
                                title = "Presencial",
                                details = "0 m de você",
                                address = address.formattedAddress(),
                                serviceDays = listOf("seg", "qua", "sex"),
                                contacts = listOf(
                                    ServiceLocation.ServiceContact(
                                        text = "(071) 99999-9999",
                                        url = "tel:************",
                                        type = ServiceLocation.ServiceContactType.PHONE
                                    ),
                                    ServiceLocation.ServiceContact(
                                        text = "(071) 99999-9998",
                                        url = "https://wa.me/55***********",
                                        type = ServiceLocation.ServiceContactType.WHATSAPP
                                    ),
                                    ServiceLocation.ServiceContact(
                                        text = "http://pudim.com.br/",
                                        url = "http://pudim.com.br/",
                                        type = ServiceLocation.ServiceContactType.WEBSITE
                                    )
                                ),
                            )
                        ),
                        membershipCard = MembershipCardBuilder.buildAliceMembershipCard(person, product),
                        sections = listOf(
                            AccreditedNetworkDetailsSection(
                                title = "Biografia",
                                icon = "user",
                                content = listOf(
                                    AccreditedNetworkDetailsSection.Content(
                                        text = specialist.profileBio.orEmpty()
                                    )
                                )
                            ),
                            AccreditedNetworkDetailsSection(
                                title = "Educação",
                                icon = "paper",
                                content = listOf(
                                    AccreditedNetworkDetailsSection.Content(
                                        text = specialistEducation
                                    )
                                )
                            ),
                            AccreditedNetworkDetailsSection(
                                title = "Certificações e qualificações",
                                icon = "script",
                                content = listOf(
                                    AccreditedNetworkDetailsSection.Content(
                                        text = "${Qualification.ISO_9001.description}, ${Qualification.JOINT_COMMISSION_INTERNATIONAL.description}"
                                    )
                                ),
                                isExpandable = false
                            )
                        ),
                        showMembershipCardShareButton = false
                    )

                    assertEquals(expectedSpecialistDetailsTransport, result)
                }
            }

        @Test
        fun `Should return specialist as SpecialistDetailsTransport missing only one evaluation`() {
            val presentialContact = TestModelFactory.buildContact(
                modality = ModalityType.PRESENTIAL,
                addressId = address.id
            ).copy(
                address = address,
                availableDays = listOf(
                    AvailableDay(weekDay = ContactWeekday.MONDAY),
                    AvailableDay(weekDay = ContactWeekday.WEDNESDAY),
                    AvailableDay(weekDay = ContactWeekday.FRIDAY)
                ),
                scheduleAvailabilityDays = 3,
                website = "http://pudim.com.br/",
                phones = listOf(
                    PhoneNumber(phone = "71999999999", type = PhoneType.PHONE),
                    PhoneNumber(phone = "***********", type = PhoneType.WHATSAPP)
                )
            )
            val specialist = TestModelFactory.buildHealthProfessional(
                gender = Gender.MALE,
                education = listOf(specialistEducation),
                qualifications = listOf(Qualification.ISO_9001, Qualification.JOINT_COMMISSION_INTERNATIONAL),
                appointmentTypes = listOf(
                    SpecialistAppointmentType.REMOTE,
                    SpecialistAppointmentType.PRESENTIAL
                ),
                addressesStructured = listOf(address),
                imageUrl = "http://pudim.com.br/"
            ).copy(contacts = listOf(remoteContact, presentialContact))
            val consolidatedRating = TestModelFactory.buildConsolidatedRating(
                ratingDetail = RatingDetail(
                    appointmentCount = 300,
                    evaluationsCount = 9,
                    trustScore = 0.89,
                    recommendationScore = 0.92
                )
            )
            val subspecialties = listOf(
                TestModelFactory.buildMedicalSpecialty(name = "Subspecialty 1"),
                TestModelFactory.buildMedicalSpecialty(name = "Subspecialty 2")
            )

            val result = AccreditedNetworkSpecialistResponseBuilder.buildSpecialistResponse(
                person = person,
                member = member,
                product = product,
                specialist = specialist,
                favorite = null,
                subSpecialties = subspecialties,
                consolidatedRating = consolidatedRating,
                memberLat = latitude.toDouble(),
                memberLng = longitude.toDouble(),
                schedulingInfo = null,
                serviceDays = emptyList(),
                appVersion = appVersion,
            )

            val expectedSpecialistDetailsTransport = SpecialistDetailsTransport(
                id = specialist.id,
                providerType = specialist.type.toConsolidatedType(),
                title = specialist.type.toConsolidatedType().title,
                name = specialist.name,
                imageUrl = specialist.imageUrl ?: SPECIALIST_PLACEHOLDER_URL,
                crm = specialist.council.toString(),
                favoriteInfo = FavoriteInfoTransportBuilder.build(
                    referenceId = specialist.id,
                    specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                    referenceType = specialist.type.toConsolidatedType(),
                    isFavorite = false,
                    appVersion = appVersion,
                ),
                emptyRating = SpecialistDetailsTransport.EmptySpecialistRating(
                    value = "Falta **1 avaliação** dos membros para disponibilizar os dados",
                    backgroundColor = BackgroundColor.BACKGROUND_DEFAULT
                ),
                subspecialties = listOf("Subspecialty 1", "Subspecialty 2"),
                services = listOf(
                    ServiceLocation(
                        title = "Por vídeo chamada",
                        details = "Mais rápido",
                    ),
                    ServiceLocation(
                        title = "Presencial",
                        details = "0 m de você",
                        address = address.formattedAddress(),
                        serviceDays = listOf("seg", "qua", "sex"),
                        tag = Tag(
                            icon = "calendar",
                            text = "Previsão de agenda: 3 dias",
                            colorScheme = TagColorScheme.GRAY
                        ),
                        contacts = listOf(
                            ServiceLocation.ServiceContact(
                                text = "(071) 99999-9999",
                                url = "tel:************",
                                type = ServiceLocation.ServiceContactType.PHONE
                            ),
                            ServiceLocation.ServiceContact(
                                text = "(071) 99999-9998",
                                url = "https://wa.me/55***********",
                                type = ServiceLocation.ServiceContactType.WHATSAPP
                            ),
                            ServiceLocation.ServiceContact(
                                text = "http://pudim.com.br/",
                                url = "http://pudim.com.br/",
                                type = ServiceLocation.ServiceContactType.WEBSITE
                            )
                        ),
                    )
                ),
                membershipCard = MembershipCardBuilder.buildAliceMembershipCard(person, product),
                sections = listOf(
                    AccreditedNetworkDetailsSection(
                        title = "Biografia",
                        icon = "user",
                        content = listOf(
                            AccreditedNetworkDetailsSection.Content(
                                text = specialist.profileBio.orEmpty()
                            )
                        )
                    ),
                    AccreditedNetworkDetailsSection(
                        title = "Educação",
                        icon = "paper",
                        content = listOf(
                            AccreditedNetworkDetailsSection.Content(
                                text = specialistEducation
                            )
                        )
                    ),
                    AccreditedNetworkDetailsSection(
                        title = "Certificações e qualificações",
                        icon = "script",
                        content = listOf(
                            AccreditedNetworkDetailsSection.Content(
                                text = "${Qualification.ISO_9001.description}, ${Qualification.JOINT_COMMISSION_INTERNATIONAL.description}"
                            )
                        ),
                        isExpandable = false,
                    )
                ),
                showMembershipCardShareButton = false,
            )

            assertThat(result).isEqualTo(expectedSpecialistDetailsTransport)
        }

        @Test
        fun `Should return specialist as SpecialistDetailsTransport with empty fields`() {
            val presentialContact = TestModelFactory.buildContact(
                modality = ModalityType.PRESENTIAL,
                addressId = address.id
            ).copy(
                address = address,
                availableDays = listOf(
                    AvailableDay(weekDay = ContactWeekday.MONDAY),
                    AvailableDay(weekDay = ContactWeekday.WEDNESDAY),
                    AvailableDay(weekDay = ContactWeekday.FRIDAY)
                ),
                scheduleAvailabilityDays = 3,
                website = "http://pudim.com.br/",
                phones = listOf(
                    PhoneNumber(phone = "71999999999", type = PhoneType.PHONE),
                    PhoneNumber(phone = "***********", type = PhoneType.WHATSAPP)
                )
            )
            val specialist = TestModelFactory.buildHealthProfessional(
                gender = Gender.MALE,
                education = listOf(specialistEducation),
                profileBio = null,
                qualifications = listOf(Qualification.ISO_9001, Qualification.JOINT_COMMISSION_INTERNATIONAL),
                appointmentTypes = listOf(
                    SpecialistAppointmentType.REMOTE,
                    SpecialistAppointmentType.PRESENTIAL
                ),
                addressesStructured = listOf(address),
                imageUrl = "http://pudim.com.br/"
            ).copy(contacts = listOf(remoteContact, presentialContact))

            val result = AccreditedNetworkSpecialistResponseBuilder.buildSpecialistResponse(
                person = person,
                member = member,
                product = product,
                specialist = specialist,
                favorite = null,
                consolidatedRating = null,
                schedulingInfo = null,
                serviceDays = emptyList(),
                appVersion = appVersion,
            )

            val expectedSpecialistDetailsTransport = SpecialistDetailsTransport(
                id = specialist.id,
                providerType = specialist.type.toConsolidatedType(),
                title = specialist.type.toConsolidatedType().title,
                name = specialist.name,
                imageUrl = specialist.imageUrl ?: SPECIALIST_PLACEHOLDER_URL,
                crm = specialist.council.toString(),
                favoriteInfo = FavoriteInfoTransportBuilder.build(
                    referenceId = specialist.id,
                    specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                    referenceType = specialist.type.toConsolidatedType(),
                    isFavorite = false,
                    appVersion = appVersion,
                ),
                services = listOf(
                    ServiceLocation(
                        title = "Por vídeo chamada",
                        details = "Mais rápido"
                    ),
                    ServiceLocation(
                        title = "Presencial",
                        details = "",
                        address = address.formattedAddress(),
                        serviceDays = listOf("seg", "qua", "sex"),
                        tag = Tag(
                            icon = "calendar",
                            text = "Previsão de agenda: 3 dias",
                            colorScheme = TagColorScheme.GRAY
                        ),
                        contacts = listOf(
                            ServiceLocation.ServiceContact(
                                text = "(071) 99999-9999",
                                url = "tel:************",
                                type = ServiceLocation.ServiceContactType.PHONE
                            ),
                            ServiceLocation.ServiceContact(
                                text = "(071) 99999-9998",
                                url = "https://wa.me/55***********",
                                type = ServiceLocation.ServiceContactType.WHATSAPP
                            ),
                            ServiceLocation.ServiceContact(
                                text = "http://pudim.com.br/",
                                url = "http://pudim.com.br/",
                                type = ServiceLocation.ServiceContactType.WEBSITE
                            )
                        ),
                    )
                ),
                membershipCard = MembershipCardBuilder.buildAliceMembershipCard(person, product),
                sections = listOf(
                    AccreditedNetworkDetailsSection(
                        title = "Educação",
                        icon = "paper",
                        content = listOf(
                            AccreditedNetworkDetailsSection.Content(
                                text = specialistEducation
                            )
                        )
                    ),
                    AccreditedNetworkDetailsSection(
                        title = "Certificações e qualificações",
                        icon = "script",
                        content = listOf(
                            AccreditedNetworkDetailsSection.Content(
                                text = "${Qualification.ISO_9001.description}, ${Qualification.JOINT_COMMISSION_INTERNATIONAL.description}"
                            )
                        ),
                        isExpandable = false,
                    )
                ),
                showMembershipCardShareButton = false,
            )

            assertThat(result).isEqualTo(expectedSpecialistDetailsTransport)
        }
    }

    @Test
    fun `Should return member healthcare team physician as SpecialistDetailsTransport`() {
        val latitude = "-12.***************"
        val longitude = "-38.**************"
        val specialistEducation = "Educação"
        val address = TestModelFactory.buildStructuredAddress(
            latitude = latitude,
            longitude = longitude
        )
        val remoteContact = TestModelFactory.buildContact(
            website = "http://pudim.com.br/"
        )
        val presentialContact = TestModelFactory.buildContact(
            modality = ModalityType.PRESENTIAL,
            addressId = address.id

        ).copy(
            address = address,
            availableDays = listOf(
                AvailableDay(weekDay = ContactWeekday.MONDAY),
                AvailableDay(weekDay = ContactWeekday.WEDNESDAY),
                AvailableDay(weekDay = ContactWeekday.FRIDAY)
            ),
            scheduleAvailabilityDays = 3,
            website = "http://pudim.com.br/",
            phones = listOf(
                PhoneNumber(phone = "71999999999", type = PhoneType.PHONE),
                PhoneNumber(phone = "***********", type = PhoneType.WHATSAPP)
            )
        )
        val specialist = TestModelFactory.buildHealthProfessional(
            gender = Gender.MALE,
            education = listOf(specialistEducation),
            qualifications = listOf(Qualification.ISO_9001, Qualification.JOINT_COMMISSION_INTERNATIONAL),
            appointmentTypes = listOf(
                SpecialistAppointmentType.REMOTE,
                SpecialistAppointmentType.PRESENTIAL
            ),
            addressesStructured = listOf(address),
        ).copy(contacts = listOf(remoteContact, presentialContact))
        val memberHealthcareTeam = TestModelFactory.buildHealthcareTeam(
            segment = HealthcareTeam.Segment.DEFAULT,
            physicianStaffId = specialist.staffId
        )
        val schedulingUrl = "https://www.alice.com.br"
        val nextAvailableDate = LocalDateTime.of(2024, 7, 12, 9, 30)
        val serviceDays = listOf("seg", "qua", "sex")

        val result = AccreditedNetworkSpecialistResponseBuilder.buildHealthCareTeamSpecialistResponse(
            specialist = specialist,
            rootNodeId = ROOT_NODE_ID,
            memberHealthcareTeam = memberHealthcareTeam,
            person = person,
            schedulingInfo = SpecialistSchedulingInformationTransport(
                appointmentType = SpecialistAppointmentType.REMOTE,
                schedulingUrl = schedulingUrl,
                nextAvailableDate = nextAvailableDate,
            ),
            serviceDays = serviceDays,
            specialty = specialty,
            memberLat = latitude.toDouble(),
            memberLng = longitude.toDouble(),
            appVersion = appVersion,
        )

        val expectedSpecialistDetailsTransport = SpecialistDetailsTransport(
            id = specialist.id,
            providerType = specialist.type.toConsolidatedType(),
            title = specialty.name,
            name = specialist.name,
            imageUrl = specialist.imageUrl ?: SPECIALIST_PLACEHOLDER_URL,
            crm = specialist.council.toString(),
            favoriteInfo = FavoriteInfoTransportBuilder.build(
                referenceId = specialist.id,
                specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                referenceType = specialist.type.toConsolidatedType(),
                isFavorite = false,
                appVersion = appVersion,
            ),
            tag = Tag(
                text = "Seu Médico de Família",
                colorScheme = TagColorScheme.MAGENTA
            ),
            services = listOf(
                ServiceLocation(
                    title = "Agendar",
                    details = "",
                    nextDateAvailable = nextAvailableDate.toString(),
                    scheduleNavigation = PRE_TRIAGE_SCHEDULE_NAVIGATION,
                ),
                ServiceLocation(
                    title = "Por vídeo chamada",
                    details = "Mais rápido",
                    serviceDays = serviceDays,
                    contacts = listOf(
                        ServiceLocation.ServiceContact(
                            text = "http://pudim.com.br/",
                            url = "http://pudim.com.br/",
                            type = ServiceLocation.ServiceContactType.WEBSITE
                        )
                    )
                ),
                ServiceLocation(
                    title = "Presencial",
                    details = "0 m de você",
                    address = address.formattedAddress(),
                    serviceDays = listOf("seg", "qua", "sex"),
                    tag = Tag(
                        icon = "calendar",
                        text = "Previsão de agenda: 3 dias",
                        colorScheme = TagColorScheme.GRAY
                    ),
                    contacts = listOf(
                        ServiceLocation.ServiceContact(
                            text = "(071) 99999-9999",
                            url = "tel:************",
                            type = ServiceLocation.ServiceContactType.PHONE
                        ),
                        ServiceLocation.ServiceContact(
                            text = "(071) 99999-9998",
                            url = "https://wa.me/55***********",
                            type = ServiceLocation.ServiceContactType.WHATSAPP
                        ),
                        ServiceLocation.ServiceContact(
                            text = "http://pudim.com.br/",
                            url = "http://pudim.com.br/",
                            type = ServiceLocation.ServiceContactType.WEBSITE
                        )

                    ),
                    nextDateAvailable = null,
                    scheduleNavigation = null
                )
            ),
            sections = listOf(
                AccreditedNetworkDetailsSection(
                    title = "Biografia",
                    icon = "user",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = specialist.profileBio.orEmpty()
                        )
                    )
                ),
                AccreditedNetworkDetailsSection(
                    title = "Educação",
                    icon = "paper",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = specialistEducation
                        )
                    )
                ),
                AccreditedNetworkDetailsSection(
                    title = "Certificações e qualificações",
                    icon = "script",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = "${Qualification.ISO_9001.description}, ${Qualification.JOINT_COMMISSION_INTERNATIONAL.description}"
                        )
                    ),
                    isExpandable = false
                )
            ),
            action = null,
            showMembershipCardShareButton = false
        )

        assertThat(result).isEqualTo(expectedSpecialistDetailsTransport)
    }

    @Test
    fun `Should return member healthcare team physician as SpecialistDetailsTransport with empty fields`() {
        val presentialContact = TestModelFactory.buildContact(
            modality = ModalityType.PRESENTIAL,
            addressId = address.id
        ).copy(
            address = address,
            availableDays = listOf(
                AvailableDay(weekDay = ContactWeekday.MONDAY),
                AvailableDay(weekDay = ContactWeekday.WEDNESDAY),
                AvailableDay(weekDay = ContactWeekday.FRIDAY)
            ),
            scheduleAvailabilityDays = 3,
            website = "http://pudim.com.br/",
            phones = listOf(
                PhoneNumber(phone = "71999999999", type = PhoneType.PHONE),
                PhoneNumber(phone = "***********", type = PhoneType.WHATSAPP)
            )
        )
        val specialist = TestModelFactory.buildHealthProfessional(
            name = "name",
            imageUrl = null,
            gender = Gender.MALE,
            education = listOf(specialistEducation),
            qualifications = listOf(Qualification.ISO_9001, Qualification.JOINT_COMMISSION_INTERNATIONAL),
            appointmentTypes = listOf(
                SpecialistAppointmentType.REMOTE,
                SpecialistAppointmentType.PRESENTIAL
            ),
            addressesStructured = listOf(address),
        ).copy(contacts = listOf(remoteContact, presentialContact))
        val memberHealthcareTeam = TestModelFactory.buildHealthcareTeam(
            segment = HealthcareTeam.Segment.DEFAULT,
            physicianStaffId = specialist.staffId
        )
        val serviceDays = listOf("seg", "qua", "sex")

        val result = AccreditedNetworkSpecialistResponseBuilder.buildHealthCareTeamSpecialistResponse(
            specialist = specialist,
            rootNodeId = ROOT_NODE_ID,
            memberHealthcareTeam = memberHealthcareTeam,
            person = person,
            schedulingInfo = null,
            serviceDays = serviceDays,
            appVersion = appVersion,
        )

        val expectedSpecialistDetailsTransport = SpecialistDetailsTransport(
            id = specialist.id,
            providerType = specialist.type.toConsolidatedType(),
            title = specialist.type.toConsolidatedType().title,
            name = specialist.name,
            imageUrl = specialist.imageUrl ?: SPECIALIST_PLACEHOLDER_URL,
            crm = specialist.council.toString(),
            favoriteInfo = FavoriteInfoTransportBuilder.build(
                referenceId = specialist.id,
                specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                referenceType = specialist.type.toConsolidatedType(),
                isFavorite = false,
                appVersion = appVersion,
            ),
            tag = Tag(
                text = "Seu Médico de Família",
                colorScheme = TagColorScheme.MAGENTA
            ),
            services = listOf(
                ServiceLocation(
                    title = "Agendar",
                    details = "",
                    scheduleNavigation = PRE_TRIAGE_SCHEDULE_NAVIGATION,
                ),
                ServiceLocation(
                    title = "Por vídeo chamada",
                    details = "Mais rápido",
                    serviceDays = serviceDays,
                    nextDateAvailable = null,
                    scheduleNavigation = null
                ),
                ServiceLocation(
                    title = "Presencial",
                    details = "",
                    address = address.formattedAddress(),
                    serviceDays = listOf("seg", "qua", "sex"),
                    tag = Tag(
                        icon = "calendar",
                        text = "Previsão de agenda: 3 dias",
                        colorScheme = TagColorScheme.GRAY
                    ),
                    contacts = listOf(
                        ServiceLocation.ServiceContact(
                            text = "(071) 99999-9999",
                            url = "tel:************",
                            type = ServiceLocation.ServiceContactType.PHONE
                        ),
                        ServiceLocation.ServiceContact(
                            text = "(071) 99999-9998",
                            url = "https://wa.me/55***********",
                            type = ServiceLocation.ServiceContactType.WHATSAPP
                        ),
                        ServiceLocation.ServiceContact(
                            text = "http://pudim.com.br/",
                            url = "http://pudim.com.br/",
                            type = ServiceLocation.ServiceContactType.WEBSITE
                        )

                    ),
                    nextDateAvailable = null,
                    scheduleNavigation = null
                )
            ),
            sections = listOf(
                AccreditedNetworkDetailsSection(
                    title = "Biografia",
                    icon = "user",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = specialist.profileBio.orEmpty()
                        )
                    )
                ),
                AccreditedNetworkDetailsSection(
                    title = "Educação",
                    icon = "paper",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = specialistEducation
                        )
                    )
                ),
                AccreditedNetworkDetailsSection(
                    title = "Certificações e qualificações",
                    icon = "script",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = "${Qualification.ISO_9001.description}, ${Qualification.JOINT_COMMISSION_INTERNATIONAL.description}"
                        )
                    ),
                    isExpandable = false
                )
            ),
            action = null,
            showMembershipCardShareButton = false,
        )

        assertThat(result).isEqualTo(expectedSpecialistDetailsTransport)
    }

    @Test
    fun `Should return not member healthcare team physician as SpecialistDetailsTransport`() {
        val presentialContact = TestModelFactory.buildContact(
            modality = ModalityType.PRESENTIAL,
            addressId = address.id

        ).copy(address = address)
        val specialist = TestModelFactory.buildHealthProfessional(
            gender = Gender.MALE,
            education = listOf(specialistEducation),
            qualifications = listOf(Qualification.ISO_9001, Qualification.JOINT_COMMISSION_INTERNATIONAL),
            appointmentTypes = listOf(
                SpecialistAppointmentType.REMOTE,
                SpecialistAppointmentType.PRESENTIAL
            ),
            addressesStructured = listOf(address)
        ).copy(contacts = listOf(remoteContact, presentialContact))
        val memberHealthCareTeamPhysician = TestModelFactory.buildHealthProfessional()
        val memberHealthcareTeam = TestModelFactory.buildHealthcareTeam(
            segment = HealthcareTeam.Segment.DEFAULT,
            physicianStaffId = memberHealthCareTeamPhysician.staffId
        )

        val result = AccreditedNetworkSpecialistResponseBuilder.buildHealthCareTeamSpecialistResponse(
            specialist = specialist,
            rootNodeId = ROOT_NODE_ID,
            memberHealthcareTeam = memberHealthcareTeam,
            person = person,
            schedulingInfo = null,
            serviceDays = emptyList(),
            memberLat = latitude.toDouble(),
            memberLng = longitude.toDouble(),
            appVersion = appVersion
        )

        val expectedSpecialistDetailsTransport = SpecialistDetailsTransport(
            id = specialist.id,
            providerType = specialist.type.toConsolidatedType(),
            title = specialist.type.toConsolidatedType().title,
            name = specialist.name,
            imageUrl = specialist.imageUrl ?: SPECIALIST_PLACEHOLDER_URL,
            crm = specialist.council.toString(),
            favoriteInfo = FavoriteInfoTransportBuilder.build(
                referenceId = specialist.id,
                specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                referenceType = specialist.type.toConsolidatedType(),
                isFavorite = false,
                appVersion = appVersion,
            ),
            services = listOf(
                ServiceLocation(
                    title = "Agendar",
                    details = "",
                    scheduleNavigation = PRE_TRIAGE_SCHEDULE_NAVIGATION,
                ),
                ServiceLocation(
                    title = "Por vídeo chamada",
                    details = "Mais rápido"
                ),
                ServiceLocation(
                    title = "Presencial",
                    details = "0 m de você",
                    address = address.formattedAddress(),
                    serviceDays = emptyList(),
                    nextDateAvailable = null,
                    scheduleNavigation = null
                )
            ),
            sections = listOf(
                AccreditedNetworkDetailsSection(
                    title = "Biografia",
                    icon = "user",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = specialist.profileBio.orEmpty()
                        )
                    )
                ),
                AccreditedNetworkDetailsSection(
                    title = "Educação",
                    icon = "paper",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = specialistEducation
                        )
                    )
                ),
                AccreditedNetworkDetailsSection(
                    title = "Certificações e qualificações",
                    icon = "script",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = "${Qualification.ISO_9001.description}, ${Qualification.JOINT_COMMISSION_INTERNATIONAL.description}"
                        )
                    ),
                    isExpandable = false
                )
            ),
            action = SpecialistDetailsTransport.Action(
                label = "Ver seu médico de família",
                navigation = NavigationResponse(
                    mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                    properties = mapOf(
                        "method" to RemoteActionMethod.GET,
                        "endpoint" to "/accredited_network/healthcare_team/physician/${memberHealthCareTeamPhysician.staffId}?lat=$latitude&lng=$longitude"
                    ),
                )
            ),
            showMembershipCardShareButton = false
        )

        assertThat(result).isEqualTo(expectedSpecialistDetailsTransport)
    }

    @Test
    fun `Should return not member healthcare team physician as SpecialistDetailsTransport with action to profile module`() =
        runBlocking {
            withFeatureFlags(
                FeatureNamespace.ALICE_APP,
                mapOf(
                    "app_version_with_accredited_network_profile" to "4.37.0",
                ),
            ) {
                val presentialContact = TestModelFactory.buildContact(
                    modality = ModalityType.PRESENTIAL,
                    addressId = address.id

                ).copy(address = address)
                val specialist = TestModelFactory.buildHealthProfessional(
                    gender = Gender.MALE,
                    education = listOf(specialistEducation),
                    qualifications = listOf(Qualification.ISO_9001, Qualification.JOINT_COMMISSION_INTERNATIONAL),
                    appointmentTypes = listOf(
                        SpecialistAppointmentType.REMOTE,
                        SpecialistAppointmentType.PRESENTIAL
                    ),
                    addressesStructured = listOf(address)
                ).copy(contacts = listOf(remoteContact, presentialContact))
                val memberHealthCareTeamPhysician = TestModelFactory.buildHealthProfessional()
                val memberHealthcareTeam = TestModelFactory.buildHealthcareTeam(
                    segment = HealthcareTeam.Segment.DEFAULT,
                    physicianStaffId = memberHealthCareTeamPhysician.staffId
                )

                val appVersion = SemanticVersion("4.37.0")
                val result = AccreditedNetworkSpecialistResponseBuilder.buildHealthCareTeamSpecialistResponse(
                    specialist = specialist,
                    rootNodeId = ROOT_NODE_ID,
                    memberHealthcareTeam = memberHealthcareTeam,
                    person = person,
                    schedulingInfo = null,
                    serviceDays = emptyList(),
                    memberLat = latitude.toDouble(),
                    memberLng = longitude.toDouble(),
                    appVersion = appVersion
                )

                val expectedSpecialistDetailsTransport = SpecialistDetailsTransport(
                    id = specialist.id,
                    providerType = specialist.type.toConsolidatedType(),
                    title = specialist.type.toConsolidatedType().title,
                    name = specialist.name,
                    imageUrl = specialist.imageUrl ?: SPECIALIST_PLACEHOLDER_URL,
                    crm = specialist.council.toString(),
                    favoriteInfo = null,
                    services = listOf(
                        ServiceLocation(
                            title = "Agendar",
                            details = "",
                            scheduleNavigation = PRE_TRIAGE_SCHEDULE_NAVIGATION,
                        ),
                        ServiceLocation(
                            title = "Por vídeo chamada",
                            details = "Mais rápido"
                        ),
                        ServiceLocation(
                            title = "Presencial",
                            details = "0 m de você",
                            address = address.formattedAddress(),
                            serviceDays = emptyList(),
                            nextDateAvailable = null,
                            scheduleNavigation = null
                        )
                    ),
                    sections = listOf(
                        AccreditedNetworkDetailsSection(
                            title = "Biografia",
                            icon = "user",
                            content = listOf(
                                AccreditedNetworkDetailsSection.Content(
                                    text = specialist.profileBio.orEmpty()
                                )
                            )
                        ),
                        AccreditedNetworkDetailsSection(
                            title = "Educação",
                            icon = "education",
                            content = listOf(
                                AccreditedNetworkDetailsSection.Content(
                                    text = specialistEducation
                                )
                            )
                        ),
                        AccreditedNetworkDetailsSection(
                            title = "Certificações e qualificações",
                            icon = "script",
                            content = listOf(
                                AccreditedNetworkDetailsSection.Content(
                                    text = "${Qualification.ISO_9001.description}, ${Qualification.JOINT_COMMISSION_INTERNATIONAL.description}"
                                )
                            ),
                            isExpandable = false
                        )
                    ),
                    action = SpecialistDetailsTransport.Action(
                        label = "Ver seu médico de família",
                        navigation = NavigationResponse(
                            mobileRoute = MobileRouting.ACCREDITED_NETWORK_PROFILE,
                            properties = mapOf(
                                "method" to RemoteActionMethod.GET,
                                "endpoint" to "/v2/accredited_network/healthcare_team/physician?lat=$latitude&lng=$longitude"
                            ),
                        )
                    ),
                    showMembershipCardShareButton = false
                )

                assertThat(result).isEqualTo(expectedSpecialistDetailsTransport)
            }
        }

    @Test
    fun `Should return healthcare team physician as SpecialistDetailsTransport without lat and lng`() {
        val presentialContact = TestModelFactory.buildContact(
            modality = ModalityType.PRESENTIAL,
            addressId = address.id,
            phones = listOf(
                PhoneNumber(phone = "************"),
                PhoneNumber(phone = "***********", type = PhoneType.MOBILE),
                PhoneNumber(phone = "***********", type = PhoneType.WHATSAPP)
            )

        ).copy(address = address)
        val presentialContactWithEmptyAddress = TestModelFactory.buildContact(
            modality = ModalityType.PRESENTIAL,
        )
        val specialist = TestModelFactory.buildHealthProfessional(
            gender = Gender.MALE,
            education = listOf(specialistEducation),
            qualifications = listOf(Qualification.ISO_9001, Qualification.JOINT_COMMISSION_INTERNATIONAL),
            appointmentTypes = listOf(
                SpecialistAppointmentType.REMOTE,
                SpecialistAppointmentType.PRESENTIAL
            ),
            addressesStructured = listOf(address)
        ).copy(contacts = listOf(remoteContact, presentialContact, presentialContactWithEmptyAddress))
        val memberHealthCareTeamPhysician = TestModelFactory.buildHealthProfessional()
        val memberHealthcareTeam = TestModelFactory.buildHealthcareTeam(
            segment = HealthcareTeam.Segment.DEFAULT,
            physicianStaffId = memberHealthCareTeamPhysician.staffId
        )

        val result = AccreditedNetworkSpecialistResponseBuilder.buildHealthCareTeamSpecialistResponse(
            specialist = specialist,
            rootNodeId = ROOT_NODE_ID,
            memberHealthcareTeam = memberHealthcareTeam,
            person = person,
            schedulingInfo = null,
            serviceDays = emptyList(),
            appVersion = appVersion
        )

        val expectedSpecialistDetailsTransport = SpecialistDetailsTransport(
            id = specialist.id,
            providerType = specialist.type.toConsolidatedType(),
            title = specialist.type.toConsolidatedType().title,
            name = specialist.name,
            imageUrl = specialist.imageUrl ?: SPECIALIST_PLACEHOLDER_URL,
            crm = specialist.council.toString(),
            favoriteInfo = FavoriteInfoTransportBuilder.build(
                referenceId = specialist.id,
                specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                referenceType = specialist.type.toConsolidatedType(),
                isFavorite = false,
                appVersion = appVersion,
            ),
            services = listOf(
                ServiceLocation(
                    title = "Agendar",
                    details = "",
                    scheduleNavigation = PRE_TRIAGE_SCHEDULE_NAVIGATION,
                ),
                ServiceLocation(
                    title = "Por vídeo chamada",
                    details = "Mais rápido"
                ),
                ServiceLocation(
                    title = "Presencial",
                    details = "",
                    address = address.formattedAddress(),
                    serviceDays = emptyList(),
                    nextDateAvailable = null,
                    scheduleNavigation = null,
                    contacts = listOf(
                        ServiceLocation.ServiceContact(
                            text = "(071) 99999-9999",
                            url = "tel:************",
                            type = ServiceLocation.ServiceContactType.PHONE
                        ),
                        ServiceLocation.ServiceContact(
                            text = "(071) 99999-9998",
                            url = "tel:0***********",
                            type = ServiceLocation.ServiceContactType.MOBILE
                        ),
                        ServiceLocation.ServiceContact(
                            text = "(071) 99999-9998",
                            url = "https://wa.me/55***********",
                            type = ServiceLocation.ServiceContactType.WHATSAPP
                        )
                    )
                ),
                ServiceLocation(
                    title = "Presencial",
                    details = "",
                    address = null,
                    serviceDays = emptyList(),
                    nextDateAvailable = null,
                    scheduleNavigation = null
                )
            ),
            sections = listOf(
                AccreditedNetworkDetailsSection(
                    title = "Biografia",
                    icon = "user",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = specialist.profileBio.orEmpty()
                        )
                    )
                ),
                AccreditedNetworkDetailsSection(
                    title = "Educação",
                    icon = "paper",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = specialistEducation
                        )
                    )
                ),
                AccreditedNetworkDetailsSection(
                    title = "Certificações e qualificações",
                    icon = "script",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = "${Qualification.ISO_9001.description}, ${Qualification.JOINT_COMMISSION_INTERNATIONAL.description}"
                        )
                    ),
                    isExpandable = false
                )
            ),
            action = SpecialistDetailsTransport.Action(
                label = "Ver seu médico de família",
                navigation = NavigationResponse(
                    mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                    properties = mapOf(
                        "method" to RemoteActionMethod.GET,
                        "endpoint" to "/accredited_network/healthcare_team/physician/${memberHealthCareTeamPhysician.staffId}"
                    ),
                )
            ),
            showMembershipCardShareButton = false
        )

        assertThat(result).isEqualTo(expectedSpecialistDetailsTransport)
    }

    @Test
    fun `should return 'education' icon in SpecialistDetailsTransport section when app version is 4_30_0 or higher`() {
        val appVersion = SemanticVersion("4.30.0")
        val latitude = "-12.***************"
        val longitude = "-38.**************"
        val specialistEducation = "Educação"
        val address = TestModelFactory.buildStructuredAddress(
            latitude = latitude,
            longitude = longitude
        )
        val remoteContact = TestModelFactory.buildContact(
            website = "http://pudim.com.br/"
        )
        val presentialContact = TestModelFactory.buildContact(
            modality = ModalityType.PRESENTIAL,
            addressId = address.id

        ).copy(
            address = address,
            availableDays = listOf(
                AvailableDay(weekDay = ContactWeekday.MONDAY),
                AvailableDay(weekDay = ContactWeekday.WEDNESDAY),
                AvailableDay(weekDay = ContactWeekday.FRIDAY)
            ),
            scheduleAvailabilityDays = 3,
            website = "http://pudim.com.br/",
            phones = listOf(
                PhoneNumber(phone = "71999999999", type = PhoneType.PHONE),
                PhoneNumber(phone = "***********", type = PhoneType.WHATSAPP)
            )
        )
        val specialist = TestModelFactory.buildHealthProfessional(
            gender = Gender.MALE,
            education = listOf(specialistEducation),
            qualifications = listOf(Qualification.ISO_9001, Qualification.JOINT_COMMISSION_INTERNATIONAL),
            appointmentTypes = listOf(
                SpecialistAppointmentType.REMOTE,
                SpecialistAppointmentType.PRESENTIAL
            ),
            addressesStructured = listOf(address),
        ).copy(contacts = listOf(remoteContact, presentialContact))
        val memberHealthcareTeam = TestModelFactory.buildHealthcareTeam(
            segment = HealthcareTeam.Segment.DEFAULT,
            physicianStaffId = specialist.staffId
        )
        val schedulingUrl = "https://www.alice.com.br"
        val nextAvailableDate = LocalDateTime.of(2024, 7, 12, 9, 30)
        val serviceDays = listOf("seg", "qua", "sex")

        val result = AccreditedNetworkSpecialistResponseBuilder.buildHealthCareTeamSpecialistResponse(
            specialist = specialist,
            rootNodeId = ROOT_NODE_ID,
            memberHealthcareTeam = memberHealthcareTeam,
            person = person,
            schedulingInfo = SpecialistSchedulingInformationTransport(
                appointmentType = SpecialistAppointmentType.REMOTE,
                schedulingUrl = schedulingUrl,
                nextAvailableDate = nextAvailableDate,
            ),
            serviceDays = serviceDays,
            specialty = specialty,
            memberLat = latitude.toDouble(),
            memberLng = longitude.toDouble(),
            appVersion = appVersion,
        )

        val expectedSpecialistDetailsTransport = SpecialistDetailsTransport(
            id = specialist.id,
            providerType = specialist.type.toConsolidatedType(),
            title = specialty.name,
            name = specialist.name,
            imageUrl = specialist.imageUrl ?: SPECIALIST_PLACEHOLDER_URL,
            crm = specialist.council.toString(),
            favoriteInfo = null,
            tag = Tag(
                text = "Seu Médico de Família",
                colorScheme = TagColorScheme.MAGENTA
            ),
            services = listOf(
                ServiceLocation(
                    title = "Agendar",
                    details = "",
                    nextDateAvailable = nextAvailableDate.toString(),
                    scheduleNavigation = PRE_TRIAGE_SCHEDULE_NAVIGATION,
                ),
                ServiceLocation(
                    title = "Por vídeo chamada",
                    details = "Mais rápido",
                    serviceDays = serviceDays,
                    contacts = listOf(
                        ServiceLocation.ServiceContact(
                            text = "http://pudim.com.br/",
                            url = "http://pudim.com.br/",
                            type = ServiceLocation.ServiceContactType.WEBSITE
                        )
                    )
                ),
                ServiceLocation(
                    title = "Presencial",
                    details = "0 m de você",
                    address = address.formattedAddress(),
                    serviceDays = listOf("seg", "qua", "sex"),
                    tag = Tag(
                        icon = "calendar",
                        text = "Previsão de agenda: 3 dias",
                        colorScheme = TagColorScheme.GRAY
                    ),
                    contacts = listOf(
                        ServiceLocation.ServiceContact(
                            text = "(071) 99999-9999",
                            url = "tel:************",
                            type = ServiceLocation.ServiceContactType.PHONE
                        ),
                        ServiceLocation.ServiceContact(
                            text = "(071) 99999-9998",
                            url = "https://wa.me/55***********",
                            type = ServiceLocation.ServiceContactType.WHATSAPP
                        ),
                        ServiceLocation.ServiceContact(
                            text = "http://pudim.com.br/",
                            url = "http://pudim.com.br/",
                            type = ServiceLocation.ServiceContactType.WEBSITE
                        )

                    ),
                    nextDateAvailable = null,
                    scheduleNavigation = null
                )
            ),
            sections = listOf(
                AccreditedNetworkDetailsSection(
                    title = "Biografia",
                    icon = "user",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = specialist.profileBio.orEmpty()
                        )
                    )
                ),
                AccreditedNetworkDetailsSection(
                    title = "Educação",
                    icon = "education",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = specialistEducation
                        )
                    )
                ),
                AccreditedNetworkDetailsSection(
                    title = "Certificações e qualificações",
                    icon = "script",
                    content = listOf(
                        AccreditedNetworkDetailsSection.Content(
                            text = "${Qualification.ISO_9001.description}, ${Qualification.JOINT_COMMISSION_INTERNATIONAL.description}"
                        )
                    ),
                    isExpandable = false
                )
            ),
            action = null,
            showMembershipCardShareButton = false
        )

        assertThat(result).isEqualTo(expectedSpecialistDetailsTransport)
    }
}
