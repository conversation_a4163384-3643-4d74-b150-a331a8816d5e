package br.com.alice.member.api.models.videoCall

import br.com.alice.data.layer.models.VideoCallStatus
import br.com.alice.data.layer.models.VideoCallType
import br.com.alice.member.api.models.HealthProfessionalResponse
import java.util.UUID


data class VideoCallMeetingResponse(
    val id: UUID,
    val name: String,
    val type: VideoCallType,
    val status: VideoCallStatus,
    val amazonChimeConfig: AmazonChimeConfig? = null,
    val healthProfessional: HealthProfessionalResponse? = null
)

data class AmazonChimeConfig(
    val meetingResponse: MeetingResponse? = null,
    val attendeeResponse: AttendeeResponse? = null,
    val trackEvents: Boolean = false
)

data class MeetingResponse(
    val meeting: Meeting
)

data class AttendeeResponse(
    val attendee: Attendee
)

data class Meeting(
    val meetingId: UUID,
    val externalMeetingId: UUID,
    val mediaRegion: String,
    val mediaPlacement: MediaPlacement
)

data class MediaPlacement(
    val audioHostUrl: String,
    val audioFallbackUrl: String,
    val screenDataUrl: String,
    val screenSharingUrl: String,
    val screenViewingUrl: String,
    val signalingUrl: String,
    val turnControlUrl: String
)

data class Attendee(
    val attendeeId: UUID,
    val externalUserId: UUID,
    val joinToken: String
)
