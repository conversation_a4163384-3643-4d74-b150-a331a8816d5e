package br.com.alice.documentsigner.services

import br.com.alice.common.MultipartRequest
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.isNotNullOrBlank
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.common.storage.FileVaultStorage
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.Attachment
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.Prescription
import br.com.alice.data.layer.models.Referral
import br.com.alice.data.layer.models.TestRequest
import br.com.alice.documentsigner.clients.CertifyDocumentRequest
import br.com.alice.documentsigner.clients.DocumentRequest
import br.com.alice.documentsigner.clients.DocumentType
import br.com.alice.documentsigner.utils.PDFPrinter
import br.com.alice.documentsigner.utils.RoutineDocumentsUtil
import br.com.alice.person.client.PersonService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import java.io.File
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.UUID

class DocumentPrinterService(
    private val healthProfessionalService: HealthProfessionalService,
    private val personService: PersonService,
    private val signatureService: SignaturePdfService,
    private val fileVaultStorage: FileVaultStorage
) : Spannable {

    suspend fun printPrescriptionsPdf(
        prescriptions: List<Prescription>,
        staffId: UUID,
        id: UUID,
        token: String? = null
    ): Result<ByteArray, Throwable> =
        try {
            val person = personService.get(prescriptions.first().personId).get()
            val healthProfessional = healthProfessionalService.findByStaffId(staffId).getOrNullIfNotFound()
                ?: throw InvalidArgumentException(
                    code = "no_health_professional",
                    message = "Staff=$staffId is not a health professional"
                )

            coResultOf {
                if (token != null) {
                    val content = PDFPrinter.generatePrescriptions(
                        prescriptions,
                        LocalDateTime.now(ZoneId.of("America/Sao_Paulo")),
                        healthProfessional,
                        person
                    )
                    createCertifyRequest(content, healthProfessional, id, DocumentType.PRESCRIPTION, "Simples")
                        .let { signatureService.signDocument(it, token).get() }
                } else {
                    RoutineDocumentsUtil.generatePrescriptions(prescriptions, healthProfessional, person)
                }
            }
        } catch (e: Exception) {
            logger.error(
                "Error to generate prescriptions PDF",
                "staff_id" to staffId,
                e.message
            )
            e.failure()
        }

    suspend fun printSpecialPrescriptionsPdf(
        prescription: Prescription,
        physicianId: UUID,
        id: UUID,
        token: String? = null
    ): Result<ByteArray, Throwable> =
        try {
            val person = personService.get(prescription.personId).get()
            val healthProfessional =
                healthProfessionalService.findByStaffId(
                    physicianId,
                    HealthProfessionalService.FindOptions(withStaff = true)
                ).getOrNullIfNotFound()

            if (healthProfessional == null || !healthProfessional.staff!!.isPhysician()) {
                throw InvalidArgumentException(
                    code = "no_physician",
                    message = "Staff=$physicianId is not a physician"
                )
            }

            coResultOf {
                if (token != null) {
                    val content = PDFPrinter.generateSpecialPrescription(
                        prescription,
                        LocalDateTime.now(ZoneId.of("America/Sao_Paulo")),
                        healthProfessional,
                        person
                    )
                    createCertifyRequest(
                        content,
                        healthProfessional,
                        id,
                        DocumentType.PRESCRIPTION,
                        "Especial",
                    )
                        .let { signatureService.signDocument(it, token).get() }
                } else {
                    RoutineDocumentsUtil.generateSpecialPrescription(prescription, healthProfessional, person)
                }
            }
        } catch (e: Exception) {
            logger.error(
                "Error to generate prescription PDF",
                "staff_id" to physicianId,
                e.message
            )
            e.failure()
        }

    suspend fun printTestRequestPdf(testRequests: List<TestRequest>, staffId: UUID): Result<ByteArray, Throwable> =
        try {
            if (testRequests.isEmpty()) throw InvalidArgumentException(
                code = "empty_test_requests",
                message = "TestRequests is empty"
            )

            val healthProfessional = healthProfessionalService.findByStaffId(
                staffId = staffId,
                HealthProfessionalService.FindOptions(withStaff = true)
            ).getOrNullIfNotFound()
                ?: throw InvalidArgumentException(
                    code = "no_health_professional",
                    message = "Staff=$staffId is not a health professional"
                )

            Result.of {
                val person = personService.get(testRequests.first().personId).get()

                RoutineDocumentsUtil.generateExamRequest(
                    createdAt = testRequests.first().releasedAt ?: LocalDateTime.now(),
                    testRequests = testRequests.map { it.formattedTitle() },
                    healthProfessional = healthProfessional,
                    person = person
                )
            }
        } catch (e: Exception) {
            logger.error("Error to generate test requests PDF", "staff_id" to staffId, e.message)
            e.failure()
        }

    suspend fun printSignedTestRequestPdf(
        testRequests: List<TestRequest>,
        physicianId: UUID,
        id: UUID,
        token: String,
    ): Result<ByteArray, Throwable> =
        try {
            val person = personService.get(testRequests.first().personId).get()
            val healthProfessional = healthProfessionalService.findByStaffId(physicianId).getOrNullIfNotFound()
                ?: throw InvalidArgumentException(
                    code = "no_health_professional",
                    message = "Staff=$physicianId is not a health professional"
                )

            val content = PDFPrinter.generateSignedExamRequest(
                date = LocalDateTime.now(),
                testRequests = testRequests,
                healthProfessional = healthProfessional,
                person = person
            )

            coResultOf {
                createCertifyRequest(content, healthProfessional, id, DocumentType.TEST_REQUEST).let {
                    signatureService.signDocument(it, token).get()
                }
            }
        } catch (e: Exception) {
            logger.error(
                "Error generating signed test request PDF",
                "physician_id" to physicianId,
                e.message
            )

            e.failure()
        }

    suspend fun printExcuseNotes(
        appointment: Appointment,
        staffId: UUID,
        documentToken: String,
        token: String? = null,
    ): Result<ByteArray, Throwable> = span("printExcuseNotes") { span ->
        span.setAttribute("appointment_id", appointment.id.toString())
        span.setAttribute("staff_id", staffId.toString())
        span.setAttribute("has_document_token", documentToken.isNotNullOrBlank())
        try {
            val person = personService.get(appointment.personId).get()
            val healthProfessional = healthProfessionalService.findByStaffId(staffId).get()

            coResultOf {
                if (token != null) {
                    val content = PDFPrinter.generateExcuseNotes(appointment, healthProfessional, person, documentToken)
                    createCertifyRequest(
                        content,
                        healthProfessional,
                        appointment.id,
                        DocumentType.APPOINTMENT,
                        "Atestado médico",
                    ).let { signatureService.signDocument(it, token).get() }
                } else {
                    RoutineDocumentsUtil.generateExcuseNotes(appointment, healthProfessional, person)
                }
            }
        } catch (e: Exception) {
            logger.error(
                "Error generating signed excuse notes PDF",
                "staff_id" to staffId,
                "appointment" to appointment,
                e
            )

            e.failure()
        }
    }

    suspend fun printReferralPdf(
        referral: Referral,
        staffId: UUID,
        id: UUID,
        token: String? = null
    ): Result<ByteArray, Throwable> =
        try {
            val person = personService.get(referral.personId).get()
            val healthProfessional = healthProfessionalService.findByStaffId(staffId).getOrNullIfNotFound()
                ?: throw InvalidArgumentException(
                    code = "no_health_professional",
                    message = "Staff=$staffId is not a health professional"
                )

            coResultOf {
                if (token != null) {
                    val content = PDFPrinter.generateSignedReferral(
                        referral,
                        LocalDateTime.now(ZoneId.of("America/Sao_Paulo")),
                        healthProfessional,
                        person
                    )
                    createCertifyRequest(content, healthProfessional, id, DocumentType.REFERRAL)
                        .let { signatureService.signDocument(it, token).get() }
                } else {
                    RoutineDocumentsUtil.generateReferral(
                        referral,
                        LocalDateTime.now(ZoneId.of("America/Sao_Paulo")),
                        healthProfessional,
                        person
                    )
                }
            }
        } catch (e: Exception) {
            logger.error(
                "Error to generate referral PDF",
                "staff_id" to staffId,
                e.message
            )
            e.failure()
        }

    suspend fun saveFile(
        id: UUID,
        personId: PersonId,
        doc: ByteArray,
        namespace: String,
        fileName: String,
    ) =
        fileVaultStorage.store(
            personId = personId,
            domain = "ehr",
            namespace = namespace,
            multipartRequest = MultipartRequest(
                fileContent = doc.inputStream(),
                file = File("${RangeUUID.generate()}.pdf"),
                parameters = emptyMap()
            )
        ).fold(
            { vaultResponse ->
                logger.info(
                    "DocumentPrinterService.saveFile: saved file",
                    "source_id" to id,
                    "file_id" to vaultResponse.id
                )

                Attachment(
                    id = vaultResponse.id,
                    type = vaultResponse.type,
                    fileName = fileName,
                )
            }, { ex ->
                logger.error(
                    "DocumentPrinterService.saveFile: error to upload file on file vault",
                    "source_id" to id,
                    ex
                )
                throw BadRequestException(
                    "error to upload file source_id=$id",
                    "file_vault_exception",
                    ex
                )
            }
        )

    private fun createCertifyRequest(
        byteArray: ByteArray,
        healthProfessional: HealthProfessional,
        id: UUID,
        documentType: DocumentType,
        documentTypeDescription: String = ""
    ): CertifyDocumentRequest {
        return CertifyDocumentRequest(
            signatureSettings = listOf(
                signatureService.createDefaultSignatureSettings(
                    healthProfessional.council,
                    documentType,
                    documentTypeDescription,
                    id
                )
            ),
            documents = listOf(
                DocumentRequest(
                    id = id.toString(),
                    originalFileName = "$id.pdf",
                    data = signatureService.transformContent(byteArray)
                )
            )
        )
    }
}
