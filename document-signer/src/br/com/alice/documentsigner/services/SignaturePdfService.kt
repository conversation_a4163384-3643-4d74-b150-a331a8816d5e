package br.com.alice.documentsigner.services

import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Council
import br.com.alice.documentsigner.clients.CertifyDocumentRequest
import br.com.alice.documentsigner.clients.CessClient
import br.com.alice.documentsigner.clients.DocumentType
import br.com.alice.documentsigner.clients.DocumentType.APPOINTMENT
import br.com.alice.documentsigner.clients.DocumentType.PRESCRIPTION
import br.com.alice.documentsigner.clients.DocumentType.TEST_REQUEST
import br.com.alice.documentsigner.clients.DocumentType.REFERRAL
import br.com.alice.documentsigner.clients.OID
import br.com.alice.documentsigner.clients.SignatureSettings
import br.com.alice.documentsigner.clients.SignatureSettingsExtraInfoItem
import java.util.Base64
import java.util.UUID

class SignaturePdfService(private val cessClient: CessClient) {

    private val base64 = Base64.getEncoder()

    suspend fun signDocument(request: CertifyDocumentRequest, token: String) =
        coResultOf<ByteArray, Throwable> {
            cessClient.signDocument(request, token)
        }.thenError {
            logger.error("SignaturePdfService::signDocument error to sign document", "error" to it)
        }

    fun transformContent(byteArray: ByteArray) =
        "data:application/pdf;base64,${base64.encodeToString(byteArray)}"

    fun createDefaultSignatureSettings(
        physicianCouncil: Council?,
        documentType: DocumentType,
        documentTypeDescription: String,
        id: UUID? = null
    ) =
        SignatureSettings(
            contact = "",
            location = "Av. Rebouças, 3506 - Pinheiros, São Paulo / SP - 05402-600",
            reason = when (documentType) {
                PRESCRIPTION -> "Prescrição de medicamento"
                TEST_REQUEST -> "Pedido de exame"
                APPOINTMENT -> "Atestado médico"
                REFERRAL -> "Encaminhamento"
            },
            extraInfo = listOf(
                SignatureSettingsExtraInfoItem(
                    name = OID.COUNCIL_NUMBER.value,
                    value = physicianCouncil?.number ?: ""
                ),
                SignatureSettingsExtraInfoItem(
                    name = OID.COUNCIL_STATE.value,
                    value = physicianCouncil?.state?.name ?: ""
                )

            ) + getExtraInfoItemByType(documentType, documentTypeDescription, id)
        )

    private fun getExtraInfoItemByType(
        documentType: DocumentType,
        documentTypeDescription: String,
        id: UUID?
    ) =
        when (documentType) {
            PRESCRIPTION -> listOf(
                SignatureSettingsExtraInfoItem(
                    name = OID.PRESCRIPTION.value,
                    value = documentTypeDescription
                ),
                SignatureSettingsExtraInfoItem(
                    name = OID.PRESCRIPTION_TYPE.value,
                    value = documentTypeDescription
                )
            )
            TEST_REQUEST -> listOf(
                SignatureSettingsExtraInfoItem(
                    name = OID.TEST_REQUEST.value,
                    value = documentTypeDescription
                )
            )
            APPOINTMENT -> listOf(
                SignatureSettingsExtraInfoItem(
                    name = OID.EXCUSE_NOTES.value,
                    value = documentTypeDescription
                ),
                SignatureSettingsExtraInfoItem(
                    name = OID.EXCUSE_NOTES_NUMBER.value,
                    value = id.toString()
                )
            )
            REFERRAL -> listOf(
                SignatureSettingsExtraInfoItem(
                    name = OID.REFERRAL.value,
                    value = documentTypeDescription
                )
            )
        }
}
