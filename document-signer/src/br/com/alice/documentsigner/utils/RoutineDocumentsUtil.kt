package br.com.alice.documentsigner.utils

import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.Prescription
import br.com.alice.data.layer.models.PrescriptionMedicineType.SPECIAL
import br.com.alice.data.layer.models.Referral
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.time.LocalDateTime
import org.apache.pdfbox.multipdf.PDFMergerUtility
import org.apache.pdfbox.pdmodel.PDDocument
import org.apache.pdfbox.pdmodel.PDPageContentStream
import org.apache.pdfbox.pdmodel.font.PDTrueTypeFont
import org.apache.pdfbox.pdmodel.font.encoding.WinAnsiEncoding

object RoutineDocumentsUtil {

    private const val defaultFontSize = 11f
    private const val physicianAddressLineX = 315f
    private const val physicianLineTwoY = 695f
    private const val physicianAddressLineOneData = "Av. Rebouças, 3506 - Pinheiros"
    private const val physicianAddressLineTwoData = "São Paulo / SP - 05402-600"
    private const val personDataAndAddressLineX = 275f
    private const val personAddressSecondLineY = 645f
    private const val leading = 1.5f * defaultFontSize
    private const val linesPerPage = 17
    private const val charsPerLine = 80
    private const val bodyContentX = 70f

    private const val personNameLineSize = 30

    private const val examRequestTemplatePDF = "old_exam_request.pdf"
    private const val excuseNotesTemplatePDF = "old_excuse_notes.pdf"
    private const val simplePrescriptionTemplatePDF = "normal_prescription.pdf"
    private const val specialPrescriptionTemplatePDF = "special_prescription.pdf"
    private const val referaralTemplate = "referral_template.pdf"

    fun generateExamRequest(
        createdAt: LocalDateTime,
        testRequests: List<String>,
        healthProfessional: HealthProfessional,
        person: Person
    ): ByteArray {
        val dateY = 230f

        val dateContent = listOf(
            PdfStringContent(70f, dateY, createdAt.dayOfMonth.toString()),
            PdfStringContent(105f, dateY, createdAt.monthValue.toString()),
            PdfStringContent(145f, dateY, createdAt.year.toString())
        )

        return drawPdf(examRequestTemplatePDF, person, healthProfessional, testRequests, dateContent)
    }

    fun generateExcuseNotes(
        appointment: Appointment,
        healthProfessional: HealthProfessional,
        person: Person
    ): ByteArray {
        val physicianPersonalData = healthProfessional.getSanitizedName()
        val physicianFirstLineFooterX = 400f
        val physicianFirstLineFooterY = 213f
        val physicianSecondLineFooterX = 405f
        val physicianSecondLineFooterY = 195f
        val dateY = 230f

        val date = appointment.createdAt.toSaoPauloTimeZone()

        val dateContent = listOf(
            PdfStringContent(70f, dateY, date.dayOfMonth.toString()),
            PdfStringContent(105f, dateY, date.monthValue.toString()),
            PdfStringContent(145f, dateY, date.year.toString()),
            PdfStringContent(physicianFirstLineFooterX, physicianFirstLineFooterY, physicianPersonalData),
            PdfStringContent(physicianSecondLineFooterX, physicianSecondLineFooterY, healthProfessional.councilSignature)
        )

        val content = appointment.excuseNotes.map { it.description }

        return drawPdf(excuseNotesTemplatePDF, person, healthProfessional, content, dateContent)
    }

    fun generateSpecialPrescription(
        prescription: Prescription,
        healthProfessional: HealthProfessional,
        person: Person,
    ): ByteArray {
        logger.info("RoutineDocumentsUtil.generateSpecialPrescription", "prescription" to prescription)
        val pdfMerger = PDFMergerUtility()
        val outputStream = ByteArrayOutputStream(4096)
        pdfMerger.destinationStream = outputStream

        if (prescription.medicine?.type == SPECIAL) {
            repeat(2) {
                pdfMerger.addSource(
                    ByteArrayInputStream(
                        generateSpecialPrescription(
                            listOf(prescription.formattedTitle(), "\t${prescription.fullSentence()}"),
                            healthProfessional,
                            person,
                        )
                    )
                )
            }
        }

        pdfMerger.mergeDocuments(null)

        return outputStream.toByteArray()
    }

    fun generatePrescriptions(
        prescriptions: List<Prescription>,
        healthProfessional: HealthProfessional,
        person: Person,
    ): ByteArray {
        logger.info("RoutineDocumentsUtil.generatePrescriptions", "prescriptions" to prescriptions)
        val pdfMerger = PDFMergerUtility()
        val outputStream = ByteArrayOutputStream(4096)
        pdfMerger.destinationStream = outputStream

        if (prescriptions.isNotEmpty()) {
            pdfMerger.addSource(ByteArrayInputStream(generateSimplePrescription(prescriptions.map {
                listOf(
                    it.formattedTitle(),
                    "\t${it.fullSentence()}"
                )
            }.flatten(), healthProfessional, person)))
        }

        pdfMerger.mergeDocuments(null)

        return outputStream.toByteArray()
    }

    private fun generateSpecialPrescription(
        prescriptionDescriptions: List<String>,
        healthProfessional: HealthProfessional,
        person: Person,
    ) = drawPdf(specialPrescriptionTemplatePDF, person, healthProfessional, prescriptionDescriptions)

    private fun generateSimplePrescription(
        prescriptionDescriptions: List<String>,
        healthProfessional: HealthProfessional,
        person: Person,
    ) = drawPdf(simplePrescriptionTemplatePDF, person, healthProfessional, prescriptionDescriptions)

    fun generateReferral(
        referral: Referral,
        date: LocalDateTime,
        healthProfessional: HealthProfessional,
        person: Person,
    ): ByteArray {
        logger.info("RoutineDocumentsUtil.generateReferral", "referral" to referral)
        val dateY = 230f
        val fixedContent = listOf(
            PdfStringContent(70f, dateY, date.dayOfMonth.toString()),
            PdfStringContent(105f, dateY, date.monthValue.toString()),
            PdfStringContent(145f, dateY, date.year.toString())
        )

        return drawPdf(referaralTemplate, person, healthProfessional, listOf(referral.therapySentence()), fixedContent)
    }

    private fun drawPdf(
        template: String,
        person: Person,
        healthProfessional: HealthProfessional,
        content: List<String>,
        fixedContent: List<PdfContent> = emptyList()
    ): ByteArray {
        val document = PDDocument()
        val font = PDTrueTypeFont.load(document, getFontResource(), WinAnsiEncoding.INSTANCE)
        val personAddress: Address? = if (person.addresses.isEmpty()) null else person.addresses[0]
        val formattedFirstLinePersonAddress =
            if (personAddress != null) getFormattedFirstLinePersonAddress(personAddress) else "N/A"
        val formattedSecondLinePersonAddress =
            if (personAddress != null) getFormattedSecondLinePersonAddress(personAddress) else "N/A"
        val personName = getPersonPersonData(person)
        val firstColumnX = 100f
        val firstColumnY = 708f
        val secondColumnY = 658f

        val currentItemX = bodyContentX
        val currentItemY = 600f

        // serialize content to count characters per line and lines per page
        val allDataToString = content
            .map { MarkdownEraser.erase(it) }
            .joinToString(separator = System.lineSeparator()) { it.removeSpecialChars() }

        val contentListByNewLine = allDataToString.split(System.lineSeparator())
        val contentListByWordsPerLine = contentListByNewLine.map { it.chunked(charsPerLine) }
        val chunkedContent = contentListByWordsPerLine.flatten().chunked(linesPerPage)

        chunkedContent.forEach { contentList ->
            // create page
            val templateDocument = PDDocument.load(getResourceAsStream(template))
            val templatePage = templateDocument.getPage(0)
            val contentStream = PDPageContentStream(document, templatePage, PDPageContentStream.AppendMode.APPEND, true)
            contentStream.setFont(font, defaultFontSize)

            // draw header
            contentStream.drawString(firstColumnX, firstColumnY, healthProfessional.getSanitizedName())
            contentStream.drawString(firstColumnX, physicianLineTwoY, healthProfessional.councilSignature)
            contentStream.drawString(physicianAddressLineX, firstColumnY, physicianAddressLineOneData)
            contentStream.drawString(physicianAddressLineX, physicianLineTwoY, physicianAddressLineTwoData)
            contentStream.drawList(firstColumnX, secondColumnY, personName)
            contentStream.drawString(personDataAndAddressLineX, secondColumnY, formattedFirstLinePersonAddress)
            contentStream.drawString(personDataAndAddressLineX, personAddressSecondLineY, formattedSecondLinePersonAddress)

            // draw items per page
            contentStream.drawList(currentItemX, currentItemY, contentList)

            // draw fixed items per page
            fixedContent.forEach {
                when (it) {
                    is PdfStringContent -> contentStream.drawString(it.x, it.y, it.content)
                    is PdfListContent -> contentStream.drawList(it.x, it.y, it.content)
                }
            }

            contentStream.close()
            document.addPage(templatePage)
        }

        val output = ByteArrayOutputStream(1024)
        document.save(output)
        document.close()
        return output.toByteArray()
    }

    private fun PDPageContentStream.drawString(x: Float, y: Float, text: String) {
        beginText()
        newLineAtOffset(x, y)
        showText(text)
        endText()
    }

    private fun PDPageContentStream.drawList(x: Float, y: Float, lines: List<String>) {
        beginText()
        newLineAtOffset(x, y)
        lines.forEach { content ->
            showText(content.removeSpecialChars())
            newLineAtOffset(0f, -leading)
        }
        endText()
    }

    private fun getResourceAsStream(fileName: String) =
        javaClass.classLoader.getResourceAsStream("pdf_templates/$fileName")!!

    private fun getFontResource() =
        getResourceAsStream("poligon.ttf")

    private fun getFormattedSecondLinePersonAddress(address: Address) =
        "${address.city} / ${address.state}".removeSpecialChars()

    private fun getFormattedFirstLinePersonAddress(address: Address): String {
        val complement = if (address.complement.isNullOrEmpty()) "" else ", ${address.complement}"

        return "${address.street} ${address.number} $complement - ${address.neighbourhood}".removeSpecialChars()
    }

    private fun String.removeSpecialChars() =
        this.replace("[\r]".toRegex(), "")
            .replace("[\t]".toRegex(), "    ")
            .replace('\u00A0', ' ')
            .replace('\u00AD', ' ')
            .replace('\u2981', ' ')
            .replace('\u0009', ' ')
            .replace('\u1BCA', ' ')
            .replace('\u0009', ' ')
            .replace('\u0327', ' ')
            .replace('\uFFFD', ' ')

    private fun HealthProfessional.getSanitizedName() = name.removeSpecialChars()

    private fun getPersonPersonData(person: Person): List<String> {
        val fullName = person.fullSocialName.removeSpecialChars()

        if (fullName.length <= personNameLineSize) {
            return listOf(fullName)
        }

        val words = fullName.split(" ")
        val nameLines = mutableListOf(words.first())

        words.subList(1, words.size).forEach { word ->
            val currentLine = nameLines.last()

            if (currentLine.length + word.length < personNameLineSize) {
                nameLines[nameLines.lastIndex] = "$currentLine $word"
            } else {
                nameLines.add(word)
            }
        }

        return nameLines
    }

    interface PdfContent {
        val x: Float
        val y: Float
    }

    data class PdfStringContent(
        override val x: Float,
        override val y: Float,
        val content: String
    ) : PdfContent

    data class PdfListContent(
        override val x: Float,
        override val y: Float,
        val content: List<String>
    ) : PdfContent
}
