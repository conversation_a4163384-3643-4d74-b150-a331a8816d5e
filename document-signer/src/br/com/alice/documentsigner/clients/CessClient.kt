package br.com.alice.documentsigner.clients

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.common.service.serialization.gsonCompleteSerializer
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.documentsigner.LibConfig
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import com.google.gson.annotations.SerializedName
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.plugins.ServerResponseException
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.content.TextContent

class CessClient(
    private val client: HttpClient = DefaultHttpClient({
        expectSuccess = false

        install(ContentNegotiation) {
            gsonSnakeCase()
        }
    }, timeoutInMillis = 15_000)
) : Spannable {

    private val credentials = LibConfig.CessCredentials

    suspend fun authorize(grants: Grants): Result<AccessToken, Throwable> {
        val request = AuthorizationRequest(
            clientId = credentials.clientId,
            clientSecret = credentials.clientSecret,
            username = grants.username,
            password = grants.password
        )

        val response = client.post("${credentials.baseUrl}/oauth/pwd_authorize") {
            setBody(TextContent(gsonCompleteSerializer.toJson(request), ContentType.Application.Json))
        }

        return handleAuthorizeResponse(response)
    }

    private suspend fun handleAuthorizeResponse(
        response: HttpResponse,
    ): Result<AccessToken, Throwable> {
        val responseBody = response.bodyAsText()

        return when (response.status) {
            HttpStatusCode.OK -> {
                try {
                    span("authorize") {
                        val accessToken = gsonCompleteSerializer.fromJson(responseBody, AccessToken::class.java)
                        accessToken.success()
                    }
                } catch (ex: Exception) {
                    logger.error("CessClient::authorize error parsing AccessToken", ex)
                    CessClientException("Error parsing AccessToken: ${ex.message}").failure()
                }
            }
            HttpStatusCode.BadRequest -> {
                if (responseBody.contains("INVALID_CREDENTIALS")) {
                    logger.error("Invalid credentials","error_message" to responseBody)
                    CessClientAuthorizationException("Invalid credentials: $responseBody").failure()
                } else {
                    logger.error("Unexpected error response","error_message" to responseBody)
                    CessClientException("Unexpected Cess client error: $responseBody").failure()
                }
            }
            else -> {
                logger.error("Internal Server Error","error_message" to responseBody)
                ServerResponseException(response, responseBody).failure()
            }
        }
    }


    suspend fun signDocument(request: CertifyDocumentRequest, token: String): ByteArray {
        logger.info("CessClient::signDocument", "files" to request.documents.map { it.id })
        val response = createSignature(request, token)
        return getDocument(response, token)
    }

    private suspend fun createSignature(request: CertifyDocumentRequest, token: String): CertifyDocumentResponse =
        try {
            span("createSignature") {
                val responseString = client.post("${credentials.baseUrl}/signature-service") {
                    setBody(TextContent(gsonCompleteSerializer.toJson(request), ContentType.Application.Json))
                    header("Authorization", "Bearer $token")
                }.bodyAsText()
                gsonCompleteSerializer.fromJson(responseString, CertifyDocumentResponse::class.java)
            }
        } catch (ex: Exception) {
            logger.error("CessClient::createSignature Error to create signature on CESS", "request" to request, ex)
            throw ex
        }

    private suspend fun getDocument(signatureResponse: CertifyDocumentResponse, token: String): ByteArray =
        try {
            val document = signatureResponse.documents.first()

            span("getDocument") {
                client.get("${credentials.baseUrl}/file-transfer/${signatureResponse.tcn}/${document.id}") {
                    header("Authorization", "Bearer $token")
                }.body()
            }
        } catch (ex: Exception) {
            logger.error(
                "CessClient::getDocument Error to get signed document on CESS",
                "tcn" to signatureResponse.tcn,
                ex
            )
            throw ex
        }
}

const val CESS_TOKEN_LIFETIME_IN_SECONDS = 604800

data class AuthorizationRequest(
    val clientId: String,
    val clientSecret: String,
    val username: String,
    val password: String,
    val grantType: String = "password",
    val scope: String = "signature_session",
    val lifetime: Int = CESS_TOKEN_LIFETIME_IN_SECONDS
)

data class Grants(
    val username: String,
    val password: String,
)

data class AccessToken(
    val accessToken: String
)

data class DocumentRequest(
    val id: String,
    val signatureSetting: String = "default",
    val originalFileName: String,
    val data: String
)

data class SignatureSettingsExtraInfoItem(
    val name: String,
    val value: String
)

data class SignatureSettings(
    val id: String = "default",
    val contact: String,
    val location: String,
    val reason: String,
    val visibleSignature: Boolean = false,
    @SerializedName("extraInfo")
    val extraInfo: List<SignatureSettingsExtraInfoItem>
)

data class CertifyDocumentRequest(
    val type: String = "PDFSignature",
    val hashAlgorithm: String = "SHA256",
    val autoFixDocument: Boolean = true,
    val mode: String = "sync",
    val signatureSettings: List<SignatureSettings>,
    val documentsSource: String = "DATA_URL",
    val documents: List<DocumentRequest>
)

data class CertifyDocumentResponse(
    val tcn: String,
    val status: Int,
    val documents: List<DocumentResponse>
)

data class DocumentResponse(
    val id: String
)

enum class DocumentType {
    PRESCRIPTION,
    TEST_REQUEST,
    APPOINTMENT,
    REFERRAL
}

enum class OID(val value: String) {
    COUNCIL_NUMBER("*********.*******"),
    COUNCIL_STATE("*********.*******"),
    PRESCRIPTION("*********.12.1.1"),
    PRESCRIPTION_TYPE("*********.********"),
    TEST_REQUEST("*********.12.1.3"),
    EXCUSE_NOTES("*********.12.1.2"),
    EXCUSE_NOTES_NUMBER("*********.********"),
    REFERRAL("*********.12.1.11"),
}
