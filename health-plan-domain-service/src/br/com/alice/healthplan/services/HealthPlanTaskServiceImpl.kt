package br.com.alice.healthplan.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.ConflictException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.isAfterEq
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.foldNotFound
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.basePredicateForFilters
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.coverage.client.LocationService
import br.com.alice.data.layer.models.Attachment
import br.com.alice.data.layer.models.Deadline
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.HealthPlanTaskStatus.ACTIVE
import br.com.alice.data.layer.models.HealthPlanTaskStatus.CREATE
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DELETED
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DELETED_BY_MEMBER
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DELETED_BY_STAFF
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DONE
import br.com.alice.data.layer.models.HealthPlanTaskStatus.DRAFT
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.HealthPlanTaskType.*
import br.com.alice.data.layer.models.PeriodUnit
import br.com.alice.data.layer.models.Prescription
import br.com.alice.data.layer.models.PrescriptionMedicineType.SIMPLE
import br.com.alice.data.layer.models.PrescriptionMedicineType.SPECIAL
import br.com.alice.data.layer.models.Referral
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.TaskSourceType.MEMBER
import br.com.alice.data.layer.models.TaskSourceType.STAFF
import br.com.alice.data.layer.models.TaskSourceType.SYSTEM
import br.com.alice.data.layer.models.TaskUpdatedBy
import br.com.alice.data.layer.models.TestRequest
import br.com.alice.data.layer.models.copy
import br.com.alice.data.layer.services.HealthPlanTaskDataService
import br.com.alice.data.layer.strategies.ConflictResolutionStrategy
import br.com.alice.documentsigner.services.DocumentPrinterService
import br.com.alice.ehr.client.AdvancedAccessService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthlogic.client.AdherenceValidationService
import br.com.alice.healthlogic.models.adherence.AdherenceResponse
import br.com.alice.healthplan.client.ActiveReferralsByPersonNotFound
import br.com.alice.healthplan.client.HealthPlanTaskCounters
import br.com.alice.healthplan.client.HealthPlanTaskFilters
import br.com.alice.healthplan.client.HealthPlanTaskGroupService
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.healthplan.client.PublishGroupedDemandRequest
import br.com.alice.healthplan.client.ReferralWithoutSpecialistNotFound
import br.com.alice.healthplan.converters.HealthPlanTaskConverter
import br.com.alice.healthplan.converters.HealthPlanTaskTransportConverter
import br.com.alice.healthplan.converters.HealthPlanTasksTransportConverter
import br.com.alice.healthplan.converters.ReferralSuggestionResponseConverter
import br.com.alice.healthplan.converters.taskProgress.TaskProgressInfoBuilderImpl
import br.com.alice.healthplan.events.HealthPlanTaskGroupPublishedEvent
import br.com.alice.healthplan.events.HealthPlanTaskOverdueEvent
import br.com.alice.healthplan.events.HealthPlanTaskUpsertedEvent
import br.com.alice.healthplan.extensions.internal.fillDateUsingNewDeadlineInterval
import br.com.alice.healthplan.extensions.onValidInitState
import br.com.alice.healthplan.models.HealthPlanTaskTransport
import br.com.alice.healthplan.models.HealthPlanTasksTransport
import br.com.alice.healthplan.models.ReferralSuggestionResponse
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.apache.commons.lang3.RandomStringUtils
import kotlin.reflect.full.isSubclassOf

class HealthPlanTaskServiceImpl(
    private val healthPlanTaskDataService: HealthPlanTaskDataService,
    private val kafkaProducer: KafkaProducerService,
    private val documentPrinterService: DocumentPrinterService,
    private val healthPlanTaskGroupService: HealthPlanTaskGroupService,
    private val staffService: StaffService,
    private val locationService: LocationService,
    private val adherenceValidationService: AdherenceValidationService,
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val advancedAccessService: AdvancedAccessService,
) : HealthPlanTaskService, Spannable {

    private val filterKeyStatus = "status"
    private val filterKeyType = "type"
    private val filterKeyGroup = "group"
    private val filterKeyReleasedBy = "released_by"
    private val filterKeyCaseId = "case_id"
    private val filterKeyAppointmentId = "appointment_id"

    private val defaultStatuses = listOf(DRAFT, ACTIVE, DELETED_BY_MEMBER, DONE)
    private val actionPlanTaskBackfillStatusList = listOf(ACTIVE, DONE, DELETED, DELETED_BY_MEMBER, DELETED_BY_STAFF)
    private val validHealthPlanTypes = listOf(
        PRESCRIPTION,
        EATING,
        PHYSICAL_ACTIVITY,
        SLEEP,
        MOOD,
        OTHERS,
        TEST_REQUEST,
        REFERRAL,
        EMERGENCY,
        FOLLOW_UP_REQUEST,
        SURGERY_PRESCRIPTION
    )

    private companion object {
        val validStatusesToDelete = listOf(ACTIVE, DONE, DELETED, DELETED_BY_MEMBER, DELETED_BY_STAFF)
    }

    override suspend fun create(
        healthPlanTask: HealthPlanTask,
        staffId: UUID,
        shouldValidateAdherence: Boolean
    ): Result<HealthPlanTaskTransport, Throwable> =
        catchResult {
            val task = addRequester(healthPlanTask, staffId)
            val taskGeneralize = handleAdvancedAccessHealthPlanTask(task)
                ?.let { getGeneralizeTask(it) }
                ?: getGeneralizeTask(task)

            healthPlanTaskDataService.add(taskGeneralize).then {
                notifyTaskChange(it, staffId, originMethodCall = "HealthPlanTaskServiceImpl#create")
            }.map { taskCreated ->
                checkAdherence(shouldValidateAdherence, taskCreated)
            }
        }

    override suspend fun update(
        healthPlanTask: HealthPlanTask,
        staffId: UUID,
        shouldValidateAdherence: Boolean
    ): Result<HealthPlanTaskTransport, Throwable> =
        get(healthPlanTask.id).map { task ->
            if (ConflictResolutionStrategy.hasConflict(healthPlanTask, task) { staffId }) {
                throw ConflictException("Conflict on update of healthPlanTaskId = ${task.id}")
            } else {
                updateTask(
                    healthPlanTask.copy(version = task.version),
                    task,
                    staffId,
                ).let { taskUpdated ->
                    checkAdherence(shouldValidateAdherence, taskUpdated)
                }
            }
        }

    override suspend fun getTransportById(id: UUID): Result<HealthPlanTaskTransport, Throwable> =
        healthPlanTaskDataService.get(id).map { convertToTransport(it) }

    override suspend fun updateTask(task: HealthPlanTask, notify: Boolean): Result<HealthPlanTask, Throwable> =
        healthPlanTaskDataService.update(task).then {
            if (notify) notifyTaskChange(it, originMethodCall = "HealthPlanTaskServiceImpl#updateTask")
        }

    suspend fun updateAll(tasks: List<HealthPlanTask>, notify: Boolean): Result<List<HealthPlanTask>, Throwable> =
        healthPlanTaskDataService.updateList(tasks).then {
            if (notify)
                it.pmap { task ->
                    notifyTaskChange(task, originMethodCall = "HealthPlanTaskServiceImpl#updateAll")
                }
        }

    override suspend fun updateAll(
        healthPlanTasks: List<HealthPlanTaskTransport>,
        staff: Staff
    ): Result<List<HealthPlanTask>, Throwable> {
        val tasksMap = healthPlanTasks.mapNotNull { it.id }
            .takeIf { it.isNotEmpty() }
            ?.let { ids ->
                healthPlanTaskDataService.find { where { id.inList(ids) } }.get().map { it.id to it }
            }?.toMap() ?: emptyMap()

        return if (hasConflicts(healthPlanTasks, tasksMap, staff)) {
            ConflictException("Conflict on update a task").failure()
        } else {
            healthPlanTasks.map {
                val newTask = HealthPlanTaskConverter.convert(it, staff.id)
                updateTask(newTask, tasksMap[it.id], staff.id)
            }.success()
        }
    }

    override suspend fun acknowledge(
        id: UUID,
        acknowledgedAt: LocalDateTime
    ): Result<HealthPlanTask, Throwable> =
        healthPlanTaskDataService.get(id).flatMap { task ->
            healthPlanTaskDataService.update(task.copy(acknowledgedAt = acknowledgedAt))
        }.then { task ->
            notifyTaskChange(task, originMethodCall = "HealthPlanTaskServiceImpl#acknowlegde")
        }

    override suspend fun acknowledgeTasks(
        ids: List<UUID>,
        acknowledgedAt: LocalDateTime
    ): Result<List<HealthPlanTask>, Throwable> =
        getByIds(ids).map { tasks ->
            updateList(tasks.map { it.copy(acknowledgedAt = acknowledgedAt) })
        }

    override suspend fun init(id: UUID, initiatedAt: LocalDateTime): Result<HealthPlanTask, Throwable> =
        healthPlanTaskDataService.get(id).flatMap { task ->
            val progressCalculable = TaskProgressInfoBuilderImpl().canBuild(task)

            if (!task.onValidInitState() || !progressCalculable) {
                return InvalidArgumentException(
                    code = "invalid_task_state",
                    message = "task $id cannot be initiated"
                ).failure()
            }

            healthPlanTaskDataService
                .update(task.copy(initiatedByMemberAt = initiatedAt))
                .then {
                    notifyTaskChange(it, originMethodCall = "HealthPlanTaskServiceImpl#init")
                }.map {
                    val endAt = TaskProgressInfoBuilderImpl().build(it)?.endAt

                    if (it.type != PRESCRIPTION || endAt == null) {
                        return@map it
                    }

                    val prescription = it.specialize<Prescription>().copy(medicineEndAt = endAt)

                    healthPlanTaskDataService.update(prescription.generalize()).get()
                }
        }

    override suspend fun deleteByMember(id: UUID): Result<HealthPlanTask, Throwable> =
        healthPlanTaskDataService.get(id)
            .flatMap { task ->
                if (task.status == DONE) {
                    return@flatMap InvalidArgumentException(
                        code = "invalid_task_state",
                        message = "task ${task.id} is already DONE and cannot be deleted"
                    ).failure()
                }

                val deletedTask = task.copy(
                    status = DELETED_BY_MEMBER,
                    finishedAt = LocalDateTime.now(),
                    finishedBy = TaskUpdatedBy(source = MEMBER),
                )

                healthPlanTaskDataService.update(deletedTask)
                    .then {
                        logger.info("HealthPlanTaskServiceImpl #deleteByMember", "task_id" to task.id)
                        notifyTaskChange(
                            it,
                            previousStatus = task.status,
                            originMethodCall = "HealthPlanTaskServiceImpl#deleteByMember"
                        )
                    }
            }

    override suspend fun favorite(id: UUID): Result<HealthPlanTask, Throwable> =
        healthPlanTaskDataService.get(id)
            .flatMap { task ->
                if (task.status == DELETED || task.status == DELETED_BY_MEMBER || task.status == DELETED_BY_STAFF) {
                    return@flatMap InvalidArgumentException(
                        code = "invalid_task_state",
                        message = "task ${task.id} is already DELETED and cannot be favorite"
                    ).failure()
                }

                healthPlanTaskDataService.update(
                    task.copy(
                        favorite = true
                    )
                )
            }.then { notifyTaskChange(it, originMethodCall = "HealthPlanTaskServiceImpl#favorite") }

    override suspend fun unfavorite(id: UUID): Result<HealthPlanTask, Throwable> =
        healthPlanTaskDataService.get(id)
            .flatMap { task ->
                healthPlanTaskDataService.update(
                    task.copy(
                        favorite = false
                    )
                )
            }.then { notifyTaskChange(it, originMethodCall = "HealthPlanTaskServiceImpl#unfavorite") }

    override suspend fun completeInitiated(): Result<List<HealthPlanTask>, Throwable> =
        healthPlanTaskDataService.find {
            where {
                this.status.eq(ACTIVE).and(this.initiatedByMemberAt.isNotNull())
            }.orderBy { status }.sortOrder { asc }
        }.flatMap { tasks ->
            tasks.filter { task ->
                val endAt = task.deadline?.fillDate(task.initiatedByMemberAt)?.date

                endAt != null && endAt.toLocalDate().atEndOfTheDay() < LocalDateTime.now()
            }.map { task ->
                completeTask(task).get()
            }.success()
        }

    override suspend fun completeTask(task: HealthPlanTask): Result<HealthPlanTask, Throwable> {
        if (alreadyDoneBySystem(task)) {
            return task.success()
        }

        val finishedTask = task.copy(
            status = DONE,
            finishedBy = TaskUpdatedBy(source = SYSTEM),
            finishedAt = LocalDateTime.now(),
        )

        return healthPlanTaskDataService.update(finishedTask)
            .then {
                logger.info(
                    "HealthPlanTaskServiceImpl #completeTask",
                    "task_id" to task.id
                )
                notifyTaskChange(
                    it,
                    previousStatus = task.status,
                    originMethodCall = "HealthPlanTaskServiceImpl#completeTask"
                )
            }.thenError {
                logger.info(
                    "HealthPlanTaskServiceImpl #completeTask error updating task",
                    "task_id" to task.id
                )
            }
    }

    override suspend fun completeTaskByMember(id: UUID): Result<HealthPlanTask, Throwable> {
        return healthPlanTaskDataService.get(id).flatMap { task ->
            healthPlanTaskDataService.update(
                task.copy(
                    status = DONE,
                    finishedBy = TaskUpdatedBy(source = MEMBER),
                    finishedAt = LocalDateTime.now()
                )
            ).then {
                logger.info("HealthPlanTaskServiceImpl #completeTaskByMember", "task_id" to task.id)
                notifyTaskChange(
                    it,
                    previousStatus = task.status,
                    originMethodCall = "HealthPlanTaskServiceImpl#completeTaskByMember"
                )
            }
        }
    }

    override suspend fun activateTask(task: HealthPlanTask): Result<HealthPlanTask, Throwable> {
        if (task.status == ACTIVE) {
            return task.success()
        }

        return healthPlanTaskDataService.update(
            task.copy(
                status = ACTIVE,
                finishedBy = null,
                finishedAt = null,
            )
        ).then {
            logger.info("HealthPlanTaskServiceImpl #activateTask", "task_id" to task.id)
            notifyTaskChange(
                it,
                previousStatus = task.status,
                originMethodCall = "HealthPlanTaskServiceImpl#activateTask"
            )
        }
    }

    override suspend fun get(id: UUID) = healthPlanTaskDataService.get(id)

    override suspend fun getByIds(ids: List<UUID>) = healthPlanTaskDataService.find { where { id.inList(ids) } }

    override suspend fun getByAppointmentId(appointmentId: UUID) =
        healthPlanTaskDataService.find { where { this.appointmentId.eq(appointmentId) } }

    override suspend fun getAllByIds(ids: List<UUID>) =
        getByIds(ids)
            .flatMap { tasks ->
                if (tasks.size == ids.size)
                    tasks.success()
                else
                    NotFoundException("Could not find ALL tasks").failure()
            }

    override suspend fun getByHealthPlan(healthPlanId: UUID): Result<List<HealthPlanTask>, Throwable> =
        healthPlanTaskDataService.find {
            where {
                this.healthPlanId.eq(healthPlanId).and(this.status.inList(listOf(DRAFT, ACTIVE, DONE)))
            }.orderBy { createdAt }.sortOrder { asc }
        }

    override suspend fun findByPersonAndFilters(
        personId: PersonId,
        filterOptions: HealthPlanTaskFilters,
        checkAdherence: Boolean
    ): Result<List<HealthPlanTask>, Throwable> =
        span("findByPersonAndFilters") { span ->
            filterTasks(personId, filterOptions)
                .recordResult(span)
        }

    override suspend fun findByPersonAndCreatedByList(
        personId: PersonId,
        createdByList: List<TaskUpdatedBy>
    ): Result<List<HealthPlanTask>, Throwable> =
        healthPlanTaskDataService.find {
            where {
                this.personId.eq(personId)
                    .and(this.createdBy.inList(createdByList))
                    .and(this.status.eq(ACTIVE))
            }
        }

    override suspend fun getByPerson(
        personId: PersonId,
        filterOptions: Map<String, List<String>>,
        checkAdherence: Boolean
    ): Result<HealthPlanTasksTransport, Throwable> = coroutineScope {
        filterTasks(personId, filterOptions.toFilter()).map { tasks ->
            val groupIds = tasks.mapNotNull { it.groupId }.distinct()
            val staffIds = tasks.map { it.getStaffIds() }.flatten().distinct()
            val cityIds = tasks.mapNotNull { it.content?.get("cityId")?.toString() }.distinct()

            val associatedGroupsDeferred = async {
                healthPlanTaskGroupService.findByIds(groupIds)
                    .get()
                    .associateBy { it.id!! }
            }

            val associatedStaffsDeferred = async {
                staffService.findByList(staffIds).get().associateBy { it.id }
            }

            val associatedCitiesDeferred = async {
                cityIds.takeIf { it.isNotEmpty() }?.let { ids ->
                    if (canLoadCityInIbge()) locationService.getCities(ids).getOrNullIfNotFound()?.associateBy { it.id }
                    else null
                } ?: emptyMap()
            }

            val adherenceValidationsDeferred = async {
                if (checkAdherence) {
                    adherenceValidationService.getByTaskIds(tasks).fold(
                        { adherenceByTaskIdList ->
                            adherenceByTaskIdList.takeIf { it.isNotEmpty() }
                                ?.associate { it.taskId to it.adherenceValidation } ?: emptyMap()
                        },
                        { emptyMap() }
                    )
                } else emptyMap()
            }

            logger.info(
                "HealthPlanTaskServiceImpl.getByPerson filteredTasks",
                "size" to tasks.size,
                "person_id" to personId
            )

            HealthPlanTasksTransportConverter.convert(
                tasks.groupBy { it.type }.mapValues { it.value },
                associatedStaffsDeferred.await(),
                associatedGroupsDeferred.await(),
                associatedCitiesDeferred.await(),
                adherenceValidationsDeferred.await()
            )
        }.thenError {
            logger.error(
                "HealthPlanTaskServiceImpl::getByPerson: error to get tasks",
                "person_id" to personId,
                "filter_options" to filterOptions,
                "check_adherence" to checkAdherence,
                it
            )
        }
    }

    override suspend fun getByDateRangeForActionPlanTaskBackfill(
        startingDateTime: LocalDateTime,
        endingDateTime: LocalDateTime,
        offset: Int,
        limit: Int,
    ): Result<List<HealthPlanTask>, Throwable> =
        healthPlanTaskDataService.find {
            where {
                this.type.inList(validHealthPlanTypes)
                    .and(this.status.inList(actionPlanTaskBackfillStatusList))
                    .and(this.createdAt.greaterEq(startingDateTime))
                    .and(this.createdAt.lessEq(endingDateTime))
            }.orderBy {
                createdAt
            }.sortOrder {
                asc
            }.offset {
                offset
            }.limit {
                limit
            }
        }

    override suspend fun getByPersonForActionPlanTaskBackfill(
        personId: PersonId,
        offset: Int,
        limit: Int,
    ): Result<List<HealthPlanTask>, Throwable> =
        healthPlanTaskDataService.find {
            where {
                this.type.inList(validHealthPlanTypes)
                    .and(this.personId.eq(personId))
                    .and(this.status.inList(actionPlanTaskBackfillStatusList))
            }.orderBy {
                createdAt
            }.sortOrder {
                desc
            }.offset {
                offset
            }.limit {
                limit
            }
        }

    override suspend fun getLastActiveReferralWithoutSpecialist(personId: PersonId): Result<Referral, Throwable> =
        healthPlanTaskDataService.find {
            where {
                this.personId.eq(personId) and
                        this.type.eq(REFERRAL) and
                        this.status.inList(listOf(ACTIVE))
            }
        }
            .foldNotFound { ActiveReferralsByPersonNotFound(personId.toString()).failure() }
            .flatMap { tasks ->
                val lastReferralWithoutSpecialist = tasks
                    .map { it.specialize<Referral>() }
                    .filter { it.suggestedSpecialist == null }
                    .maxByOrNull { it.updatedAt }

                lastReferralWithoutSpecialist?.success()
                    ?: ReferralWithoutSpecialistNotFound(personId.toString()).failure()
            }

    override suspend fun getTask(shortId: String, token: String): Result<HealthPlanTask, Throwable> =
        healthPlanTaskDataService.findOne {
            where { this.shortId.eq(shortId) and this.token.eq(token) }
        }

    override suspend fun getByPersonAndCreateBy(
        personId: PersonId,
        createdBy: TaskUpdatedBy
    ): Result<List<HealthPlanTask>, Throwable> =
        healthPlanTaskDataService.find {
            where {
                this.personId.eq(personId) and
                        this.createdBy.eq(createdBy) and
                        status.inList(listOf(DRAFT, ACTIVE))
            }
        }

    override suspend fun getByPersonAndCreatedByAndType(
        personId: PersonId,
        createdBy: TaskUpdatedBy,
        type: HealthPlanTaskType?
    ): Result<List<HealthPlanTask>, Throwable> =
        healthPlanTaskDataService.find {
            where {
                this.personId.eq(personId) and
                        this.createdBy.eq(createdBy) and
                        this.status.eq(ACTIVE) and
                        type?.let { this.type.eq(type) }
            }
        }

    override suspend fun getTaskReferralByPersonAndStaffId(
        personId: PersonId,
        staffId: UUID
    ): Result<ReferralSuggestionResponse, Throwable> =
        healthPlanTaskDataService.findOne {
            where {
                this.personId.eq(personId) and
                        this.status.eq(ACTIVE) and
                        this.type.eq(REFERRAL) and
                        this.healthSpecialistId.eq(staffId.toString())
            }.orderBy { this.dueDate }.sortOrder { desc }
        }.flatMapPair { healthPlanTask ->
            healthPlanTaskGroupService.get(healthPlanTask.groupId!!)
        }.map { (group, healthPlanTask) ->
            ReferralSuggestionResponseConverter.convert(healthPlanTask.specialize(), group)
        }

    override suspend fun getAllActiveByPersonAndType(
        personId: PersonId,
        type: HealthPlanTaskType
    ): Result<List<HealthPlanTask>, Throwable> =
        healthPlanTaskDataService.find {
            where {
                this.personId.eq(personId)
                    .and(this.type.eq(type))
                    .and(this.status.eq(ACTIVE))
            }
        }

    override suspend fun getAllActiveByPersonAndTypeAndDueDate(
        personId: PersonId,
        type: HealthPlanTaskType,
        dateUntil: LocalDate
    ): Result<List<HealthPlanTask>, Throwable> = healthPlanTaskDataService.find {
        where {
            this.personId.eq(personId)
                .and(this.type.eq(type))
                .and(this.status.eq(ACTIVE))
                .and(this.dueDate.greaterEq(dateUntil))
        }
    }

    override suspend fun getActiveReferralsByPersonAndSpecialtyIdAndDueDate(
        personId: PersonId,
        specialtyId: UUID,
        dateUntil: LocalDate
    ): Result<List<Referral>, Throwable> =
        getAllActiveByPersonAndTypeAndDueDate(personId, REFERRAL, dateUntil)
            .map { healthPlanTasks ->
                healthPlanTasks.pmap { it.specialize<Referral>() }
                    .filter { it.specialty?.id == specialtyId }
            }

    override suspend fun getAllByTypeAndStatus(
        personId: PersonId,
        type: HealthPlanTaskType,
        status: List<HealthPlanTaskStatus>
    ): Result<List<HealthPlanTask>, Throwable> =
        healthPlanTaskDataService.find {
            where {
                this.personId.eq(personId)
                    .and(this.type.eq(type))
                    .and(this.status.inList(status))
            }
        }

    override suspend fun getByGroup(groupId: UUID): Result<List<HealthPlanTask>, Throwable> =
        healthPlanTaskDataService.find {
            where {
                this.groupId.eq(groupId).and(this.status.inList(listOf(DRAFT, ACTIVE, DONE)))
            }.orderBy { createdAt }.sortOrder { asc }
        }

    override suspend fun getAllActiveTasksByTypeAndReleasedInterval(
        type: HealthPlanTaskType,
        dateTimeFromOffset: LocalDateTime,
        dateTimeToOffset: LocalDateTime
    ): Result<List<HealthPlanTask>, Throwable> =
        healthPlanTaskDataService.find {
            where {
                this.type.eq(type)
                    .and(this.status.eq(ACTIVE))
                    .and(this.releasedAt.greater(dateTimeFromOffset))
                    .and(this.releasedAt.lessEq(dateTimeToOffset))
            }
        }

    override suspend fun markAsDoneUndone(id: UUID, staffId: UUID): Result<HealthPlanTaskTransport, Throwable> =
        healthPlanTaskDataService.get(id).map { task ->
            val taskToUpdate = task.copy(
                status = toggleStatus(task.status),
                finishedBy = toggleFinishedBy(task.status, staffId)
            )

            updateTask(taskToUpdate, task, staffId).run { convertToTransport(this) }
        }

    override suspend fun delete(id: UUID, staffId: UUID): Result<Boolean, Throwable> =
        healthPlanTaskDataService.get(id).map { task ->
            val statusToUpdate = if (task.status == DRAFT) DELETED else DELETED_BY_STAFF
            val taskToUpdate = task.copy(status = statusToUpdate)

            updateTask(taskToUpdate, task, staffId).let { true }
        }

    override suspend fun sendOverdueTasks(date: LocalDate): Result<Boolean, Throwable> =
        healthPlanTaskDataService.find {
            where {
                type.inList(listOf(REFERRAL, PRESCRIPTION, TEST_REQUEST, EMERGENCY))
                    .and(status.inList(listOf(ACTIVE)))
                    .and(dueDate.less(date))
                    .and(dueDate.greater(date.minusDays(2)))
            }
        }.map { all ->
            val tasks = all.groupBy { it.personId }
            tasks.mapKeys {
                val event = HealthPlanTaskOverdueEvent(it.key, it.value)
                kafkaProducer.produce(event)
            }
        }.let { true.success() }

    override suspend fun getCountsByPersonId(
        personId: PersonId,
        filters: Map<String, List<String>>
    ): Result<HealthPlanTaskCounters, Throwable> = filters
        .let { filtersNew ->
            healthPlanTaskDataService.countGrouped {
                where {
                    this.buildByFilter(personId, filtersNew.toFilter())
                }.groupBy {
                    listOf(this.type)
                }
            }.map { tasksGroupedByType ->
                tasksGroupedByType
                    .associate {
                        HealthPlanTaskType.valueOf(it.values.first()) to it.count
                    }.let { map ->
                        HealthPlanTaskCounters(
                            prescription = map[PRESCRIPTION] ?: 0,
                            eating = map[EATING] ?: 0,
                            physicalActivity = map[PHYSICAL_ACTIVITY] ?: 0,
                            sleep = map[SLEEP] ?: 0,
                            mood = map[MOOD] ?: 0,
                            others = map[OTHERS] ?: 0,
                            testRequest = map[TEST_REQUEST] ?: 0,
                            referral = map[REFERRAL] ?: 0,
                            emergency = map[EMERGENCY] ?: 0,
                            followUpRequest = map[FOLLOW_UP_REQUEST] ?: 0,
                            surgeryPrescription = map[SURGERY_PRESCRIPTION] ?: 0
                        )
                    }
            }
        }

    override suspend fun getByOriginTaskIdAndType(
        originTaskId: UUID,
        type: HealthPlanTaskType
    ): Result<List<HealthPlanTask>, Throwable> =
        healthPlanTaskDataService.find {
            where {
                this.originTaskId.eq(originTaskId).and(this.type.eq(type)).and(this.status.eq(ACTIVE))
            }
        }

    override suspend fun existsByPersonAndCreatedBy(
        personId: PersonId,
        createdBy: TaskUpdatedBy,
    ): Result<Boolean, Throwable> =
        healthPlanTaskDataService.exists {
            where {
                this.personId.eq(personId)
                    .and(this.createdBy.eq(createdBy))
                    .and(this.status.eq(ACTIVE))
            }
        }

    override suspend fun getByFilters(filters: HealthPlanTaskFilters) =
        healthPlanTaskDataService.find {
            where {
                buildQueryWithFilters(filters, this)
            }.let { queryBuilder ->
                filters.range?.let { range ->
                    queryBuilder.offset {
                        range.first
                    }.limit {
                        range.count()
                    }
                } ?: queryBuilder
            }
        }

    override suspend fun existsByFilters(filters: HealthPlanTaskFilters): Result<Boolean, Throwable> = catchResult {
        filters.isValid()
        healthPlanTaskDataService.exists {
            where {
                buildQueryWithFilters(filters, this)
            }
        }
    }

    override suspend fun publishTasksByDemand(
        request: PublishGroupedDemandRequest
    ): Result<List<HealthPlanTask>, Throwable> = span("publishTasksByDemand") { span ->
        span.setAttribute("staff_id", request.staffId)
        span.setAttribute("case_id", request.caseId)
        span.setAttribute("type", request.type)

        getTasksByDemand(request).flatMap {
            publishTasks(
                it,
                request.caseId,
                request.type,
                request.token,
                request.staffId
            )
        }.recordResult(span)
    }

    @OptIn(WithFilterPredicateUsage::class)
    private suspend fun getTasksByDemand(request: PublishGroupedDemandRequest) =
        healthPlanTaskDataService.find {
            where {
                this.caseId.eq(request.caseId)
                    .withFilter(request.type) { this.type.eq(it) }
                    .withFilter(statusOnFlag()) { this.status.inList(it) }
                    .withFilter(request.personId) { this.personId.eq(it) }!!
            }
                .orderBy { this.createdAt }
                .sortOrder { desc }
        }

    private fun statusOnFlag() =
        FeatureService.get(
            namespace = FeatureNamespace.HEALTH_PLAN,
            key = "health_plan_task_get_by_demand_statuses",
            defaultValue = listOf("DRAFT", "ACTIVE")
        ).map {
            HealthPlanTaskStatus.valueOf(it)
        }

    suspend fun publishTasks(
        healthPlanTasks: List<HealthPlanTask>,
        id: UUID,
        type: HealthPlanTaskType,
        token: String? = null,
        staffId: UUID
    ): Result<List<HealthPlanTask>, Throwable> =
        span("publishTasks") { span ->
            span.setAttribute("tasks_size", healthPlanTasks.size)
            span.setAttribute("id", id)
            span.setAttribute("type", type)
            span.setAttribute("staff_id", staffId)

            coResultOf<List<HealthPlanTask>, Throwable> {
                healthPlanTasks.validateRequiredFields().let { tasks ->
                    when (type) {
                        PRESCRIPTION -> {
                            span.setAttribute("has_digital_prescription", tasks.hasDigitalPrescription())
                            span.setAttribute("has_common_prescription", tasks.hasCommonPrescription())

                            if (tasks.first().specialize<Prescription>().digitalPrescription != null) {
                                callUpdateTask(tasks, staffId)
                            } else {
                                processPrescriptionToPublish(token, staffId, tasks)
                            }
                        }

                        TEST_REQUEST -> processTestRequestToPublish(token, staffId, tasks)
                        REFERRAL -> {
                            if (shouldCreateReferralDocument(staffId)) processReferralToPublish(token, staffId, tasks)
                            else callUpdateTask(tasks, staffId)
                        }
                        else -> callUpdateTask(tasks, staffId)
                    }
                }.also { tasks -> notifyPublishedTasks(tasks, staffId, id, type) }
            }.recordResult(span)
        }


    private suspend fun callUpdateTask(tasks: List<HealthPlanTask>, staffId: UUID) =
        tasks.pmap {
            val taskToUpdate = it.copy(status = ACTIVE, releasedByStaffId = staffId).setDate()

            updateTask(taskToUpdate, it, staffId)
        }

    private fun getGeneralizeTask(healthPlanTask: HealthPlanTask) =
        healthPlanTask.setDate().let {
            if (it::class.isSubclassOf(HealthPlanTask::class)) {
                it.generalize()
            } else it
        }

    private suspend fun handleAdvancedAccessHealthPlanTask(healthPlanTask: HealthPlanTask) =
        takeIf { healthPlanTask.isReferral() }?.let {
            val referral = healthPlanTask.specialize<Referral>()
            val subSpecialtyId = referral.subSpecialty?.id ?: return@let null

            val updatedReferral = referral.copy(
                isAdvancedAccess = false
            ).generalize()

            medicalSpecialtyService.getById(subSpecialtyId)
                .flatMap { specialty ->
                    if (specialty.isAdvancedAccess)
                        advancedAccessService.getAdvancedAccessDurationByPersonId(referral.personId)
                            .map { durationInDays ->
                                referral.copy(
                                    isAdvancedAccess = true,
                                    task = referral.task.copy(
                                        title = "Encaminhamento para ${specialty.name}",
                                        deadline = Deadline(
                                            quantity = durationInDays,
                                            unit = PeriodUnit.DAY
                                        ).fillDate(healthPlanTask.releasedAt ?: LocalDateTime.now()),
                                    )
                                ).generalize()
                            }
                    else updatedReferral.success()
                }.getOrElse { updatedReferral }

        }


    private suspend fun hasConflicts(
        healthPlanTasksTransport: List<HealthPlanTaskTransport>,
        tasks: Map<UUID, HealthPlanTask>,
        staff: Staff
    ) =
        healthPlanTasksTransport.map { task ->
            tasks[task.id]?.let {
                ConflictResolutionStrategy.hasConflict(task, it) { staff.id }
            } ?: false
        }.find { it } ?: false

    private fun addRequester(task: HealthPlanTask, staffId: UUID): HealthPlanTask =
        task.copy(lastRequesterStaffId = staffId, requestersStaffIds = task.requestersStaffIds.plus(staffId))

    private suspend fun updateTask(
        newTask: HealthPlanTask,
        oldTask: HealthPlanTask?,
        staffId: UUID
    ): HealthPlanTask {
        return addRequester(newTask, staffId)
            .takeIf { oldTask != it }
            ?.takeIf { isValidStatusChange(it, oldTask) }
            ?.let { healthPlanTask ->
                val healthPlanTaskUpdatedWithoutDeadline = healthPlanTask.copy(deadline = null)
                val task = handleAdvancedAccessHealthPlanTask(healthPlanTaskUpdatedWithoutDeadline)
                    ?.let { getGeneralizeTask(it) }
                    ?: getGeneralizeTask(healthPlanTaskUpdatedWithoutDeadline)
                if (newTask.status == CREATE) {
                    healthPlanTaskDataService.add(task).then {
                        notifyTaskChange(it, staffId, originMethodCall = "HealthPlanTaskServiceImpl#updateTask#add")
                    }.get()
                } else {
                    healthPlanTaskDataService.update(task).then {
                        logger.info("HealthPlanTaskServiceImpl #updateTask", "task_id" to task.id)
                        notifyTaskChange(
                            it,
                            staffId,
                            oldTask?.status,
                            originMethodCall = "HealthPlanTaskServiceImpl#updateTask#update"
                        )
                    }.get()
                }
            } ?: oldTask!!
    }

    private suspend fun notifyTaskChange(
        task: HealthPlanTask,
        staffId: UUID? = null,
        previousStatus: HealthPlanTaskStatus? = null,
        originMethodCall: String
    ) {
        try {
            if (validStatusesToDelete.contains(task.status)) {
                val event = HealthPlanTaskUpsertedEvent(task, staffId, previousStatus)
                logger.info(
                    "notifyTaskChange for HealthPlanTaskUpsertedEvent - publishing message",
                    "task_id" to task.id,
                    "task_person_id" to task.personId,
                    "task_type" to task.type,
                    "task_status" to task.status,
                    "staff_id" to staffId,
                    "origin_method_call" to originMethodCall
                )
                kafkaProducer.produce(event = event, partitionKey = event.payload.task.id.toString())
                logger.info(
                    "notifyTaskChange for HealthPlanTaskUpsertedEvent - message published",
                    "task_id" to task.id,
                    "task_person_id" to task.personId,
                    "task_type" to task.type,
                    "task_status" to task.status,
                    "staff_id" to staffId,
                    "origin_method_call" to originMethodCall
                )
            }
        } catch (e: Exception) {
            logger.error(
                "notifyTaskChange for HealthPlanTaskUpsertedEvent - error notifying",
                "task_id" to task.id,
                "task_person_id" to task.personId,
                "task_type" to task.type,
                "task_status" to task.status,
                "staff_id" to staffId,
                "origin_method_call" to originMethodCall,
                e
            )
            throw e
        }

    }

    private suspend fun notifyPublishedTasks(
        tasks: List<HealthPlanTask>,
        staffId: UUID,
        groupId: UUID,
        type: HealthPlanTaskType
    ) {
        val personId = tasks.firstOrNull()?.personId

        logger.info(
            "Publishing HealthPlanTaskGroupPublishedEvent",
            "person_id" to personId, "staff_id" to staffId, "group_id" to groupId, "type" to type
        )

        if (tasks.isNotEmpty()) kafkaProducer.produce(HealthPlanTaskGroupPublishedEvent(tasks))
    }

    private fun isValidStatusChange(newTask: HealthPlanTask, oldTask: HealthPlanTask?): Boolean {
        // create a new task
        if (oldTask == null) return true

        // release task to app
        if (oldTask.isDraft() && newTask.isActive()) return true
        // delete draft task
        if (oldTask.isDraft() && newTask.isDeleted()) return true
        // delete active task
        if (oldTask.isActive() && newTask.isDeletedByStaff()) return true
        // complete active task
        if (oldTask.isActive() && newTask.isDone()) return true
        // update draft tasks
        if (oldTask.isDraft() && newTask.isDraft()) return true
        // update done to active
        if (oldTask.isDone() && newTask.isActive()) return true

        // update attachments
        if (oldTask.isActive() && newTask.isActive() && oldTask.attachments != newTask.attachments) return true

        // any other change is invalid
        return false
    }

    private suspend fun saveTestRequestFile(tasks: List<HealthPlanTask>, doc: ByteArray) =
        tasks.first().let {
            documentPrinterService.saveFile(
                id = it.caseId ?: it.id,
                personId = it.personId,
                doc = doc,
                namespace = "test_request",
                fileName = "Pedido_de_exame.pdf"
            )
        }

    private suspend fun saveSignedTestRequestFile(tasks: List<HealthPlanTask>, doc: ByteArray) =
        tasks.first().let {
            documentPrinterService.saveFile(
                id = it.caseId ?: it.id,
                personId = it.personId,
                doc = doc,
                namespace = "signed_test_request",
                fileName = "Pedido_de_exame.pdf"
            )
        }

    private suspend fun savePrescriptionFile(tasks: List<HealthPlanTask>, doc: ByteArray) =
        tasks.first().let {
            documentPrinterService.saveFile(
                id = it.caseId ?: it.id,
                personId = it.personId,
                doc = doc,
                namespace = "signed_prescription",
                fileName = "Prescrição.pdf"
            )
        }

    private suspend fun saveReferralFile(task: HealthPlanTask, doc: ByteArray) =
        documentPrinterService.saveFile(
            id = task.caseId ?: task.id,
            personId = task.personId,
            doc = doc,
            namespace = "signed_referral",
            fileName = "Encaminhamento.pdf"
        )

    private fun toggleStatus(status: HealthPlanTaskStatus): HealthPlanTaskStatus =
        when (status) {
            ACTIVE -> DONE
            DONE -> ACTIVE
            else -> status
        }

    private fun toggleFinishedBy(status: HealthPlanTaskStatus, staffId: UUID): TaskUpdatedBy? =
        when (status) {
            ACTIVE -> TaskUpdatedBy(id = staffId, source = STAFF)
            DONE -> null
            else -> null
        }

    private suspend fun convertToTransport(
        task: HealthPlanTask,
        adherenceValidation: AdherenceResponse? = null
    ): HealthPlanTaskTransport = coroutineScope {
        val citiesMapDeferred = async {
            task.content?.get("cityId")?.toString()?.let { cityId ->
                if (canLoadCityInIbge())
                    locationService.getCity(cityId).map {
                        mapOf(it.id to it)
                    }.getOrNullIfNotFound()
                else null
            } ?: mapOf()
        }

        val staffMapDeferred = async {
            staffService.findByList(
                task.requestersStaffIds.toList()
            ).get().associateBy { it.id }
        }

        val groupMapDeferred = async {
            task.groupId?.let { groupId ->
                healthPlanTaskGroupService.get(groupId).map { group ->
                    mapOf(groupId to group)
                }.getOrNullIfNotFound()
            } ?: emptyMap()
        }

        HealthPlanTaskTransportConverter.convertToTransport(
            task,
            staffMapDeferred.await(),
            groupMapDeferred.await(),
            citiesMapDeferred.await(),
            adherenceValidation
        )
    }

    private suspend fun filterTasks(
        personId: PersonId,
        filters: HealthPlanTaskFilters
    ): Result<List<HealthPlanTask>, Throwable> =
        span("filterTasks") { span ->
            span.setAttribute("person_id", personId.toString())
            span.setAttribute("filters", filters.toString())

            healthPlanTaskDataService.find {
                where {
                    this.buildByFilter(personId, filters)
                }
                    .orderBy { createdAt }
                    .sortOrder { desc }
            }.recordResult(span)
        }

    private suspend fun getAdherenceResponse(task: HealthPlanTask): AdherenceResponse? =
        task.caseRecordDetails.firstOrNull()?.description?.id?.let { healthConditionId ->
            adherenceValidationService.validateAdherence(
                healthConditionId = healthConditionId.toUUID(),
                healthPlanTask = task,
                forceValidation = false
            ).getOrElse { null }
        }

    @OptIn(WithFilterPredicateUsage::class)
    private fun HealthPlanTaskDataService.FieldOptions.buildByFilter(
        personId: PersonId,
        filters: HealthPlanTaskFilters
    ) =
        this.personId.eq(personId)
            .withFilter(filters.statuses ?: defaultStatuses) { this.status.inList(it) }
            .withFilter(filters.types) { this.type.inList(it) }
            .withFilter(filters.groups) { this.groupId.inList(it) }
            .withFilter(filters.releasedByStaffIds) { this.releasedByStaffId.inList(it) }
            .withFilter(filters.caseIds) { this.caseId.inList(it) }
            .withFilter(filters.appointmentIds) { this.appointmentId.inList(it) }
            .withFilter(filters.dueDateGreater) { this.dueDate.greaterEq(it) }
            .withFilter(filters.releasedAtGreater) { this.releasedAt.greaterEq(it) }!!

    private fun alreadyDoneBySystem(task: HealthPlanTask): Boolean =
        task.status == DONE && task.finishedBy?.source == SYSTEM

    private suspend fun updateList(healthPlanTasks: List<HealthPlanTask>) =
        healthPlanTasks.chunked(50).pmap { healthPlanTasksChunk ->
            logger.info(
                "HealthPlanTaskServiceImpl:updateList Updating health plan task list in chunks",
                "task_list_chunk_size" to healthPlanTasksChunk.size
            )
            healthPlanTaskDataService.updateList(healthPlanTasksChunk).get()
        }.flatten()

    private suspend fun processPrescriptionToPublish(
        token: String?,
        staffId: UUID,
        prescriptionTasks: List<HealthPlanTask>
    ): List<HealthPlanTask> = coroutineScope {
        try {
            val prescriptions = prescriptionTasks.map {
                it.specialize<Prescription>().copy(
                    shortId = generateTaskShortId(),
                    token = generateTaskToken(),
                )
            }

            val simpleFileDeferred =
                async { getSimplePrescriptions(prescriptions, staffId, token, prescriptionTasks) }
            val specialFilesDeferred =
                async { getSpecialPrescription(prescriptions, staffId, token, prescriptionTasks) }
            val simpleFile = simpleFileDeferred.await()
            val specialFiles = specialFilesDeferred.await()

            prescriptions.pmap { prescription ->
                val attachment = if (prescription.medicine?.type == SIMPLE) {
                    simpleFile
                } else specialFiles.getValue(prescription.id)

                val attachments = prescription.attachments.filter { it.fileName != attachment.fileName }

                val taskToUpdate = prescription.generalize().copy(
                    status = ACTIVE,
                    releasedByStaffId = prescription.releasedByStaffId ?: staffId,
                    attachments = attachments.plus(attachment)
                )
                val task = updateTask(taskToUpdate, prescription, staffId)
                logger.info(
                    "HealthPlanTaskServiceImpl.processPrescriptionToPublish",
                    "task_id" to prescription.id,
                    "has_token" to (token != null),
                    "staff_id" to staffId
                )
                task
            }
        } catch (ex: Exception) {
            throw InvalidArgumentException("error to generate prescription PDF", "certifier_error", ex)
        }
    }

    private suspend fun processTestRequestToPublish(
        token: String?,
        staffId: UUID,
        testRequestsTasks: List<HealthPlanTask>,
    ): List<HealthPlanTask> = coroutineScope {
        val testRequests = testRequestsTasks.map {
            it.specialize<TestRequest>().copy(
                shortId = generateTaskShortId(),
                token = generateTaskToken(),
            )
        }

        val attachment = async {
            token?.let { getSignedTestRequestAttachment(testRequests, staffId, token, testRequestsTasks) }
                ?: getTestRequestAttachment(testRequests, staffId, testRequestsTasks)
        }.await()

        testRequests.pmap { testRequest ->
            val attachments = testRequest.attachments.filter {
                it.fileName != attachment.fileName
            }

            val taskToUpdate = testRequest.generalize().copy(
                status = ACTIVE,
                releasedByStaffId = testRequest.releasedByStaffId ?: staffId,
                attachments = attachments.plus(attachment)
            )

            val task = updateTask(taskToUpdate, testRequest, staffId)

            logger.info(
                "HealthPlanTaskServiceImpl.processTestRequestToPublish",
                "task_id" to testRequest.id,
                "person_id" to testRequest.personId,
                "short_id" to testRequest.shortId,
                "task_content" to taskToUpdate.content,
                "staff_id" to staffId
            )

            task
        }
    }

    private suspend fun processReferralToPublish(
        token: String?,
        staffId: UUID,
        referralTasks: List<HealthPlanTask>
    ): List<HealthPlanTask> = coroutineScope {
        referralTasks.pmap { referralTask ->

            val referralSpecialized = referralTask.specialize<Referral>()

            if (referralSpecialized.sessionsQuantity == null){
                logger.info(
                    "HealthPlanTaskServiceImpl.processReferralToPublish::sessionsQuantity is null",
                    "task_id" to referralTask.id,
                    "person_id" to referralTask.personId,
                    "task_content" to referralTask.content,
                    "staff_id" to staffId,
                )

                val task = callUpdateTask(listOf(referralTask), staffId)
                return@pmap task.first()
            }

            val referral = referralSpecialized.copy(
                    shortId = generateTaskShortId(),
                    token = generateTaskToken(),
                )

            val attachment = async {  getReferralAttachment(referral, staffId, token, referralTask) }.await()

            val attachments = referral.attachments.filter {
                it.fileName != attachment.fileName
            }

            val taskToUpdate = referral.generalize().copy(
                status = ACTIVE,
                releasedByStaffId = referral.releasedByStaffId ?: staffId,
                attachments = attachments.plus(attachment)
            )

            val task = updateTask(taskToUpdate, referral, staffId)

            logger.info(
                "HealthPlanTaskServiceImpl.processReferralToPublish",
                "task_id" to referral.id,
                "person_id" to referral.personId,
                "short_id" to referral.shortId,
                "task_content" to taskToUpdate.content,
                "staff_id" to staffId,
                "attachment" to attachment.fileName
            )

            task
        }
    }


    private fun generateTaskToken() = RandomStringUtils.randomNumeric(4)

    private fun generateTaskShortId() = RandomStringUtils.randomAlphanumeric(6)

    private suspend fun getSpecialPrescription(
        prescriptions: List<Prescription>,
        staffId: UUID,
        token: String?,
        prescriptionTasks: List<HealthPlanTask>
    ): Map<UUID, Attachment> =
        prescriptions.filter { it.medicine?.type == SPECIAL }.pmap { prescription ->
            prescription.id to documentPrinterService.printSpecialPrescriptionsPdf(
                prescription,
                staffId,
                prescription.caseId ?: prescription.id,
                token
            ).map { content -> savePrescriptionFile(prescriptionTasks, content) }.get()
        }.toMap()

    private suspend fun getSimplePrescriptions(
        prescriptions: List<Prescription>,
        staffId: UUID,
        token: String?,
        prescriptionTasks: List<HealthPlanTask>
    ): Attachment =
        prescriptions.first().let { prescription ->
            documentPrinterService.printPrescriptionsPdf(
                prescriptions,
                staffId,
                prescription.caseId ?: prescription.id,
                token
            ).map { content ->
                savePrescriptionFile(prescriptionTasks, content)
            }.get()
        }

    private suspend fun getTestRequestAttachment(
        testRequests: List<TestRequest>,
        staffId: UUID,
        testRequestTasks: List<HealthPlanTask>,
    ): Attachment =
        documentPrinterService.printTestRequestPdf(testRequests, staffId).map { content ->
            saveTestRequestFile(testRequestTasks, content)
        }.get()

    private suspend fun getSignedTestRequestAttachment(
        testRequests: List<TestRequest>,
        physicianId: UUID,
        token: String,
        testRequestTasks: List<HealthPlanTask>,
    ): Attachment =
        testRequests.first().let { testRequest ->
            documentPrinterService.printSignedTestRequestPdf(
                testRequests,
                physicianId,
                testRequest.caseId ?: testRequest.id,
                token
            ).map { content ->
                saveSignedTestRequestFile(testRequestTasks, content)
            }.get()
        }

    private suspend fun getReferralAttachment(
        referral: Referral,
        physicianId: UUID,
        token: String?,
        referralTask: HealthPlanTask,
    ): Attachment =
        documentPrinterService.printReferralPdf(
            referral,
            physicianId,
            referral.caseId ?: referral.id,
            token
        ).map { content ->
            saveReferralFile(referralTask, content)
        }.get()

    private suspend fun checkAdherence(
        shouldValidateAdherence: Boolean,
        task: HealthPlanTask
    ): HealthPlanTaskTransport =
        convertToTransport(
            task,
            if (shouldValidateAdherence) getAdherenceResponse(task) else null
        )


    private fun List<HealthPlanTask>.validateRequiredFields() = also { tasks ->
        tasks
            .filter { it.status == DRAFT }
            .forEach { it.checkRequiredField() }
    }

    private fun List<HealthPlanTask>.hasDigitalPrescription() =
        this.any { it.specialize<Prescription>().digitalPrescription != null }

    private fun List<HealthPlanTask>.hasCommonPrescription() =
        this.any { it.specialize<Prescription>().digitalPrescription == null }

    private fun Map<String, List<String>>.toFilter() =
        HealthPlanTaskFilters().let { filters ->
            get(filterKeyStatus)
                ?.map { HealthPlanTaskStatus.valueOf(it.uppercase()) }
                ?.let { filters.copy(statuses = it) }
                ?: filters
        }.let { filters ->
            get(filterKeyType)
                ?.map { HealthPlanTaskType.valueOf(it.uppercase()) }
                ?.let { filters.copy(types = it) }
                ?: filters
        }.let { filters ->
            get(filterKeyGroup)
                ?.map { it.toUUID() }
                ?.let { filters.copy(groups = it) }
                ?: filters
        }.let { filters ->
            get(filterKeyReleasedBy)
                ?.map { it.toUUID() }
                ?.let { filters.copy(releasedByStaffIds = it) }
                ?: filters
        }.let { filters ->
            get(filterKeyCaseId)
                ?.map { it.toUUID() }
                ?.let { filters.copy(caseIds = it) }
                ?: filters
        }.let { filters ->
            get(filterKeyAppointmentId)
                ?.map { it.toUUID() }
                ?.let { filters.copy(appointmentIds = it) }
                ?: filters
        }

    private fun canLoadCityInIbge() =
        FeatureService.get(
            namespace = FeatureNamespace.HEALTH_PLAN,
            key = "can_load_city_in_ibge",
            defaultValue = true
        )

    @OptIn(WithFilterPredicateUsage::class)
    private fun buildQueryWithFilters(
        filters: HealthPlanTaskFilters,
        fieldOptions: HealthPlanTaskDataService.FieldOptions
    ): Predicate {
        filters.isValid()

        return basePredicateForFilters()
            .withFilter(filters.statuses) { fieldOptions.status.inList(it) }
            .withFilter(filters.types) { fieldOptions.type.inList(it) }
            .withFilter(filters.groups) { fieldOptions.groupId.inList(it) }
            .withFilter(filters.releasedByStaffIds) { fieldOptions.releasedByStaffId.inList(it) }
            .withFilter(filters.caseIds) { fieldOptions.caseId.inList(it) }
            .withFilter(filters.appointmentIds) { fieldOptions.appointmentId.inList(it) }
            .withFilter(filters.createdAtLess) { fieldOptions.createdAt.lessEq(it) }
            .withFilter(filters.dueDateGreater) { fieldOptions.dueDate.greaterEq(it) }
            .withFilter(filters.releasedAtGreater) { fieldOptions.releasedAt.greaterEq(it) }
            .withFilter(filters.medicalSpecialtyIds) { fieldOptions.medicalSpecialtyIds.inList(it) }!!
    }

    private fun HealthPlanTaskFilters.isValid(): Unit =
        if (this.statuses.isNotNullOrEmpty()) Unit
        else if (this.types.isNotNullOrEmpty()) Unit
        else if (this.groups.isNotNullOrEmpty()) Unit
        else if (this.releasedByStaffIds.isNotNullOrEmpty()) Unit
        else if (this.caseIds.isNotNullOrEmpty()) Unit
        else if (this.appointmentIds.isNotNullOrEmpty()) Unit
        else if (this.createdAtLess != null) Unit
        else if (this.dueDateGreater != null) Unit
        else if (this.releasedAtGreater != null) Unit
        else throw InvalidArgumentException(code = "empty_filters", message = "Filters cannot be empty")

    private fun HealthPlanTask.setDate() =
        if (shouldUseNewCalculationDeadlineFlow() || this.isValidDateToUseNewDeadlineFlow())
            this.fillDateUsingNewDeadlineInterval()
        else
            this.fillDate()

    private fun shouldUseNewCalculationDeadlineFlow() =
        FeatureService.get(
            namespace = FeatureNamespace.HEALTH_PLAN,
            key = "should_use_new_health_plan_task_deadline_calculation_flow",
            defaultValue = false
        )

    private fun HealthPlanTask.isValidDateToUseNewDeadlineFlow() =
        createdAt.toLocalDate().isAfterEq(newCalculationDeadlineDate())

    private fun newCalculationDeadlineDate() =
        FeatureService.get(
            namespace = FeatureNamespace.HEALTH_PLAN,
            key = "new_health_plan_task_deadline_calculation_flow_base_date",
            defaultValue = "2030-01-01"
        ).let { LocalDate.parse(it) }

    private fun shouldCreateReferralDocument(staffId: UUID) =
        FeatureService.getList(
            namespace = FeatureNamespace.HEALTH_PLAN,
            key = "should_create_referral_pdf_document_staff_ids",
            defaultValue = emptyList<UUID>()
        ).contains(staffId)
}
