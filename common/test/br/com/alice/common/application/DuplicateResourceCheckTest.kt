package br.com.alice.common.application

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class DuplicateResourceCheckTest {

    @Test
    fun `should not throw exception when no duplicate resources exist`() {
        // This test verifies that the function completes without throwing an exception
        // when there are no duplicate resources in the classpath

        // Using a very specific extension that's unlikely to have duplicates
        val checker = DuplicateResourceChecker(
            resourceExtensions = listOf("nonexistent-extension"),
            excludePatterns = emptyList()
        )
        checker.checkForDuplicates()

        // If we reach this point, no exception was thrown
        assertTrue(true, "No exception should be thrown when no duplicates exist")
    }

    @Test
    fun `should exclude resources based on patterns`() {
        // This test verifies that the exclude patterns work correctly
        // We exclude version.properties which are commonly duplicated in dependencies

        val checker = DuplicateResourceChecker(
            resourceExtensions = listOf("properties", "xml"),
            excludePatterns = listOf(
                Regex(".*META-INF.*"),
                <PERSON><PERSON>(".*MANIFEST.*"),
                <PERSON><PERSON>(".*module-info.*"),
                <PERSON><PERSON>(".*version\\.properties.*"),  // Exclude version.properties files
                Regex(".*version\\.properties")     // Also match exact filename
            )
        )
        checker.checkForDuplicates()

        // If we reach this point, the exclusion patterns worked correctly
        assertTrue(true, "Exclusion patterns should prevent duplicate detection in excluded paths")
    }

    @Test
    fun `should throw DuplicateResourceException when duplicates are found`() {
        // This test is more complex as it would require setting up actual duplicate resources
        // For now, we'll test the exception type and structure

        val resourceName = "test.properties"
        val locations = listOf("jar1!/test.properties", "jar2!/test.properties")

        val exception = DuplicateResourceException(resourceName, locations)

        assertEquals(resourceName, exception.resourceName)
        assertEquals(locations, exception.locations)
        assertTrue(exception.message?.contains(resourceName) ?: false)
        assertTrue(exception.message?.contains("jar1") ?: false)
        assertTrue(exception.message?.contains("jar2") ?: false)
    }
}
