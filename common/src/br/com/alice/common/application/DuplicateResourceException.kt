package br.com.alice.common.application

import br.com.alice.common.core.exceptions.DuplicatedItemException

/**
 * Exception thrown when duplicate resources with the same name are found in the classpath.
 * This typically indicates conflicting dependencies that could lead to unpredictable behavior.
 */
data class DuplicateResourceException(
    val resourceName: String,
    val locations: List<String>,
    override val message: String = "Duplicate resource '$resourceName' found in classpath at locations: ${locations.joinToString(", ")}"
) : RuntimeException(message)
