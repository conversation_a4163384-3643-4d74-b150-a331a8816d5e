package br.com.alice.common.application

import br.com.alice.common.logging.logger
import java.io.File

/**
 * Utility class for checking duplicate resources in the classpath.
 * This helps detect conflicting dependencies that could lead to unpredictable behavior.
 */
class DuplicateResourceChecker(
    private val resourceExtensions: List<String> = DEFAULT_RESOURCE_EXTENSIONS,
    private val excludePatterns: List<Regex> = DEFAULT_EXCLUDE_PATTERNS
) {

    companion object {
        val DEFAULT_RESOURCE_EXTENSIONS = listOf("properties", "conf", "yaml", "yml", "xml")
        val DEFAULT_EXCLUDE_PATTERNS = listOf(
            Regex(".*META-INF.*"),
            <PERSON><PERSON>(".*module-info.*"),
            <PERSON><PERSON>(".*MANIFEST.*"),
            <PERSON><PERSON>(".*LICENSE.*"),
            <PERSON><PERSON>(".*NOTICE.*"),
            <PERSON><PERSON>(".*README.*")
        )
    }

    /**
     * Checks for duplicate resources in the classpath and throws DuplicateResourceException if any are found.
     *
     * @throws DuplicateResourceException if duplicate resources are found
     */
    fun checkForDuplicates() {
        logger.info("Starting duplicate resource check for extensions: ${resourceExtensions.joinToString(", ")}")

        val classLoader = Thread.currentThread().contextClassLoader

        // Strategy 1: Direct resource enumeration (more reliable for finding duplicates)
        // This approach directly asks the ClassLoader for specific resource names
        val resourceMap = scanForKnownResourcePatterns(classLoader)

        // Check for duplicates and throw exception if found
        val duplicates = resourceMap.filter { it.value.size > 1 }
        if (duplicates.isNotEmpty()) {
            duplicates.forEach { (resourceName, locations) ->
                logger.error("Duplicate resource found: $resourceName at locations: ${locations.joinToString(", ")}")
            }

            // Throw exception for the first duplicate found
            val firstDuplicate = duplicates.entries.first()
            throw DuplicateResourceException(firstDuplicate.key, firstDuplicate.value)
        }

        logger.info("Duplicate resource check completed successfully. Scanned ${resourceMap.size} unique resources.")
    }

    /**
     * Scans for known resource patterns using direct ClassLoader enumeration.
     * This is more reliable than URL scanning for detecting duplicates.
     */
    private fun scanForKnownResourcePatterns(
        classLoader: ClassLoader
    ): Map<String, List<String>> {
        // Common resource names that are likely to be duplicated
        val commonResourceNames = listOf(
            "application.properties",
            "application.conf",
            "application.commons.conf",
            "logback-spring.xml",
            "config.properties",
            "database.properties",
            "messages.properties"
        )

        val resourceMap = mutableMapOf<String, List<String>>()

        // Try to find resources by pattern
        commonResourceNames.filter { !shouldExcludeResource(it) }.forEach { resourceName ->
            try {
                val resources = classLoader.getResources(resourceName)
                val locations = mutableListOf<String>()

                while (resources.hasMoreElements()) {
                    val resource = resources.nextElement()
                    locations.add(resource.toString())
                }

                if (locations.isNotEmpty()) {
                    val fileName = resourceName.substringAfterLast('/')
                    val extension = fileName.substringAfterLast('.', "").lowercase()

                    if (extension in resourceExtensions) {
                        resourceMap[fileName] = locations
                        logger.debug("Found resource '$fileName' at ${locations.size} location(s): ${locations.joinToString(", ")}")
                    }
                }
            } catch (e: Exception) {
                logger.debug("Failed to enumerate resource: $resourceName", e)
            }
        }
        return resourceMap
    }

    /**
     * Checks if a resource should be excluded based on the exclude patterns.
     */
    private fun shouldExcludeResource(resourcePath: String): Boolean {
        return excludePatterns.any { pattern -> pattern.matches(resourcePath) }
    }
}
